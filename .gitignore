# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
cat_recognition_env/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# 数据集和模型文件
data/
datasets/
*.pth
*.pt
*.h5
*.ckpt
*.pb
*.onnx
weights/
checkpoints/
pretrained/

# 日志和临时文件
logs/
*.log
temp/
tmp/

# 测试相关
.coverage
htmlcov/
.pytest_cache/
.tox/
coverage.xml
*.cover

# 可能包含敏感信息的配置文件
config.ini
secrets.yaml
.env

# 实验结果和可视化
results/
output/*.pth
output/metadata/*.json
output/data
visualization/
output/best_feature_extractor.pth
output/final_feature_extractor.pth
output/metadata/train_meta.json
output/metadata/val_meta.json

# 系统文件
.DS_Store
Thumbs.db 
test