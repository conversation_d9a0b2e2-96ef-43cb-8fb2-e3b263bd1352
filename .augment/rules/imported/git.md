---
type: "always_apply"
---

{
  "git_management_rules": {
    "goal": "为LLM提供结构化上下文，使其精准识别代码变更意图并生成规范提交记录",
    
    "branch_naming_rule": {
      "description": "分支命名规范",
      "format": "type/issueId-short-description",
      "types": {
        "feat": "新功能开发",
        "fix": "修复问题",
        "refactor": "重构优化",
        "docs": "文档更新",
        "test": "单元/集成测试",
        "chore": "其他不影响功能的更新"
      },
      "examples": [
        "feat/1023-add-arcface-support",
        "fix/2312-resolve-mps-inference-bug"
      ]
    },
    
    "commit_message_rule": {
      "description": "提交信息生成规范",
      "format": "<type>(<scope>): <description>",
      "structure": [
        "标题行（必填，50字符内）",
        "正文段（必填，包含以下结构化要素）",
        "结尾段（必填，关联 issues）"
      ],
      "generation_guidelines": [
        {
          "element": "type",
          "mapping_rules": [
            "新增功能文件 → feat",
            "修改错误逻辑 → fix",
            "重构代码结构 → refactor",
            "修改README/docs → docs"
          ]
        },
        {
          "element": "scope",
          "mapping_rules": [
            "根据修改的主要文件确定范围",
            "多文件修改取核心模块名",
            "示例：修改model.py → model"
          ]
        },
        {
          "element": "description",
          "mapping_rules": [
            "使用现在时态动词开头",
            "包含具体修改对象",
            "示例：add arcface support"
          ]
        },
        {
          "element": "body",
          "required_fields": [
            "WHAT：具体修改内容（示例：新增ArcFace损失函数实现）",
            "WHY：修改原因（示例：解决人脸识别特征退化问题）",
            "HOW：关键技术点（可选，示例：通过增加角度边距计算）"
          ]
        }
      ],
      "examples": [
        "feat(model): add ArcFace + CircleLoss dynamic weighting\n\nWHAT: Implement adaptive weighting mechanism\nWHY: Improve feature discrimination in imbalanced datasets\nHOW: Use learnable parameters to balance losses\n\nCloses #1023",
        "fix(retrieval): resolve MPS compatibility issue\n\nWHAT: Fix device mismatch in similarity calculation\nWHY: Enable GPU acceleration on Apple Silicon\n\nRelated #2312"
      ]
    },
    
    "branching_strategy": {
      "description": "分支管理策略",
      "model": "Git Flow",
      "main_branches": {
        "main": "生产分支，仅存稳定可部署版本",
        "develop": "主开发分支，集成所有功能开发"
      },
      "special_branches": {
        "feature/*": "功能开发（从 develop 分出，合并回 develop）",
        "hotfix/*": "线上修复（从 main 分出，合并回 main + develop）",
        "release/*": "发布准备（从 develop 分出，合并回 main）"
      }
    },
    
    "pr_merge_workflow": {
      "description": "PR 合并流程规范",
      "requirements": [
        "经至少 1 位 Reviewer 审查通过",
        "CI 测试必须通过"
      ],
      "description_required": [
        "本次更改内容概述（需包含WHAT/WHY/HOW结构）",
        "涉及模块 / 子系统",
        "回归验证方法"
      ],
      "merge_methods": {
        "feature -> develop": "Squash & Merge",
        "release -> main": "Rebase & Merge"
      },
      "llm_auto_generate": {
        "prompt_template": "基于以下diff生成PR描述：\n{diff_content}\n\n要求：\n1. 按WHAT/WHY/HOW结构描述\n2. 标注影响模块\n3. 建议验证方法"
      }
    },
    
    "release_tagging_rule": {
      "description": "Tag 与 Release 管理",
      "tag_format": "v<major>.<minor>.<patch>",
      "example": "v1.2.3",
      "release_notes_template": {
        "date": "2025-05-08",
        "features": [
          "Add FAISS vector index loading from model output"
        ],
        "bug_fixes": [
          "Resolve index mismatch on inference"
        ],
        "compatibility": "Requires torch >= 2.1, faiss-cpu"
      }
    },
    
    "git_forbidden_practices": {
      "description": "禁止事项",
      "forbidden": [
        "不允许直接推送到 main 或 develop",
        "不允许提交无意义 Commit（如 update, fix, 123）",
        "禁止包含敏感信息（密码、token）提交入库",
        "禁止合并冲突未解决即提交 PR"
      ]
    },
    
    "llm_commit_enhancement": {
      "description": "模型语义增强协议",
      "code_change_annotation": {
        "required": "在代码关键修改处添加LLM可读注释",
        "format": "# LLM_CONTEXT: [WHAT] 修改内容 [WHY] 修改原因",
        "example": "# LLM_CONTEXT: [WHAT] Add boundary mining [WHY] Improve hard sample learning #184"
      },
      "change_mapping_matrix": {
        "file_patterns": {
          "model/.*\\.py": "scope=model",
          "trainer/.*": "scope=trainer",
          "tests/.*": "type=test"
        },
        "content_patterns": {
          "def train": "训练流程修改",
          "loss function": "损失函数调整"
        }
      }
    }
  }
}