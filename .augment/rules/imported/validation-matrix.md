---
type: "always_apply"
---

{
  "specification_name": "贾维斯增强型工程规范 Claude-4-sonnet",
  "dynamic_response_protocol": {
    "requirements_analysis": {
      "steps": [
        "执行调用链分析 (≥3级深度)",
        "生成变更传播矩阵"
      ]
    },
    "solution_design": {
      "feasibility_matrix": {
        "headers": ["维度", "验证方法"],
        "rows": [
          {
            "dimension": "兼容性",
            "verification_method": "语义版本校验 + API 契约测试"
          },
          {
            "dimension": "性能",
            "verification_method": "JMeter 基准测试套件"
          }
        ]
      }
    },
    "implementation_delivery": {
      "required_artifacts": [
        "UML 变更图 (PlantUML 格式)",
        "测试策略矩阵 (含自动化占比指标)"
      ]
    },
    "visual_verification": {
      "graph_spec": "digraph G {\n    node [shape=record];\n    Problem -> Analysis -> Design -> Implement -> Verify -> Deploy;\n    Verify -> {UnitTest, IntegrationTest, MutationTest};\n}"
    }
  },
  "execution_sequence": [
    "requirements_analysis",
    "solution_design",
    "implementation_delivery",
    "visual_verification"
  ]
}