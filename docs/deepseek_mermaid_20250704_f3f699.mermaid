graph TD
    %% ===== 主程序入口 =====
    A[main] -->|创建| B[TrainingOrchestrator]
    B -->|初始化| C[ConfigurationManager]
    C -->|获取配置| D[get_hyperparameters_config]
    C -->|获取配置| E[get_data_config]
    C -->|获取配置| F[get_environment_config]
    C -->|获取设备| G[get_device]
    
    %% ===== 种子和环境管理 =====
    B -->|导入| H[utils/deterministic_seed_manager.py]
    H -->|获取管理器| I[get_seed_manager]
    I -->|设置种子| J[set_global_seed]
    
    %% ===== 数据集管理 =====
    B -->|创建| K[DatasetManager]
    K -->|创建数据集| L[create_datasets]
    L -->|使用| M[feature/cat_dataset.py]
    L -->|应用变换| N[transform_train/transform_validation]
    
    %% ===== 样本选择器 =====
    B -->|创建| O[SampleSelector]
    O -->|创建采样器| P[create_adaptive_sample_selector]
    P -->|使用| Q[adaptive_sampling/sampler.py]
    Q -->|依赖| R[adaptive_sampling/strategies.py]
    Q -->|依赖| S[adaptive_sampling/callbacks.py]
    Q -->|依赖| T[adaptive_sampling/utils.py]
    
    %% ===== 特征提取管道 =====
    B -->|创建| U[FeatureExtractionPipeline]
    U -->|初始化| V[initialize_feature_extractor]
    V -->|创建| W[feature/feature_extractor.py]
    W -->|使用| X[feature/multi_scale_fusion.py]
    W -->|使用| Y[feature/feature_attention.py]
    W -->|使用| Z[feature/residual_block.py]
    
    %% ===== 全局调整协调器 =====
    B -->|创建| AA[GlobalAdjustmentCoordinator]
    
    %% ===== 损失计算引擎 =====
    B -->|创建| BB[LossComputationEngine]
    BB -->|初始化| CC[initialize_loss_function]
    CC -->|创建| DD[losses/multi_task_loss.py]
    DD -->|包含| EE[losses/center_loss.py]
    DD -->|包含| FF[losses/distance_utils.py]
    DD -->|使用| GG[miners/miner_manager.py]
    GG -->|使用| HH[miners/boundary_miner_utils.py]
    DD -->|注入协调器| AA
    GG -->|注入协调器| AA
    
    %% ===== 自适应权重控制 =====
    DD -->|使用| II[adaptive_weight_control/controller.py]
    II -->|使用| JJ[adaptive_weight_control/callbacks.py]
    II -->|使用| KK[adaptive_weight_control/normalizer.py]
    
    %% ===== 优化管理器 =====
    B -->|创建| LL[OptimizationManager]
    LL -->|初始化| MM[initialize_optimizer]
    LL -->|初始化| NN[initialize_meta_optimizer]
    LL -->|使用| OO[feature/meta_learning_optimizer.py]
    
    %% ===== 指标收集器 =====
    B -->|创建| PP[MetricsCollector]
    PP -->|初始化| QQ[initialize_metrics_calculator]
    QQ -->|创建| RR[feature/metrics_calculator.py]
    
    %% ===== 早停机制 =====
    B -->|创建| SS[feature/early_stopping.py]
    
    %% ===== 课程学习管理器 =====
    B -->|创建| TT[CurriculumLearningManager]
    TT -->|初始化| UU[initialize_curriculum_system]
    UU -->|使用| VV[curriculum_learning/scheduler.py]
    VV -->|使用| WW[curriculum_learning/estimator.py]
    VV -->|使用| XX[curriculum_learning/strategies.py]
    VV -->|使用| YY[curriculum_learning/callbacks.py]
    VV -->|使用| ZZ[curriculum_learning/sampler.py]
    
    %% ===== 模型持久化 =====
    B -->|创建| AAA[ModelPersistence]
    
    %% ===== 验证协调器 =====
    B -->|创建| BBB[ValidationOrchestrator]
    BBB -->|初始化| CCC[initialize_validation_engine]
    CCC -->|创建| DDD[feature/validation_engine.py]
    DDD -->|使用| RR
    DDD -->|使用| EEE[utils/parallel_manager.py]
    DDD -->|使用| FFF[utils/distance_calculator.py]
    
    %% ===== 主训练循环 =====
    B -->|执行| GGG[MainTrainingLoop]
    GGG -->|每轮调用| HHH[train_epoch]
    HHH -->|使用| III[feature/training_callbacks.py]
    HHH -->|计算损失| DD
    HHH -->|更新权重| II
    HHH -->|挖掘样本| GG
    
    %% ===== 验证流程 =====
    GGG -->|每轮调用| JJJ[ValidationOrchestrator.coordinate_validation]
    JJJ -->|调用| KKK[ValidationEngine.validate]
    KKK -->|使用| RR
    KKK -->|使用| HH
    
    %% ===== 课程学习更新 =====
    GGG -->|条件更新| LLL[CurriculumLearningManager.update_curriculum]
    LLL -->|调用| VV
    GGG -->|反馈| MMM[CurriculumLearningManager.receive_loss_feedback]
    
    %% ===== 元学习优化 =====
    GGG -->|条件优化| NNN[OptimizationManager.optimize_meta_parameters]
    NNN -->|使用| OO
    
    %% ===== 模型保存 =====
    GGG -->|每轮保存| OOO[ModelPersistence.save_epoch_model]
    GGG -->|早停检查| PPP[EarlyStopping.should_stop]
    
    %% ===== FAISS检索系统 =====
    QQQ[faiss_retrieval/app.py] -->|初始化| RRR[init_models]
    RRR -->|加载| SSS[faiss_retrieval/model_loader.py]
    SSS -->|使用| W
    RRR -->|加载| TTT[faiss_retrieval/faiss_indexer.py]
    QQQ -->|处理请求| UUU[search]
    UUU -->|特征提取| SSS
    UUU -->|检索| TTT
    
    %% ===== FAISS索引构建 =====
    VVV[faiss_retrieval/register_features.py] -->|构建索引| TTT
    VVV -->|使用| SSS
    VVV -->|批量处理| WWW[process_single_image]
    
    %% ===== 工具类支持 =====
    XXX[utils/log_utils.py] -->|日志支持| B
    YYY[utils/feature_norm.py] -->|特征归一化| W
    ZZZ[utils/path_setup.py] -->|路径管理| A
    
    %% ===== 配置系统 =====
    AAAA[config.py] -->|配置支持| C
    AAAA -->|提供配置| Q
    AAAA -->|提供配置| VV
    AAAA -->|提供配置| DDD