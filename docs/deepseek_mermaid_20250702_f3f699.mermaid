graph TD
    %% =====================================================
    %% 高精度个体识别训练系统架构图 v2.0
    %% 修复版本：消除循环依赖，明确职责边界，符合SOLID原则
    %% =====================================================
    
    %% ===== 应用协调层 (Application Orchestration Layer) =====
    subgraph "应用协调层"
        A[TrainingOrchestrator<br/>训练协调器]
        A1[ValidationOrchestrator<br/>验证协调器] 
        A2[ConfigurationManager<br/>配置管理器]
    end
    
    %% ===== 业务逻辑层 (Business Logic Layer) =====
    subgraph "业务逻辑层"
        B[AdaptiveSamplingManager<br/>自适应采样管理器]
        B1[CurriculumLearningManager<br/>课程学习管理器]
        B2[FeatureExtractionPipeline<br/>特征提取管道]
        B3[LossComputationEngine<br/>损失计算引擎]
        B4[OptimizationManager<br/>优化管理器]
        B5[ValidationEngine<br/>验证引擎]
    end
    
    %% ===== 数据处理层 (Data Processing Layer) =====
    subgraph "数据处理层"
        C[DatasetManager<br/>数据集管理器]
        C1[SampleSelector<br/>样本选择器] 
        C2[FeatureExtractor<br/>特征提取器]
        C3[MetricsCollector<br/>指标收集器]
        C4[ModelPersistence<br/>模型持久化]
    end
    
    %% ===== 算法组件层 (Algorithm Components Layer) =====
    subgraph "算法组件层"
        D[MultiScaleFusion<br/>多尺度融合]
        D1[FeatureAttention<br/>特征注意力]
        D2[MinerManager<br/>挖掘器管理器]
        D3[MultiTaskLoss<br/>多任务损失]
        D4[AdaptiveWeightController<br/>自适应权重控制器]
        D5[MetaLearningOptimizer<br/>元学习优化器]
        D6[EarlyStopping<br/>早停机制]
    end
    
    %% ===== 基础设施层 (Infrastructure Layer) =====
    subgraph "基础设施层"
        E[UtilityServices<br/>工具服务]
        E1[ParallelManager<br/>并行管理器]
        E2[LoggingService<br/>日志服务]
        E3[FAISSIndexer<br/>FAISS索引器]
        E4[ConfigService<br/>配置服务]
    end
    
    %% ===== 核心控制流 (Control Flow) =====
    A2 -->|配置注入| A
    A -->|协调训练| B
    A -->|协调训练| B1
    A -->|协调训练| B2
    A -->|协调训练| B3
    A -->|协调训练| B4
    A1 -->|协调验证| B5
    
    %% ===== 业务层内部交互 =====
    B1 -->|课程策略| B
    B -->|采样策略| C1
    B2 -->|特征管道| C2
    B3 -->|损失计算| D3
    B4 -->|优化控制| D5
    B5 -->|验证流程| C3
    
    %% ===== 数据层依赖 =====
    C -->|数据源| C1
    C1 -->|样本流| C2
    C2 -->|特征流| D
    C3 -->|指标存储| C4
    
    %% ===== 算法组件交互 =====
    C2 -->|基础特征| D
    D -->|融合特征| D1
    D1 -->|注意力特征| D2
    D2 -->|困难样本| D3
    D3 -->|损失值| D4
    D4 -->|权重分配| D3
    D5 -->|参数更新| C2
    D5 -->|参数更新| D
    D5 -->|参数更新| D1
    
    %% ===== 基础设施依赖 =====
    C2 -->|特征归一化| E
    D2 -->|距离计算| E
    C3 -->|索引构建| E3
    B5 -->|早停决策| D6
    
    %% ===== 并行协调 =====
    E1 -->|协调| C2
    E1 -->|协调| D3
    E1 -->|协调| B5
    
    %% ===== 日志记录 =====
    A -->|训练日志| E2
    B5 -->|验证日志| E2
    E2 -->|持久化| C4
    
    %% ===== 配置管理 =====
    E4 -->|配置供给| A2
    E4 -->|配置供给| B
    E4 -->|配置供给| B1
    E4 -->|配置供给| B2
    E4 -->|配置供给| B3
    
    %% ===== 验证反馈回路 =====
    B5 -->|性能反馈| B1
    D6 -->|早停信号| A
    C3 -->|指标反馈| B4
    
    %% ===== Web服务独立部署 =====
    subgraph "Web服务层"
        F[FAISSRetrievalService<br/>FAISS检索服务]
        F1[WebInterface<br/>Web界面]
    end
    
    F -->|模型加载| C4
    F -->|索引查询| E3
    F1 -->|用户接口| F
    
    %% ===== 数据流注释 =====
    classDef controlFlow fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dataFlow fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef infrastructure fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef algorithm fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef business fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    class A,A1,A2 controlFlow
    class B,B1,B2,B3,B4,B5 business
    class C,C1,C2,C3,C4 dataFlow
    class D,D1,D2,D3,D4,D5,D6 algorithm
    class E,E1,E2,E3,E4 infrastructure