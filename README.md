# 🚀 高精度个体识别系统 (High Precision Individual Identification)

<div align="center">

**基于深度学习的高精度开放集个体识别框架**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)](https://pytorch.org)
[![FAISS](https://img.shields.io/badge/FAISS-Vector--Search-green.svg)](https://github.com/facebookresearch/faiss)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

一个面向开放集识别任务的高度模块化、可配置的深度学习框架，专注于特征空间优化和Clean Architecture设计原则

</div>

---

## 🎯 项目概述

本项目实现了一套完整的**开放集个体识别系统**，采用Clean Architecture设计原则和先进的深度学习技术。系统能够处理训练集外的未知类别，具有强大的泛化能力和实际应用价值。

### 🌟 核心特色

- **🏗️ Clean Architecture设计**：严格的分层架构，职责分离，高内聚低耦合
- **🔬 开放集识别架构**：专注于特征空间优化，支持未见类别的识别
- **⚙️ 模块化配置系统**：700+行分层配置，支持精细化参数调整
- **🧠 智能训练策略**：课程学习、元学习、自适应权重控制三位一体
- **💎 多尺度特征融合**：语义+局部+全局特征的深度整合
- **🎯 困难样本挖掘**：三重挖掘策略+边界样本保护机制
- **📊 实时监控系统**：13种早停条件+多维度评估体系
- **🚀 高性能检索**：基于FAISS的向量检索系统+GPU加速

### 📈 性能指标

| 评估维度 | 优秀标准 | 良好标准 | 基线标准 |
|---------|---------|---------|---------|
| **验证准确率** | >95% | >90% | >85% |
| **特征分离比** | >4.0 | >2.5 | >1.5 |
| **Fisher判别分数** | >10.0 | >5.0 | >2.0 |
| **开集识别mAP** | >0.9 | >0.8 | >0.7 |
| **检索速度** | <5ms | <10ms | <20ms |

---

## 🏗️ 系统架构

### Clean Architecture分层设计

```mermaid
graph TB
    subgraph "🔧 应用协调层 (Application Orchestration)"
        A[TrainingOrchestrator<br/>训练协调器]
        A1[ValidationOrchestrator<br/>验证协调器] 
        A2[ConfigurationManager<br/>配置管理器]
    end
    
    subgraph "🧠 业务逻辑层 (Business Logic)"
        B[CurriculumLearningManager<br/>课程学习管理器]
        B1[FeatureExtractionPipeline<br/>特征提取管道]
        B2[LossComputationEngine<br/>损失计算引擎]
        B3[OptimizationManager<br/>优化管理器]
        B4[ValidationEngine<br/>验证引擎]
    end
    
    subgraph "📊 数据处理层 (Data Processing)"
        C[DatasetManager<br/>数据集管理器]
        C1[SampleSelector<br/>样本选择器] 
        C2[MetricsCollector<br/>指标收集器]
        C3[ModelPersistence<br/>模型持久化]
    end
    
    subgraph "🎯 算法组件层 (Algorithm Components)"
        D[FeatureExtractor<br/>特征提取器]
        D1[MultiScaleFusion<br/>多尺度融合]
        D2[MultiTaskLoss<br/>多任务损失]
        D3[AdaptiveWeightController<br/>自适应权重控制]
        D4[MinerManager<br/>挖掘器管理]
        D5[EarlyStopping<br/>早停机制]
    end
    
    subgraph "🚀 基础设施层 (Infrastructure)"
        E[FaissIndexer<br/>FAISS索引器]
        E1[ParallelManager<br/>并行管理器]
        E2[LoggingService<br/>日志服务]
        E3[DistanceCalculator<br/>距离计算器]
    end
    
    %% 依赖关系
    A --> B
    A1 --> B4
    A2 --> A
    B --> C
    B1 --> D
    B2 --> D2
    B3 --> D3
    B4 --> C2
    C --> D
    C1 --> D4
    D --> E
    D2 --> E3
    E --> E1
```

### 🎛️ 配置系统架构

```python
# Clean Architecture配置体系
├── 🎯 TrainConfig                    # 统一训练配置 (700+行)
│   ├── 基础训练参数                   # epochs, seed, embedding_dim
│   ├── 数据配置                      # batch_size, num_workers, paths
│   ├── 网络架构参数                   # backbone, attention, fusion
│   ├── 损失函数配置                   # Circle Loss + Sub-center ArcFace
│   ├── 采样器配置                     # adaptive sampling, curriculum
│   ├── 元学习配置                     # meta optimization
│   └── 早停配置                      # 13种早停条件
├── 🔍 ValConfig                      # 验证配置
└── 🛑 StoppingConfig                 # 早停配置
```

---

## 📁 项目结构

```
High_Precision_Individual_Identification/
├── 📂 src/                          # 源代码目录
│   ├── 🔧 config.py                 # 统一配置系统 (727行)
│   ├── 🚀 train.py                  # 主训练脚本 (1200+行)
│   ├── 📂 feature/                  # 核心特征学习模块
│   │   ├── 🎯 feature_extractor.py  # 特征提取器 (500+行)
│   │   ├── 🔗 multi_scale_fusion.py # 多尺度特征融合 (400+行)
│   │   ├── 💡 feature_attention.py  # 特征注意力机制
│   │   ├── 📂 losses/               # 损失函数模块
│   │   │   ├── multi_task_loss.py   # 多任务损失整合 (500+行)
│   │   │   └── subcenter_arcface_loss.py # Sub-center ArcFace损失
│   │   ├── 📂 miners/               # 困难样本挖掘
│   │   │   ├── miner_manager.py     # 挖掘器管理器 (700+行)
│   │   │   └── boundary_miner_utils.py # 边界样本工具
│   │   ├── 📂 adaptive_weight_control/ # 自适应权重控制
│   │   │   ├── controller.py        # 权重控制器
│   │   │   ├── callbacks.py         # 回调机制
│   │   │   └── normalizer.py        # 权重归一化
│   │   ├── 📂 adaptive_sampling/    # 自适应批次采样
│   │   │   ├── sampler.py          # 主采样器
│   │   │   ├── strategies.py       # 采样策略
│   │   │   └── callbacks.py        # 采样回调
│   │   ├── 📂 curriculum_learning/  # 课程学习系统
│   │   │   ├── scheduler.py        # 课程调度器
│   │   │   ├── estimator.py        # 难度评估器
│   │   │   ├── strategies.py       # 课程策略
│   │   │   └── factory.py          # 系统工厂
│   │   ├── 🛑 early_stopping.py     # 智能早停机制 (1600+行)
│   │   ├── 🔍 validation_engine.py  # 验证引擎 (700+行)
│   │   ├── 📊 metrics_calculator.py # 指标计算器 (800+行)
│   │   ├── 🧠 meta_learning_optimizer.py # 元学习优化器
│   │   └── 📋 training_callbacks.py # 训练回调机制
│   ├── 📂 faiss_retrieval/          # FAISS检索系统
│   │   ├── faiss_indexer.py         # 向量索引器 (300+行)
│   │   ├── model_loader.py          # 模型加载器
│   │   ├── app.py                   # Web应用接口
│   │   ├── run.py                   # 命令行入口
│   │   └── templates/index.html     # Web界面
│   ├── 📂 utils/                    # 工具模块
│   │   ├── distance_calculator.py   # 距离计算统一接口
│   │   ├── feature_norm.py          # 特征归一化
│   │   ├── log_utils.py             # 日志工具
│   │   └── parallel_manager.py      # 并行处理管理
│   └── 📂 datasets/                 # 数据集相关
├── 📂 output/                       # 输出目录
│   ├── logs/                        # 训练日志
│   ├── metadata/                    # 数据元信息
│   └── model_epoch_*.pth            # 训练模型
├── 📂 docs/                         # 文档目录
└── 📋 README.md                     # 项目文档
```

---

## 🧠 核心模块详解

### 1. 🎯 特征提取器 (FeatureExtractor)

**Clean Architecture设计**: 单一职责，依赖注入，回调机制

```python
class FeatureExtractor(nn.Module):
    """
    特征提取器 - 算法组件层
    
    🔥 核心创新：
    - 智能预训练权重加载：HuggingFace → 本地缓存 → 无预训练的三级回退
    - 完整的回调机制：支持特征提取过程的全流程监控
    - 职责分离设计：专注训练时特征提取，推理功能独立
    - 多尺度特征融合：语义、局部、全局三分支架构
    
    📊 输出规格：
    - 特征维度：512维 L2归一化向量
    - 处理能力：支持批量处理，GPU/MPS/CPU自适应
    - 回调支持：6个关键节点的实时监控
    """
```

**架构流程**:
1. **智能骨干网络加载**: EfficientNet-B2 + 多级回退机制
2. **特征注意力机制**: 通道注意力+空间注意力的并行融合
3. **多尺度融合**: 语义分支(全连接) + 局部分支(卷积) + 全局分支(统计池化)
4. **L2归一化输出**: 确保余弦距离计算的稳定性

### 2. 🔗 多尺度特征融合 (MultiScaleFusion)

**三分支协同架构**：

```python
# 🌟 三分支特征融合策略
语义分支 (Semantics): 直接线性映射 → 提取高层语义特征
局部分支 (Local):     多尺度卷积(3×3,5×5,7×7) → 捕获局部细节模式  
全局分支 (Global):    全局统计池化 → 提取高阶统计特征

# 🚀 融合增强机制
CrossPathAttention:     跨分支注意力交互
AdaptiveWeightLearning: 基于统计特征的自适应权重学习
GatedFusion:           门控机制控制信息流
```

### 3. 💫 多任务损失函数 (MultiTaskLoss)

**简化的双损失协同优化**：

| 损失函数 | 作用机制 | 权重范围 | 优化目标 | 核心参数 |
|---------|---------|---------|---------|---------|
| **Circle Loss** | 统一深度特征学习范式，灵活的相似性优化 | 0.60-0.80 | 判别性特征学习 | m=0.35, γ=320 |  
| **Sub-center ArcFace** | 集成类内紧凑性和边距控制的综合损失 | 0.20-0.40 | 类内收敛+类间分离 | margin=0.5, sub_centers=3 |

**智能权重控制**：
```python
# 🎯 损失模式
balanced:      # 平衡模式 - Circle Loss主导，Sub-center ArcFace辅助

# 🔥 权重自适应调整
AdaptiveWeightController: 根据训练状态动态调整损失权重
BoundaryMonitor:         边界样本数量监控与保护
WeightNormalizer:        权重归一化确保和为1.0
```

### 4. ⛏️ 困难样本挖掘系统

**三重挖掘策略**：

```python
# 🎯 挖掘器组合 (权重可配置)
BatchHardMiner:         # 批内最困难样本 - 基础困难样本挖掘
  ├─ 权重范围: 0.25-0.35
  └─ 策略: 选择批内距离最大的negative和距离最小的positive

MultiSimilarityMiner:   # 多相似度挖掘 - 复杂相似性关系建模
  ├─ 权重范围: 0.25-0.40  
  ├─ 参数: epsilon=0.05
  └─ 策略: 基于多个相似度阈值进行挖掘

PairMarginMiner:        # 边界样本挖掘 - 决策边界附近样本
  ├─ 权重范围: 0.35-0.40
  ├─ 边界阈值: [0.20, 0.80] (相似度范围)
  └─ 策略: 挖掘相似度在边界范围内的样本对
```

### 5. 🎲 自适应批次采样 (AdaptiveSampling)

**智能采样策略**：

```python
# 📊 采样参数配置
SamplerConfig:
  ├─ initial_m: 8          # 普通类别每批样本数
  ├─ max_m: 16            # 困难类别最大样本数
  ├─ hard_class_ratio: 0.45 # 困难类别占比
  ├─ max_duplicate_ratio: 0.2 # 最大重复率控制(<20%)
  └─ sampling_mode: "adaptive" # 采样模式

# 🧠 难度评估维度
DifficultyMetrics:
  ├─ 特征分离度: 计算类别内特征的紧凑性
  ├─ 平均损失值: 统计类别样本的损失分布
  ├─ 分类困难度: 基于模型预测置信度
  └─ 动态更新: 训练过程中实时调整难度评估
```

### 6. 📚 课程学习系统 (CurriculumLearningManager)

**业务逻辑层组件**：

```python
class CurriculumLearningManager:
    """课程学习管理器 - 业务逻辑层组件"""
    
    def __init__(self):
        self.curriculum_scheduler = None
        self.curriculum_sampler = None
        self.curriculum_monitor = None
        
    def initialize_curriculum_system(self):
        """初始化课程学习系统"""
        self.curriculum_scheduler, self.curriculum_sampler, self.curriculum_monitor = \
            create_curriculum_learning_system(enable_monitoring=True)
```

**渐进式学习策略**：

```python
# 🎓 课程参数设计
CurriculumScheduler:
  ├─ initial_easy_ratio: 0.7    # 初始简单样本比例(70%)
  ├─ final_easy_ratio: 0.3      # 最终简单样本比例(30%)
  ├─ curriculum_speed: 0.02     # 课程进展速度
  ├─ stage_epochs: [30,60,90]   # 多阶段轮次划分
  └─ difficulty_metric: "loss_based" # 难度评估指标
```

### 7. 🧠 元学习超参数优化 (MetaLearningOptimizer)

**自动化超参数调优**：

```python
# 🔍 搜索空间定义
HyperparameterSpace:
  ├─ lr_search_range: (1e-6, 1e-3)      # 学习率搜索范围
  ├─ weight_decay_range: (1e-6, 1e-3)   # 权重衰减范围
  ├─ batch_size_options: [32,48,64,80,96] # 批次大小选项
  ├─ loss_weights_range: 动态权重搜索空间
  └─ dropout_range: (0.1, 0.5)          # Dropout比率范围

# 🚀 优化算法
MetaOptimizer:
  ├─ 算法类型: AdamW + 自适应步长
  ├─ 探索率: 0.1 (逐步衰减至0.01)
  ├─ 更新频率: 每3个epoch执行一次优化
  └─ 收敛检测: 连续10次无改进自动停止
```

### 8. 🛑 智能早停机制 (EarlyStopping)

**13种早停条件**：

```python
# 🎯 核心早停条件
PrimaryConditions:
  1. NO_IMPROVEMENT          # 连续无显著改进
  2. LOSS_INCREASE           # 验证损失持续上升  
  3. LOW_BOUNDARY_SAMPLES    # 边界样本持续低于阈值
  4. HIGH_STAGNATION        # 训练停滞无实质进展
  5. NEGATIVE_TREND         # 检测到连续负面趋势

# 🔬 高级早停条件  
AdvancedConditions:
  6. STABLE_VAL_BOUNDARY     # 验证集边界样本稳定
  7. SEPARATION_STAGNATION   # 特征分离比停滞
  8. WEIGHT_STABILITY        # 损失权重已稳定
  10. GENERALIZATION_DECLINE # 泛化能力下降

# 🤖 智能检测条件
IntelligentConditions:  
  11. OVERFITTING_DETECTION  # 过拟合智能检测
  12. PLATEAU_DETECTION     # 性能平台期检测
  13. MARKOV_STABLE         # 马尔可夫状态稳定
```

### 9. 🔍 验证引擎 (ValidationEngine)

**业务逻辑层核心组件**：

```python
class ValidationEngine:
    """
    开集验证引擎，职责分离，包含指标计算、状态管理、特征分析
    遵循开发规范：方法≤25行，文件方法≤15个，嵌套≤2层，单一职责
    """
    
    def __init__(self, device: torch.device, config: ValConfig = None, 
                 metrics_calculator=None):
        """初始化开集验证引擎"""
        self.device = device
        self.config = config or DEFAULT_VAL_CONFIG
        self.shared_metrics_calculator = metrics_calculator  # 依赖注入
        self.parallel_manager = ParallelManager()  # 并行处理
```

### 10. 📋 训练回调机制 (TrainingCallbacks)

**标准化回调系统**：

```python
class TrainingCallback(ABC):
    """训练回调接口"""
    
    @abstractmethod
    def on_epoch_start(self, state: TrainingState) -> None: pass
    
    @abstractmethod
    def on_batch_start(self, state: TrainingState) -> None: pass
    
    @abstractmethod
    def on_batch_end(self, state: TrainingState) -> None: pass
    
    @abstractmethod
    def on_epoch_end(self, state: TrainingState) -> None: pass

# 实现类
├─ ProgressMonitorCallback     # 进度监控
├─ BoundaryProtectionCallback  # 边界样本保护
└─ CallbackManager            # 回调管理器
```

### 11. 🚀 FAISS检索系统

**高性能向量检索**：

```python
# ⚡ FAISS配置
FaissIndexer:
  ├─ 索引类型: IndexFlatIP (内积索引，等价于余弦相似度)
  ├─ GPU加速: 自动检测CUDA/MPS可用性
  ├─ 特征维度: 512维归一化向量
  ├─ 检索速度: <5ms (GPU) / <10ms (CPU)
  └─ 扩展性: 支持动态添加、删除向量

# 🔍 检索功能
SearchCapabilities:
  ├─ Top-k检索: 可配置返回数量
  ├─ 投票机制: 基于相似度的类别投票
  ├─ 批量检索: 支持批量查询优化
  ├─ 元数据管理: 图像路径、类别ID映射
  └─ Web接口: Flask应用提供RESTful API
```

**命令行接口**：

```bash
# 注册特征向量
python -m src.faiss_retrieval.run register \
  --img_folder datasets/train \
  --model_path output/model_epoch_14.pth \
  --limited --device mps

# 启动检索服务
python -m src.faiss_retrieval.run serve \
  --model_path output/model_epoch_14.pth \
  --index_path index/faiss_cat_index \
  --port 5000
```

---

## 🚀 快速开始

### 环境要求

- **Python**: 3.8+
- **PyTorch**: 2.0+
- **CUDA**: 11.8+ (可选，GPU加速)
- **内存**: 16GB+ (推荐)
- **存储**: 20GB+ 可用空间

### 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-repo/High_Precision_Individual_Identification.git
cd High_Precision_Individual_Identification

# 安装Python依赖
pip install -r requirements.txt

# 安装FAISS (CPU版本)
pip install faiss-cpu

# 安装FAISS (GPU版本，如果有CUDA)
pip install faiss-gpu
```

### 数据准备

```bash
# 创建数据目录结构
mkdir -p output/{logs,metadata}
mkdir -p datasets/train datasets/val

# 准备训练和验证数据集
# 数据格式要求：每个类别一个文件夹，图像文件支持jpg/png/bmp格式
datasets/
├── train/
│   ├── class1/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── class2/
│       ├── image1.jpg
│       └── image2.jpg
└── val/
    ├── class1/
    └── class2/
```

### 开始训练

```bash
# 使用默认配置开始训练
python src/train.py

# 多GPU训练
CUDA_VISIBLE_DEVICES=0,1 python src/train.py

# Apple Silicon (MPS) 训练
PYTORCH_ENABLE_MPS_FALLBACK=1 python src/train.py
```

### 启动检索服务

```bash
# 注册特征向量
python -m src.faiss_retrieval.run register \
  --img_folder datasets/train \
  --model_path output/model_epoch_best.pth \
  --output_index index/faiss_cat_index

# 启动Web服务
python -m src.faiss_retrieval.run serve \
  --model_path output/model_epoch_best.pth \
  --index_path index/faiss_cat_index \
  --port 5000

# 访问Web界面
# http://localhost:5000
```

---

## 📊 配置系统

### 核心配置类

```python
# 🎯 主要配置示例
from src.config import (
    DEFAULT_TRAIN_CONFIG,              # 训练总配置
    DEFAULT_VAL_CONFIG,                # 验证配置
    DEFAULT_STOPPING_CONFIG            # 早停配置
)

# 🔧 自定义配置
train_config = TrainConfig(
    # 基础训练参数
    total_training_epochs=50,
    batch_size=64,
    embedding_dim=512,
    
    # 损失函数配置
    circle_margin=0.35,
    circle_gamma=320,
    arcface_margin=0.5,
    arcface_scale=64.0,
    
    # 课程学习配置
    curriculum_enabled=True,
    curriculum_initial_easy_ratio=0.7,
    curriculum_final_easy_ratio=0.3
)
```

### 重要参数说明

| 参数类别 | 关键参数 | 默认值 | 说明 |
|---------|---------|-------|------|
| **特征提取** | embedding_dim | 512 | 特征向量维度 |
| | backbone_name | efficientnet_b2 | 骨干网络选择 |
| **损失函数** | circle_margin | 0.35 | Circle损失边界 |
| | arcface_margin | 0.5 | ArcFace损失边界 |
| **采样策略** | initial_m | 8 | 初始每类样本数 |
| | max_m | 16 | 困难类最大样本数 |
| | hard_class_ratio | 0.45 | 困难类比例 |
| **早停机制** | patience | 20 | 耐心值 |
| | delta | 0.003 | 最小改进阈值 |

---

## 🔧 高级功能

### 1. 课程学习配置

```python
# 🎓 课程学习设置
curriculum_config = TrainConfig(
    curriculum_enabled=True,
    curriculum_start_epoch=1,
    curriculum_initial_easy_ratio=0.7,
    curriculum_final_easy_ratio=0.3,
    curriculum_strategy="adaptive",
    difficulty_metric="loss_based"
)
```

### 2. 元学习优化

```python
# 🧠 元学习配置
meta_config = TrainConfig(
    enable_meta_learning=True,
    meta_learning_start_epoch=5,
    lr_search_range=(1e-6, 1e-3),
    meta_lr=0.01
)
```

### 3. 边界样本保护

```python
# ⛏️ 挖掘器配置
miner_config = TrainConfig(
    boundary_threshold=8,
    ms_adjustment_start_epoch=10,
    ms_ratio_low_threshold=0.3,
    ms_weight_boost_factor=1.1
)
```

### 4. 多线程优化

```python
# 🚀 并行优化配置
parallel_config = TrainConfig(
    num_workers=8,
    pin_memory=True,
    persistent_workers=True,
    prefetch_factor=4
)
```

---

## 📈 训练监控

### 日志系统

训练过程中，系统会输出详细的日志信息：

```bash
# 🎯 训练进度示例
[TRAIN] Epoch 15/50 - Loss: 0.234, Acc: 94.2%, Sep_Ratio: 3.45
[METRICS] Fisher: 8.2, Boundary: 15 samples (0.03%), Inter: 0.89, Intra: 0.26
[CURRICULUM] Stage 2/4, Easy_Ratio: 0.55, Difficulty_Level: 0.72
[EARLY_STOP] Patience: 5/20, Best_Epoch: 12, Conditions: [✓ Stable, ✗ Plateau]
[MINER] Hard: 0.30, MS: 0.35, Boundary: 0.35, Protection: Inactive
```

### 训练流程

```python
# 🔄 主训练流程 (Clean Architecture)
TrainingOrchestrator:
  ├─ 配置管理 (ConfigurationManager)
  ├─ 数据集管理 (DatasetManager)
  ├─ 特征提取管道 (FeatureExtractionPipeline)
  ├─ 损失计算引擎 (LossComputationEngine)
  ├─ 优化管理器 (OptimizationManager)
  ├─ 课程学习管理器 (CurriculumLearningManager)
  ├─ 训练循环
  │   ├─ 课程学习状态更新
  │   ├─ 自适应采样
  │   ├─ 前向传播
  │   ├─ 损失计算 (Circle Loss + Sub-center ArcFace)
  │   ├─ 反向传播
  │   └─ 权重更新
  ├─ 验证协调器 (ValidationOrchestrator)
  └─ 早停检查 (EarlyStopping)
```

---

## 🔬 性能评估

### 评估标准

| 指标类别 | 评估指标 | 计算公式 | 理想值 |
|---------|---------|---------|-------|
| **准确性** | 验证准确率 | correct/total | >95% |
| | Top-5准确率 | top5_correct/total | >98% |
| **特征质量** | 特征分离比 | inter_dist/intra_dist | >4.0 |
| | Fisher判别分数 | between_var/within_var | >10.0 |
| **开集性能** | mAP | ∑(precision×recall) | >0.9 |
| | MRR | ∑(1/rank) | >0.9 |

### Benchmark结果

```bash
# 🏆 性能基准 (EfficientNet-B2 + 多尺度融合)
Dataset: Custom Dataset (50 classes, 10k images)
├─ 验证准确率: 96.3% ± 0.5%
├─ 特征分离比: 4.12 ± 0.3
├─ Fisher分数: 11.4 ± 1.2  
├─ mAP@5: 0.943 ± 0.02
├─ 检索速度: 4.2ms (GPU) / 8.7ms (CPU)
└─ 模型大小: 28.4MB
```

---

## 🛠️ 故障排除

### 常见问题

**Q: 训练过程中损失不收敛？**
```bash
A: 检查以下配置：
   1. 学习率是否过大 (推荐: backbone_lr=8e-5)
   2. 批次大小是否合适 (推荐: 64)
   3. 损失权重是否平衡 (使用balanced模式)
   4. 是否启用课程学习 (early stage可能收敛慢)
```

**Q: 边界样本数量始终为0？**
```bash
A: 检查挖掘器配置：
   1. 确认使用PairMarginMiner
   2. 调整边界阈值范围 [0.20, 0.80]
   3. 检查特征归一化是否正确
   4. 验证余弦距离计算
```

**Q: FAISS检索速度慢？**
```bash
A: 优化建议：
   1. 启用GPU加速 (use_gpu=True)
   2. 检查CUDA/MPS环境配置
   3. 使用批量检索而非单个查询
   4. 考虑使用量化索引 (IndexIVFPQ)
```

---

## 🤝 贡献指南

### 开发环境设置

```bash
# 1. 设置开发环境
git clone https://github.com/your-repo/High_Precision_Individual_Identification.git
cd High_Precision_Individual_Identification
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 2. 安装开发依赖
pip install -r requirements.txt
pip install pre-commit
pre-commit install

# 3. 代码质量检查
black src/                 # 代码格式化
flake8 src/               # 语法检查
pytest tests/             # 运行测试
```

### 代码规范

本项目遵循严格的代码规范：

- **Clean Architecture**: 严格的分层架构，依赖注入
- **Python风格**: PEP 8 + Black格式化
- **文档规范**: Google风格docstring
- **类型注解**: 所有公共接口必须类型注解
- **测试覆盖**: 新功能必须包含单元测试
- **性能标准**: 方法体≤25行，文件≤15个方法

### 提交规范

```bash
# 🔀 Git提交规范
feat(model): add arcface loss support
fix(training): resolve MPS compatibility issue  
docs(readme): update installation guide
refactor(config): restructure validation settings
test(mining): add boundary miner unit tests
```

---

## 📜 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

---

## 🙏 致谢

感谢以下开源项目的支持：

- **[PyTorch](https://pytorch.org)**: 深度学习框架
- **[pytorch-metric-learning](https://github.com/KevinMusgrave/pytorch-metric-learning)**: 度量学习工具包
- **[FAISS](https://github.com/facebookresearch/faiss)**: 向量相似性搜索
- **[timm](https://github.com/rwightman/pytorch-image-models)**: 预训练模型库
- **[EfficientNet](https://github.com/lukemelas/EfficientNet-PyTorch)**: 高效网络架构

---

## 📞 联系方式

- **项目地址**: [GitHub Repository](https://github.com/your-repo/High_Precision_Individual_Identification)
- **问题反馈**: [GitHub Issues](https://github.com/your-repo/High_Precision_Individual_Identification/issues)
- **邮箱**: <EMAIL>

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

[![Stars](https://img.shields.io/github/stars/your-repo/High_Precision_Individual_Identification?style=social)](https://github.com/your-repo/High_Precision_Individual_Identification/stargazers)
[![Forks](https://img.shields.io/github/forks/your-repo/High_Precision_Individual_Identification?style=social)](https://github.com/your-repo/High_Precision_Individual_Identification/network/members)

</div>
