"""
优化器配置模块 - 负责优化器和学习率调度器的配置
从 train.py 迁移优化器配置相关功能，实现职责分离和模块化
"""

import torch
import math
from typing import List, Dict, Any, Optional, Callable
from utils import log_utils
from config import (
 
    DEFAULT_TRAIN_CONFIG, 
    TrainConfig
)


class OptimizerConfig:
    """
    优化器配置器 - 负责优化器和学习率调度器的配置
    
    职责：
    1. 参数分组配置
    2. 学习率策略设计
    3. 优化器创建和配置
    4. 学习率调度器管理
    """
    
    def __init__(self, 
                                   lr_config: Optional[TrainConfig] = None,
                  scheduler_config: Optional[TrainConfig] = None,
                 callback_config: Optional[TrainConfig] = None,
                                   log_config: Optional[TrainConfig] = None):
        """
        初始化优化器配置器
        
        Args:
            lr_config: 学习率配置
            scheduler_config: 调度器配置
            callback_config: 回调配置
            log_config: 日志配置
        """
        self.lr_config = lr_config or DEFAULT_TRAIN_CONFIG
        self.scheduler_config = scheduler_config or DEFAULT_TRAIN_CONFIG
        self.callback_config = callback_config or DEFAULT_TRAIN_CONFIG
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        
        self.config_history = []
        
    def configure_optimizer_params(self, model, 
                                 backbone_lr: Optional[float] = None, 
                                 head_lr: Optional[float] = None, 
                                 weight_decay: Optional[float] = None, 
                                 head_lr_ratio: Optional[float] = None, 
                                 max_ratio: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        配置优化器参数组，确保头部网络学习率与骨干网络学习率的比例合理

        Args:
            model: 模型实例
            backbone_lr: 骨干网络学习率，如果为None则使用配置文件默认值
            head_lr: 头部网络学习率，如果为None则通过head_lr_ratio计算
            weight_decay: 权重衰减系数，如果为None则使用配置文件默认值
            head_lr_ratio: 头部网络相对于骨干网络的学习率比例，如果为None则使用配置文件默认值
            max_ratio: 头部网络相对于骨干网络的最大学习率比例，如果为None则使用配置文件默认值

        Returns:
            param_groups: 参数分组列表
        """
        # 使用配置文件中的默认值
        backbone_lr = backbone_lr if backbone_lr is not None else self.lr_config.backbone_lr
        weight_decay = weight_decay if weight_decay is not None else self.lr_config.weight_decay
        head_lr_ratio = head_lr_ratio if head_lr_ratio is not None else self.lr_config.head_lr_ratio
        max_ratio = max_ratio if max_ratio is not None else self.lr_config.max_ratio
        
        # 按名称分组骨干网络和头部网络参数
        backbone_params = list(model.backbone.parameters())
        head_params = list(model.feature_attention.parameters()) + \
                     list(model.multi_scale_fusion.parameters()) + \
                     list(model.embedding.parameters())

        # 如果未指定头部学习率，则根据比例计算
        if head_lr is None:
            head_lr = backbone_lr * head_lr_ratio

        # 检查学习率比例是否超过上限
        current_ratio = head_lr / backbone_lr
        if current_ratio > max_ratio:
            if self.callback_config.enable_logging:
                log_utils.warning(f"学习率比例警告: 当前比例={current_ratio:.2f} 超过最大限制={max_ratio:.1f}", 
                                tag="OPTIMIZER_CONFIG")
            head_lr = backbone_lr * max_ratio

        # 重新计算最终比例
        final_ratio = head_lr / backbone_lr

        # 分组参数
        param_groups = [
            {'params': backbone_params, 'lr': backbone_lr, 'weight_decay': weight_decay},
            {'params': head_params, 'lr': head_lr, 'weight_decay': weight_decay}
        ]

        # 记录学习率配置
        if self.callback_config.enable_logging:
            log_utils.info(f"学习率配置: backbone_lr={backbone_lr:.6f}, head_lr={head_lr:.6f}, 比例={final_ratio:.2f}", 
                         tag="OPTIMIZER_CONFIG")
        
        # 保存配置历史
        if self.callback_config.save_config_history:
            config_info = {
                'backbone_lr': backbone_lr,
                'head_lr': head_lr,
                'weight_decay': weight_decay,
                'ratio': final_ratio,
                'max_ratio': max_ratio
            }
            self.config_history.append(config_info)
            
            # 限制历史记录长度
            if len(self.config_history) > self.callback_config.max_history_length:
                self.config_history = self.config_history[-self.callback_config.max_history_length:]

        return param_groups
    
    def create_warm_cosine_schedule_with_restart(self, 
                                               max_epochs: Optional[int] = None, 
                                            #    restart_epochs: Optional[List[int]] = None,
                                               warmup_epochs: Optional[int] = None,
                                               peak_lr_factor: Optional[float] = None,
                                               min_lr_factor: Optional[float] = None,
                                               decay_factor: Optional[float] = None) -> Callable[[int], float]:
        """
        创建带重启的余弦退火学习率调度器
        
        ⚠️ 注意：此方法已被注释，因为与在线课程学习和元学习优化器存在冲突
        根据贝叶斯分析，三个模块的功能重叠度达85%，存在以下冲突：
        1. 优化目标冲突：重启机制在固定点重置学习率，而元学习在动态调整超参数
        2. 策略冲突：课程学习在渐进调整难度，重启机制会破坏这种连续性
        3. 收敛干扰：多重调整机制可能导致训练不稳定
        
        建议：保留代码但暂时禁用，优先使用课程学习+元学习的组合
        
        Args:
            max_epochs: 最大训练轮数，如果为None则使用配置文件默认值
            restart_epochs: 重启点列表，如果为None则使用配置文件默认值
            warmup_epochs: 预热轮数，如果为None则使用配置文件默认值
            peak_lr_factor: 峰值学习率因子，如果为None则使用配置文件默认值
            min_lr_factor: 最小学习率因子，如果为None则使用配置文件默认值
            decay_factor: 衰减因子，如果为None则使用配置文件默认值
            
        Returns:
            学习率调度函数
        """
        # 使用配置文件中的默认值
        max_epochs = max_epochs if max_epochs is not None else self.scheduler_config.lr_schedule_total_epochs
        # 重启轮次配置（当前未使用）
        # restart_epochs = restart_epochs if restart_epochs is not None else [8, 16, 24, 32, 40]
        warmup_epochs = warmup_epochs if warmup_epochs is not None else self.scheduler_config.warmup_epochs
        peak_lr_factor = peak_lr_factor if peak_lr_factor is not None else self.scheduler_config.peak_lr_factor
        min_lr_factor = min_lr_factor if min_lr_factor is not None else self.scheduler_config.min_lr_factor
        # decay_factor = decay_factor if decay_factor is not None else self.scheduler_config.decay_factor
            
        # 替代方案：使用简单的余弦退火调度，避免重启冲突
        def simple_cosine_schedule(epoch_idx: int, batch: Optional[int] = None, 
                                 num_batches: Optional[int] = None) -> float:
            """简化的余弦退火调度器，与课程学习和元学习兼容"""
            # 预热期
            if epoch_idx < warmup_epochs:
                warmup_factor = float(epoch_idx) / float(max(1, warmup_epochs))
                return warmup_factor * peak_lr_factor
            
            # 余弦退火（无重启）
            cosine_epoch = epoch_idx - warmup_epochs
            cosine_total = max_epochs - warmup_epochs
            cosine_decay = 0.5 * (1 + math.cos(math.pi * cosine_epoch / cosine_total))
            
            return max(min_lr_factor, cosine_decay * peak_lr_factor)
        
        # 记录学习率调整策略变更
        if self.callback_config.enable_logging:
            log_utils.warning("学习率重启机制已禁用，使用简化余弦退火以避免与课程学习+元学习冲突", tag="OPTIMIZER_CONFIG")
            log_utils.info(f"应用简化学习率策略: 峰值因子{peak_lr_factor}, 最小因子{min_lr_factor}, 预热轮次{warmup_epochs}", tag="OPTIMIZER_CONFIG")
        
        return simple_cosine_schedule