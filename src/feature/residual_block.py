import torch.nn as nn


class ResidualBlock(nn.Module):
    """
    残差连接模块 - 用于特征嵌入层
    
    通过残差连接保留原始特征信息，同时增强特征表达能力。
    主分支包含两个全连接层和批归一化层，辅以PReLU激活函数。
    当输入输出维度不一致时，通过shortcut分支进行维度匹配。
    
    参数:
        in_features (int): 输入特征维度
        out_features (int): 输出特征维度
    """
    def __init__(self, in_features, out_features):
        super().__init__()
        # 主分支第一层: FC + BN + PReLU
        self.fc1 = nn.Linear(in_features, out_features)
        self.bn1 = nn.BatchNorm1d(out_features)
        self.prelu = nn.PReLU()
        
        # 主分支第二层: FC + BN
        self.fc2 = nn.Linear(out_features, out_features)
        self.bn2 = nn.BatchNorm1d(out_features)
        
        # shortcut分支: 维度不匹配时进行投影变换
        self.shortcut = nn.Sequential()
        if in_features != out_features:
            self.shortcut = nn.Sequential(
                nn.Linear(in_features, out_features),
                nn.BatchNorm1d(out_features)
            )
    
    def forward(self, x):
        """
        前向传播
        
        Args:
            x (Tensor): 输入特征 [batch_size, in_features]
            
        Returns:
            Tensor: 输出特征 [batch_size, out_features]
        """
        # 保存shortcut路径
        identity = self.shortcut(x)
        
        # 主分支前向计算
        out = self.fc1(x)
        out = self.bn1(out)
        out = self.prelu(out)
        
        out = self.fc2(out)
        out = self.bn2(out)
        
        # 残差连接
        out += identity
        out = self.prelu(out)
        
        return out