import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
import math
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from feature.residual_block import ResidualBlock
from feature.feature_attention import FeatureAttention
from feature.multi_scale_fusion import OptimizedMultiScaleFusion
from utils import log_utils
from utils.feature_norm import create_normalizer_from_config
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


# ==================== 回调机制 ====================

class FeatureCallback(ABC):
    """特征提取回调接口"""
    
    @abstractmethod
    def on_feature_extraction_start(self, extractor: 'FeatureExtractor', input_shape: Tuple[int, ...]):
        """特征提取开始时的回调"""
        pass
    
    @abstractmethod
    def on_backbone_features_extracted(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """骨干网络特征提取完成时的回调"""
        pass
    
    @abstractmethod
    def on_attention_applied(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """注意力机制应用完成时的回调"""
        pass
    
    @abstractmethod
    def on_fusion_completed(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """多尺度融合完成时的回调"""
        pass
    
    @abstractmethod
    def on_embedding_generated(self, extractor: 'FeatureExtractor', embeddings: torch.Tensor):
        """特征嵌入生成完成时的回调"""
        pass
    
    @abstractmethod
    def on_feature_extraction_complete(self, extractor: 'FeatureExtractor', final_features: torch.Tensor):
        """特征提取完全完成时的回调"""
        pass


class FeatureMonitorCallback(FeatureCallback):
    """特征提取过程监控回调"""
    
    def __init__(self, log_level: str = None, enable_analysis: bool = None, 
                 callback_config: Optional[TrainConfig] = None,
                 log_config: Optional[TrainConfig] = None):
        # 使用配置或默认值
        if callback_config is None:
            callback_config = DEFAULT_TRAIN_CONFIG
        if log_config is None:
            log_config = DEFAULT_TRAIN_CONFIG
            
        self.log_level = log_level or callback_config.monitor_log_level
        self.enable_analysis = enable_analysis if enable_analysis is not None else callback_config.enable_monitor_analysis
        self.log_config = log_config
        self.extraction_stats = {}
    
    def on_feature_extraction_start(self, extractor: 'FeatureExtractor', input_shape: Tuple[int, ...]):
        """记录特征提取开始"""
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 开始特征提取，输入形状: {input_shape}", tag="FEATURE_EXTRACTOR")
        self.extraction_stats['input_shape'] = input_shape
    
    def on_backbone_features_extracted(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """记录骨干网络特征"""
        if self.enable_analysis:
            self.extraction_stats['backbone_shape'] = features.shape
            self.extraction_stats['backbone_mean'] = features.mean().item()
            self.extraction_stats['backbone_std'] = features.std().item()
        
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 骨干网络特征提取完成，形状: {features.shape}", tag="FEATURE_EXTRACTOR")
    
    def on_attention_applied(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """记录注意力特征"""
        if self.enable_analysis:
            self.extraction_stats['attention_shape'] = features.shape
            self.extraction_stats['attention_mean'] = features.mean().item()
            self.extraction_stats['attention_std'] = features.std().item()
        
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 注意力机制应用完成，形状: {features.shape}", tag="FEATURE_EXTRACTOR")
    
    def on_fusion_completed(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """记录融合特征"""
        if self.enable_analysis:
            self.extraction_stats['fusion_shape'] = features.shape
            self.extraction_stats['fusion_mean'] = features.mean().item()
            self.extraction_stats['fusion_std'] = features.std().item()
        
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 多尺度融合完成，形状: {features.shape}", tag="FEATURE_EXTRACTOR")
    
    def on_embedding_generated(self, extractor: 'FeatureExtractor', embeddings: torch.Tensor):
        """记录嵌入特征"""
        if self.enable_analysis:
            self.extraction_stats['embedding_shape'] = embeddings.shape
            self.extraction_stats['embedding_mean'] = embeddings.mean().item()
            self.extraction_stats['embedding_std'] = embeddings.std().item()
            self.extraction_stats['embedding_norm'] = torch.norm(embeddings, p=2, dim=1).mean().item()
        
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 特征嵌入生成完成，形状: {embeddings.shape}", tag="FEATURE_EXTRACTOR")
    
    def on_feature_extraction_complete(self, extractor: 'FeatureExtractor', final_features: torch.Tensor):
        """记录最终特征"""
        if self.enable_analysis:
            self.extraction_stats['final_shape'] = final_features.shape
            self.extraction_stats['final_mean'] = final_features.mean().item()
            self.extraction_stats['final_std'] = final_features.std().item()
            self.extraction_stats['final_norm'] = torch.norm(final_features, p=2, dim=1).mean().item()
        
        if self.log_level == "debug":
            log_utils.debug(f"FeatureExtractor - 特征提取完全完成，最终形状: {final_features.shape}", tag="FEATURE_EXTRACTOR")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取特征提取统计信息"""
        return self.extraction_stats.copy()


class FeatureAnalysisCallback(FeatureCallback):
    """特征分析回调，用于收集详细的特征分析信息"""
    
    def __init__(self):
        self.analysis_data = {}
    
    def on_feature_extraction_start(self, extractor: 'FeatureExtractor', input_shape: Tuple[int, ...]):
        """初始化分析数据"""
        self.analysis_data = {'input_shape': input_shape}
    
    def on_backbone_features_extracted(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """收集骨干网络特征分析"""
        self.analysis_data['backbone_features'] = features.detach().clone()
    
    def on_attention_applied(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """收集注意力特征分析"""
        self.analysis_data['attention_features'] = features.detach().clone()
    
    def on_fusion_completed(self, extractor: 'FeatureExtractor', features: torch.Tensor):
        """收集融合特征分析"""
        self.analysis_data['fused_features'] = features.detach().clone()
    
    def on_embedding_generated(self, extractor: 'FeatureExtractor', embeddings: torch.Tensor):
        """收集嵌入特征分析"""
        self.analysis_data['embeddings'] = embeddings.detach().clone()
    
    def on_feature_extraction_complete(self, extractor: 'FeatureExtractor', final_features: torch.Tensor):
        """收集最终特征分析"""
        self.analysis_data['final_features'] = final_features.detach().clone()
    
    def get_analysis_data(self) -> Dict[str, Any]:
        """获取完整的特征分析数据"""
        return self.analysis_data.copy()


# ==================== 核心特征提取器 ====================

class FeatureExtractor(nn.Module):
    """
    增强版特征提取器 V2 - 重构版
    
    新增功能：
    1. 回调机制：支持特征提取过程的监控和扩展
    2. 职责分离：专注于训练时的特征提取，推理功能移至独立模块
    3. 向后兼容：保持原有API完全兼容
    4. 灵活配置：支持不同的配置模式
    """
    
    def __init__(self, config: Optional[TrainConfig] = None,
                 callback_config: Optional[TrainConfig] = None,
                 log_config: Optional[TrainConfig] = None,
                 callbacks: Optional[List[FeatureCallback]] = None,
                 embedding_dim: Optional[int] = None, 
                 backbone_name: Optional[str] = None, 
                 pretrained: Optional[bool] = None):
        """
        初始化特征提取器
        
        Args:
            config: 特征提取器配置
            callback_config: 回调配置
            log_config: 日志配置
            callbacks: 回调函数列表
            embedding_dim: 特征嵌入维度 (覆盖config中的值)
            backbone_name: 骨干网络名称 (覆盖config中的值)
            pretrained: 是否使用预训练权重 (覆盖config中的值)
        """
        super().__init__()
        self._init_configs(config, callback_config, log_config)
        self._init_params(embedding_dim, backbone_name, pretrained)
        
        self.callbacks = callbacks or []
        
        self._init_modules()
        
        self._log_initialization_info()

    def _init_configs(self, config, callback_config, log_config):
        """初始化配置"""
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.callback_config = callback_config or DEFAULT_TRAIN_CONFIG
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG

    def _init_params(self, embedding_dim, backbone_name, pretrained):
        """初始化向后兼容的参数"""
        self.embedding_dim = embedding_dim if embedding_dim is not None else self.config.embedding_dim
        self.backbone_name = backbone_name if backbone_name is not None else self.config.backbone_name
        self.pretrained_flag = pretrained if pretrained is not None else self.config.pretrained

    def _init_modules(self):
        """初始化所有子模块"""
        self.backbone = self._create_backbone_with_smart_loading(self.backbone_name, self.pretrained_flag)
        backbone_features = self.backbone.num_features

        self.feature_attention = FeatureAttention(backbone_features, reduction=self.config.attention_reduction)
        self.multi_scale_fusion = self._create_fusion_module(backbone_features)
        self.embedding = self._create_embedding_layers(backbone_features)
        self.normalizer = create_normalizer_from_config(self.config)

    def _create_fusion_module(self, backbone_features):
        """创建多尺度融合模块"""
        from feature.multi_scale_fusion import OptimizedMultiScaleFusion
        return OptimizedMultiScaleFusion(
            in_features=backbone_features,
            branch_dim=None,
            dropout_rate=self.config.fusion_dropout_rate,
            use_attention_fusion=self.config.use_attention_fusion,
            callbacks=self.callbacks,
            config=self.config
        )

    def _create_embedding_layers(self, in_features: int) -> nn.Sequential:
        """创建特征嵌入层"""
        embedding_layers = []
        current_dim = in_features
        
        for i, (hidden_dim, dropout_rate) in enumerate(zip(self.config.embedding_hidden_dims, self.config.embedding_dropout_rates)):
            embedding_layers.extend([
                ResidualBlock(current_dim, hidden_dim),
                nn.Dropout(dropout_rate),
            ])
            if i < len(self.config.embedding_hidden_dims) - 1:
                embedding_layers.append(nn.BatchNorm1d(hidden_dim))
            current_dim = hidden_dim
        
        embedding_layers.extend([
            ResidualBlock(current_dim, self.embedding_dim),
            nn.BatchNorm1d(self.embedding_dim, affine=True),
        ])
        
        return nn.Sequential(*embedding_layers)

    def _log_initialization_info(self):
        """记录初始化信息"""
        log_utils.info(f"🔧 特征提取器初始化完成:", tag="FEATURE_EXTRACTOR")
        log_utils.info(f"  - 骨干网络: {self.backbone_name}", tag="FEATURE_EXTRACTOR")
        log_utils.info(f"  - 预训练: {self.pretrained_flag}", tag="FEATURE_EXTRACTOR")
        log_utils.info(f"  - 嵌入维度: {self.embedding_dim}", tag="FEATURE_EXTRACTOR")
        log_utils.info(f"  - 注意力机制: {self.config.use_attention_fusion}", tag="FEATURE_EXTRACTOR")
        log_utils.info(f"  - 回调数量: {len(self.callbacks)}", tag="FEATURE_EXTRACTOR")

    def _create_backbone_with_smart_loading(self, backbone_name: str, pretrained: bool) -> nn.Module:
        """智能加载骨干网络，支持多种回退策略"""
        if not pretrained:
            return self._create_model_without_pretrained(backbone_name)

        model = self._try_load_from_hf(backbone_name)
        if model:
            return model
        
        model = self._try_load_from_local_cache(backbone_name)
        if model:
            return model
        
        return self._create_model_as_fallback(backbone_name)

    def _create_model_without_pretrained(self, backbone_name: str) -> nn.Module:
        """创建无预训练权重的模型"""
        model = timm.create_model(backbone_name, pretrained=False, num_classes=0)
        log_utils.info(f"创建无预训练权重模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
        return model

    def _try_load_from_hf(self, backbone_name: str) -> Optional[nn.Module]:
        """尝试从Hugging Face加载模型"""
        try:
            log_utils.info(f"尝试从Hugging Face下载预训练模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
            model = timm.create_model(backbone_name, pretrained=True, num_classes=0)
            log_utils.info(f"✅ 成功从Hugging Face加载预训练模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
            return model
        except Exception as hf_error:
            log_utils.error(f"⚠️ Hugging Face下载失败: {hf_error}", tag="FEATURE_EXTRACTOR")
            return None

    def _try_load_from_local_cache(self, backbone_name: str) -> Optional[nn.Module]:
        """尝试从本地缓存加载模型"""
        try:
            log_utils.info(f"尝试从torch hub本地缓存加载: {backbone_name}", tag="FEATURE_EXTRACTOR")
            import os
            original_offline = os.environ.get('HF_HUB_OFFLINE')
            os.environ['HF_HUB_OFFLINE'] = '1'
            
            model = None
            try:
                if backbone_name == 'efficientnet_b2':
                    model = self._load_efficientnet_b2_from_local(backbone_name)
                
                if not model:
                    model = timm.create_model(backbone_name, pretrained=True, num_classes=0)
                    log_utils.info(f"✅ 成功从本地缓存加载预训练模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
            finally:
                if original_offline is None:
                    os.environ.pop('HF_HUB_OFFLINE', None)
                else:
                    os.environ['HF_HUB_OFFLINE'] = original_offline
            return model
        except Exception as local_error:
            log_utils.error(f"⚠️ 本地缓存加载失败: {local_error}", tag="FEATURE_EXTRACTOR")
            return None

    def _load_efficientnet_b2_from_local(self, backbone_name: str) -> Optional[nn.Module]:
        """从本地文件加载efficientnet_b2的特殊逻辑"""
        from pathlib import Path
        torch_cache_dir = Path.home() / '.cache' / 'torch' / 'hub' / 'checkpoints'
        possible_files = ['efficientnet_b2_rwightman-c35c1473.pth', 'efficientnet-b2-8bb594d6.pth']
        
        for filename in possible_files:
            weight_file = torch_cache_dir / filename
            if weight_file.exists():
                log_utils.info(f"找到本地权重文件: {weight_file.name}", tag="FEATURE_EXTRACTOR")
                model = timm.create_model(backbone_name, pretrained=False, num_classes=0)
                try:
                    state_dict = torch.load(weight_file, map_location='cpu') # Use CPU to avoid device issues
                    filtered_dict = {k: v for k, v in state_dict.items() if not k.startswith('classifier')}
                    model.load_state_dict(filtered_dict, strict=False)
                    log_utils.info(f"✅ 成功从本地权重文件加载预训练模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
                    return model
                except Exception as load_error:
                    log_utils.error(f"本地权重文件加载失败: {load_error}", tag="FEATURE_EXTRACTOR")
        return None

    def _create_model_as_fallback(self, backbone_name: str) -> nn.Module:
        """加载失败时的回退策略"""
        try:
            log_utils.warning("回退到无预训练权重模式...", tag="FEATURE_EXTRACTOR")
            model = timm.create_model(backbone_name, pretrained=False, num_classes=0)
            log_utils.info(f"✅ 成功创建无预训练权重模型: {backbone_name}", tag="FEATURE_EXTRACTOR")
            return model
        except Exception as final_error:
            log_utils.error(f"❌ 模型创建完全失败: {final_error}", tag="FEATURE_EXTRACTOR")
            raise final_error

    def add_callback(self, callback: FeatureCallback) -> None:
        """添加特征提取回调"""
        self.callbacks.append(callback)
        log_utils.info(f"添加特征回调: {callback.__class__.__name__}", tag="FEATURE_EXTRACTOR")

    def remove_callback(self, callback: FeatureCallback) -> None:
        """移除特征提取回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            log_utils.info(f"移除特征回调: {callback.__class__.__name__}", tag="FEATURE_EXTRACTOR")

    def _trigger_callbacks(self, event_type: str, *args, **kwargs) -> None:
        """触发所有注册的回调"""
        event_map = {
            "start": "on_feature_extraction_start",
            "backbone": "on_backbone_features_extracted",
            "attention": "on_attention_applied",
            "fusion": "on_fusion_completed",
            "embedding": "on_embedding_generated",
            "complete": "on_feature_extraction_complete",
        }
        method_name = event_map.get(event_type)
        if not method_name:
            return

        for callback in self.callbacks:
            try:
                method = getattr(callback, method_name)
                method(self, *args, **kwargs)
            except Exception as e:
                log_utils.error(f"回调执行失败 {callback.__class__.__name__}: {e}", tag="FEATURE_EXTRACTOR")

    def _extract_pipeline(self, x: torch.Tensor) -> torch.Tensor:
        """统一的特征提取流水线"""
        self._trigger_callbacks("start", input_shape=x.shape)
        
        backbone_feat = self.backbone(x)
        self._trigger_callbacks("backbone", features=backbone_feat)

        attn_feat = self.feature_attention(backbone_feat)
        self._trigger_callbacks("attention", features=attn_feat)

        fused_feat = self.multi_scale_fusion(attn_feat)
        self._trigger_callbacks("fusion", features=fused_feat)

        feat = self.embedding(fused_feat)
        self._trigger_callbacks("embedding", embeddings=feat)

        feat = self.normalizer(feat)
        self._trigger_callbacks("complete", final_features=feat)

        return feat

    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取图像特征向量，专用于非训练场景"""
        with torch.no_grad():
            return self._extract_pipeline(x)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播，专用于训练"""
        return self._extract_pipeline(x)

    def get_feature_analysis(self, x: torch.Tensor) -> Dict[str, Any]:
        """获取特征提取过程中的详细分析信息"""
        with torch.no_grad():
            analysis_callback = FeatureAnalysisCallback()
            original_callbacks = self.callbacks.copy()
            self.callbacks.append(analysis_callback)
            
            try:
                return self._run_analysis_pipeline(x, analysis_callback)
            finally:
                self.callbacks = original_callbacks

    def _run_analysis_pipeline(self, x: torch.Tensor, analysis_callback: FeatureAnalysisCallback) -> Dict[str, Any]:
        """执行分析流水线并收集数据"""
        self._extract_pipeline(x)
        analysis_data = analysis_callback.get_analysis_data()
        self._add_fusion_analysis(analysis_data)
        return analysis_data

    def _add_fusion_analysis(self, analysis_data: Dict[str, Any]):
        """为分析数据添加多尺度融合的详细信息"""
        backbone_feat = analysis_data.get('backbone_features')
        attn_feat = analysis_data.get('attention_features')
        
        if backbone_feat is not None and attn_feat is not None:
            branch_outputs = self.multi_scale_fusion.get_branch_outputs(attn_feat)
            analysis_data['branch_outputs'] = branch_outputs
            analysis_data['adaptive_weights'] = branch_outputs['weights_summary']
