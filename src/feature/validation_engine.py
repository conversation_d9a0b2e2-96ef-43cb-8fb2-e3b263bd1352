"""
开集验证引擎模块，负责开集模型验证和评估，支持结构化Top-k指标和并行优化
"""

import sys
import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
from tqdm import tqdm
import numpy as np
import torch.multiprocessing as mp
import threading
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any
from concurrent.futures import as_completed
from functools import partial
from utils import log_utils, parallel_manager
from utils.feature_norm import l2_normalize, FeatureNormalizer
from config import DEFAULT_VAL_CONFIG, ValConfig
from feature.miners.boundary_miner_utils import BoundaryMinerUtils, BoundaryConfig, BoundaryResult



class ValidationMetricsCalculator:
    """验证指标计算器，负责评估指标计算"""
    
    def __init__(self, config: ValConfig):
        self.config = config
    
    def compute_structured_topk_metrics(self, similarities: torch.Tensor, true_labels: torch.Tensor, 
                                      predicted_labels: torch.Tensor, k_values: List[int] = [1, 3, 5]) -> Dict[str, float]:
        """计算结构化Top-k指标，合并循环提升效率"""
        metrics = {}
        n_queries = similarities.size(0)
        
        for k in k_values:
            if k > similarities.size(1):
                continue
                
            _, top_k_indices = similarities.topk(k, dim=1)
            top_k_correct = 0
            precision_sum = 0
            
            # 合并循环，同时计算准确率和精度
            for i in range(n_queries):
                true_label = true_labels[i].item()
                top_k_pred = predicted_labels[top_k_indices[i]]
                
                # 计算Top-k准确率
                if true_label in top_k_pred:
                    top_k_correct += 1
                
                # 计算Precision@k
                correct_in_topk = (top_k_pred == true_label).sum().item()
                precision_sum += correct_in_topk / k
            
            top_k_accuracy = top_k_correct / n_queries
            metrics[f'top{k}_accuracy'] = top_k_accuracy
            metrics[f'recall_at_{k}'] = top_k_accuracy
            metrics[f'precision_at_{k}'] = precision_sum / n_queries
        
        metrics['mean_average_precision'] = self._compute_map(similarities, true_labels, predicted_labels)
        metrics['mean_reciprocal_rank'] = self._compute_mrr(similarities, true_labels, predicted_labels)
        
        return metrics
    
    def _compute_map(self, similarities: torch.Tensor, true_labels: torch.Tensor, 
                    predicted_labels: torch.Tensor) -> float:
        """计算平均精度均值"""
        n_queries = similarities.size(0)
        ap_sum = 0.0
        
        for i in range(n_queries):
            true_label = true_labels[i].item()
            query_similarities = similarities[i]
            
            sorted_indices = torch.argsort(query_similarities, descending=True)
            sorted_labels = predicted_labels[sorted_indices]
            
            relevant_count = 0
            precision_sum = 0.0
            
            for rank, pred_label in enumerate(sorted_labels, 1):
                if pred_label.item() == true_label:
                    relevant_count += 1
                    precision_at_rank = relevant_count / rank
                    precision_sum += precision_at_rank
            
            if relevant_count > 0:
                ap = precision_sum / relevant_count
                ap_sum += ap
        
        return ap_sum / n_queries
    
    def _compute_mrr(self, similarities: torch.Tensor, true_labels: torch.Tensor, 
                    predicted_labels: torch.Tensor) -> float:
        """计算平均倒数排名"""
        n_queries = similarities.size(0)
        rr_sum = 0.0
        
        for i in range(n_queries):
            true_label = true_labels[i].item()
            query_similarities = similarities[i]
            
            sorted_indices = torch.argsort(query_similarities, descending=True)
            sorted_labels = predicted_labels[sorted_indices]
            
            for rank, pred_label in enumerate(sorted_labels, 1):
                if pred_label.item() == true_label:
                    rr_sum += 1.0 / rank
                    break
        
        return rr_sum / n_queries


class ValidationStateManager:
    """验证状态管理器，负责状态初始化和更新"""
    
    def __init__(self, config: ValConfig):
        self.config = config
        self._state_lock = threading.Lock()  # 状态锁，简化并发控制
    
    def initialize_open_set_state(self, log_confusion: bool, loader: DataLoader) -> Dict:
        """初始化开集验证状态"""
        total_samples = len(loader.dataset) if hasattr(loader.dataset, '__len__') else len(loader) * loader.batch_size
        
        state = {
            'total': 0,
            'all_embeddings': [],
            'all_labels': [],
            'class_data': defaultdict(list),
            'class_images': defaultdict(list),
            'class_paths': defaultdict(list),
            'boundary_samples_count': 0,
            'total_sample_pairs': 0,
            'val_boundary_similarities': [],
            'boundary_indices': [],
            'boundary_ratio_history': [],
            
            'realtime_stats': {
                'processed_samples': 0,
                'high_confidence_samples': 0,
                'boundary_samples': 0,
                'boundary_ratio': 0.0,
                'boundary_sample_pairs': 0,
                'class_distribution': defaultdict(int)
            }
        }
        
        if log_confusion:
            state['confusion_samples'] = {
                'images': [], 'true_labels': [], 'pred_labels': [],
                'distances': [], 'similarity': [], 'confidences': []
            }
        
        log_utils.info(f"🎯 开集验证状态初始化完成 (预估样本数: {total_samples})", tag="VALIDATION_ENGINE")
        return state
    
    def update_open_set_state(self, state: Dict, embedding: torch.Tensor, label: torch.Tensor, 
                             predicted: torch.Tensor, confidence: float, path: Optional[List], sample_idx: int):
        """更新开集状态"""
        state['total'] += 1
        state['all_embeddings'].append(embedding.detach())
        state['all_labels'].append(label.detach())
        
        label_item = label.item()
        
        realtime_stats = state['realtime_stats']
        realtime_stats['processed_samples'] += 1
        realtime_stats['class_distribution'][label_item] += 1


class ValidationFeatureAnalyzer:
    """验证特征分析器，负责特征归一化和质量分析"""
    
    def __init__(self, config: ValConfig, parallel_manager, metrics_sample_count: int):
        self.config = config
        self.parallel_manager = parallel_manager
        self._UNIFIED_METRICS_SAMPLE_COUNT = metrics_sample_count
    
    @staticmethod
    def normalize_embedding(embedding: torch.Tensor, normalizer: FeatureNormalizer = None) -> torch.Tensor:
        """统一嵌入向量归一化，优先使用FeatureNormalizer"""
        if embedding.dim() > 2:
            embedding = embedding.view(embedding.size(0), -1)
        elif embedding.dim() == 1:
            embedding = embedding.unsqueeze(0)
        
        if normalizer is not None:
            return normalizer(embedding, training=False)
        else:
            return l2_normalize(embedding, p=2, dim=1)
    
    def _get_recent_samples(self, state: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取最近样本用于特征质量计算"""
        if len(state['all_embeddings']) < 2:
            return torch.empty((0, 1)), torch.empty((0,), dtype=torch.long)
        
        recent_embeddings = state['all_embeddings'][-self._UNIFIED_METRICS_SAMPLE_COUNT:]
        recent_labels = state['all_labels'][-self._UNIFIED_METRICS_SAMPLE_COUNT:]
        
        if len(recent_embeddings) < 2:
            return torch.empty((0, 1)), torch.empty((0,), dtype=torch.long)
        
        # 数据类型安全检查
        valid_embeddings = []
        valid_labels = []
        
        for emb, lab in zip(recent_embeddings, recent_labels):
            if isinstance(emb, torch.Tensor) and isinstance(lab, torch.Tensor):
                if emb.dim() >= 1 and lab.dim() >= 0:
                    valid_embeddings.append(emb)
                    valid_labels.append(lab)
        
        if len(valid_embeddings) < 2:
            return torch.empty((0, 1)), torch.empty((0,), dtype=torch.long)
        
        try:
            all_emb = torch.cat(valid_embeddings, dim=0)
            all_lab = torch.cat(valid_labels, dim=0)
            return all_emb, all_lab
        except Exception as e:
            log_utils.error(f"样本数据拼接失败: {e}", tag="VALIDATION_ENGINE")
            return torch.empty((0, 1)), torch.empty((0,), dtype=torch.long)
    

    

    

    



class ValidationEngine:
    """
    开集验证引擎，职责分离，包含指标计算、状态管理、特征分析
    遵循开发规范：方法≤25行，文件方法≤15个，嵌套≤2层，单一职责
    """
    
    # 统一配置常量
    _UNIFIED_METRICS_SAMPLE_COUNT = 50  # 最近样本数用于特征评估
    _SAMPLES_PER_QUERY_PER_CLASS = 3    # 每类用于查询的样本数
    
    def __init__(self, device: torch.device, config: ValConfig = None, 
                 metrics_calculator=None):
        """初始化开集验证引擎"""
        self.device = device
        self.config = config if config is not None else DEFAULT_VAL_CONFIG
        self.validation_stats = {}
        
        # 并行处理配置
        self.max_workers = min(8, mp.cpu_count())
        self.enable_async_analysis = True
        
        # 创建特征归一化器
        self.feature_normalizer = FeatureNormalizer(
            p=2, dim=1, 
            training_mode=False,  # 验证阶段
            validation_mode=True,
            collect_stats=False,  # 验证不收集统计
            enable_assert=False   # 验证不启用断言
        )
        
        # 创建并行管理器
        self.parallel_manager = parallel_manager.ParallelManager(
            max_workers=self.max_workers,
            enable_async_analysis=self.enable_async_analysis,
            thread_name_prefix="ValidationEngine"
        )
        
        # 依赖注入，优先使用外部MetricsCalculator
        if metrics_calculator is not None:
            self.shared_metrics_calculator = metrics_calculator
            log_utils.info("✅ 使用共享MetricsCalculator，避免重复计算", tag="VALIDATION_ENGINE")
        else:
            from feature.metrics_calculator import MetricsCalculator
            self.shared_metrics_calculator = MetricsCalculator()
            log_utils.info("✅ 创建独立MetricsCalculator实例", tag="VALIDATION_ENGINE")
        
        self.metrics_calculator = ValidationMetricsCalculator(self.config)
        self.state_manager = ValidationStateManager(self.config)
        self.feature_analyzer = ValidationFeatureAnalyzer(self.config, self.parallel_manager, self._UNIFIED_METRICS_SAMPLE_COUNT)
        
        # 初始化边界挖掘器
        self._init_boundary_miner()
        
        log_utils.info(f"🚀 开集验证引擎初始化完成 (并行工作线程: {self.max_workers}, 统一归一化器: FeatureNormalizer)", tag="VALIDATION_ENGINE")
    
    def _init_boundary_miner(self):
        """初始化边界挖掘器"""
        try:
            boundary_utils_config = BoundaryConfig(
                boundary_low=self.config.boundary_similarity_low,
                boundary_high=self.config.boundary_similarity_high,
                denominator_mode="unified",
                enable_normalization=True,
                normalization_factor=15.0
            )
            self._boundary_miner = BoundaryMinerUtils(config=boundary_utils_config)
            log_utils.info("✅ 统一边界挖掘器已初始化", tag="VALIDATION_ENGINE")
        except AttributeError as e:
            log_utils.error(f"无法初始化边界挖掘器: {e}", tag="VALIDATION_ENGINE")
            self._boundary_miner = None
    
    @torch.no_grad()
    def validate(self, model, loader: DataLoader, criterion=None, 
                log_confusion: bool = None, epoch: Optional[int] = None) -> Tuple[float, Dict, Optional[Dict]]:
        """执行开集验证流程 - 重构为标准批量处理模式"""
        if log_confusion is None:
            log_confusion = self.config.default_log_confusion
        
        gallery_per_class = self.config.default_gallery_per_class
        
        # 存储当前epoch用于种子管理
        self.current_epoch = epoch if epoch is not None else 0
        
        model.eval()
        log_utils.info("🔍 启动开集验证引擎 (批量处理模式)", tag="VALIDATION_ENGINE")
        
        # 只在第一次调用时初始化并行管理器
        if not self.parallel_manager.is_available:
            self.parallel_manager.initialize()
        
        try:
            validation_state = self.state_manager.initialize_open_set_state(log_confusion, loader)
            progress_bar = self._create_progress_bar(loader)
            
            for batch_idx, data in enumerate(progress_bar):
                images, labels, paths = self._process_batch_data(data)
                
                # 🔧 重构为批量处理：一次性处理整个batch
                batch_embeddings, batch_predictions, batch_confidences = self._extract_batch_features(
                    model, images, labels, validation_state
                )
                
                # 批量更新状态
                self._batch_update_validation_state(
                    validation_state, batch_embeddings, labels, batch_predictions, 
                    batch_confidences, paths, batch_idx
                )
                
                # 批量收集数据
                self._batch_collect_sample_data(validation_state, batch_embeddings, labels, images, paths)
                self._update_batch_progress(progress_bar, validation_state, batch_idx)
            
            if self.enable_async_analysis:
                self.parallel_manager.wait_async_tasks()
            
            comprehensive_metrics = self._compute_comprehensive_metrics(validation_state, gallery_per_class)
            accuracy, feature_analysis = self._extract_results(comprehensive_metrics, validation_state, gallery_per_class)
            
            confusion_samples = validation_state.get('confusion_samples') if log_confusion else None
            return accuracy, feature_analysis, confusion_samples
            
        except Exception as e:
            log_utils.error(f"验证过程中出现错误: {e}", tag="VALIDATION_ENGINE")
            raise
        # 注意：不在这里shutdown，让ValidationEngine的生命周期管理并行管理器

    def _extract_batch_features(self, model, images: torch.Tensor, labels: torch.Tensor, 
                               validation_state: Dict) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """批量提取特征并计算置信度 - 新增方法，遵循复用机制"""
        with torch.no_grad():
            images = images.to(self.device)
            labels = labels.to(self.device)
            
            # 批量特征提取
            if hasattr(model, 'extract_features'):
                batch_embeddings = model.extract_features(images)
            else:
                # 回退机制：使用模型的前向传播
                batch_embeddings = model(images)
        
        # 批量预测逻辑（当前简化为真实标签）
        batch_predictions = labels.clone()
        batch_confidences = torch.full((labels.size(0),), 0.5, device=self.device)
        
        return batch_embeddings, batch_predictions, batch_confidences

    def _batch_update_validation_state(self, validation_state: Dict, embeddings: torch.Tensor, 
                                     labels: torch.Tensor, predictions: torch.Tensor, 
                                     confidences: torch.Tensor, paths: Optional[List], batch_idx: int):
        """批量更新验证状态 - 新增方法，遵循复用机制"""
        batch_size = embeddings.size(0)
        
        for i in range(batch_size):
            single_embedding = embeddings[i:i+1]
            single_label = labels[i:i+1]
            single_prediction = predictions[i:i+1]
            single_confidence = confidences[i].item()
            single_path = [paths[i]] if paths is not None else None
            
            # 复用原有的单样本更新方法
            sample_idx = batch_idx * batch_size + i
            self.state_manager.update_open_set_state(
                validation_state, single_embedding, single_label, single_prediction, 
                single_confidence, single_path, sample_idx
            )

    def _batch_collect_sample_data(self, state: Dict, embeddings: torch.Tensor, labels: torch.Tensor, 
                                 images: torch.Tensor, paths: Optional[List]):
        """批量收集样本数据到状态 - 新增方法，遵循复用机制"""
        batch_size = embeddings.size(0)
        
        for i in range(batch_size):
            single_embedding = embeddings[i:i+1]
            single_label = labels[i:i+1]
            single_image = images[i:i+1]
            single_path = [paths[i]] if paths is not None else None
            
            # 复用原有的单样本收集方法
            self._collect_sample_data(state, single_embedding, single_label, single_image, single_path)

    def _update_batch_progress(self, progress_bar, state: Dict, batch_idx: int):
        """更新批次进度条显示 - 重构原有方法，遵循复用机制"""
        is_main_process = mp.current_process().name == 'MainProcess'
        if not is_main_process or batch_idx % 5 != 0:
            return
        
        realtime_stats = state['realtime_stats']
        
        progress_info = {
            '样本': realtime_stats['processed_samples'],
            '类别': len(state['class_images']),
            '批次': batch_idx + 1
        }
        
        progress_bar.set_postfix(progress_info)

    def _create_progress_bar(self, loader: DataLoader):
        """创建进度条"""
        is_main_process = mp.current_process().name == 'MainProcess'
        
        if is_main_process:
            return tqdm(
                loader, desc="🔍 开集验证推理", leave=True, ncols=120,
                file=sys.stdout, dynamic_ncols=True, ascii=False, unit="batch",
                postfix={'样本': 0, '类别': 0}
            )
        else:
            return loader
    
    def _process_batch_data(self, data) -> Tuple[torch.Tensor, torch.Tensor, Optional[List]]:
        """处理批次数据格式"""
        if isinstance(data, (list, tuple)) and len(data) == 2:
            images, labels = data
            paths = None
        elif isinstance(data, (list, tuple)) and len(data) == 3:
            images, labels, paths = data
        else:
            raise ValueError(f"不支持的数据格式: {type(data)}")
        
        return images, labels, paths
    
    def _extract_features(self, model, image: torch.Tensor, label: torch.Tensor, 
                         validation_state: Dict) -> Tuple[torch.Tensor, torch.Tensor, float]:
        """提取特征并计算置信度 - 保留原方法，用于向后兼容"""
        with torch.no_grad():
            if hasattr(model, 'extract_features'):
                embedding = model.extract_features(image)
            else:
                embedding = model(image)
        
        # 预测逻辑，直接用真实标签和固定置信度
        predicted = label
        confidence = 0.5
        
        return embedding, predicted, confidence
    
    def _collect_sample_data(self, state: Dict, embedding: torch.Tensor, label: torch.Tensor, 
                           image: torch.Tensor, path: Optional[List]):
        """收集样本数据到状态"""
        label_item = label.item()
        state['class_data'][label_item].append(embedding.cpu())
        state['class_images'][label_item].append(image.cpu())
        if path is not None:
            state['class_paths'][label_item].extend(path)
    
    def _compute_comprehensive_metrics(self, validation_state: Dict, gallery_per_class: int) -> Dict[str, Any]:
        """计算综合评估指标"""
        metrics = {
            'structured_topk_metrics': {},
            'feature_quality_metrics': {}
        }
        
        try:
            topk_metrics = self._evaluate_topk_performance(validation_state, gallery_per_class)
            metrics['structured_topk_metrics'] = topk_metrics
            log_utils.info(f"✅ Top-k评估完成: Top-1={topk_metrics.get('top1_accuracy', 0):.4f}, "
                           f"mAP={topk_metrics.get('mean_average_precision', 0):.4f}, "
                           f"MRR={topk_metrics.get('mean_reciprocal_rank', 0):.4f}", tag="VALIDATION_ENGINE")
        except Exception as e:
            log_utils.error(f"Top-k评估失败: {e}", tag="VALIDATION_ENGINE")
            metrics['structured_topk_metrics'] = {'error': str(e)}
        
        try:
            feature_metrics = self._compute_feature_quality_metrics(validation_state)
            metrics['feature_quality_metrics'] = feature_metrics
            log_utils.info(f"✅ 特征质量评估完成: 分离比={feature_metrics.get('separation_ratio', 0):.4f}", tag="VALIDATION_ENGINE")
        except Exception as e:
            log_utils.error(f"特征质量评估失败: {e}", tag="VALIDATION_ENGINE")
            metrics['feature_quality_metrics'] = {'error': str(e)}
        
        return metrics
    
    def _evaluate_topk_performance(self, validation_state: Dict, gallery_per_class: int) -> Dict[str, float]:
        """评估Top-k性能"""
        valid_classes = [cls for cls, images in validation_state['class_images'].items() 
                        if len(images) > gallery_per_class]
        
        if len(valid_classes) < 2:
            log_utils.warning("有效类别数量不足", tag="VALIDATION_ENGINE")
            return {'error': 'insufficient_classes', 'valid_classes': len(valid_classes)}
        
        n_trials = 5
        all_metrics = []
        
        for trial in range(n_trials):
            trial_metrics = self._single_trial_evaluation(validation_state, valid_classes, gallery_per_class, trial)
            if trial_metrics is not None:
                all_metrics.append(trial_metrics)
        
        if not all_metrics:
            return {'error': 'all_trials_failed'}
        
        averaged_metrics = {}
        for key in all_metrics[0].keys():
            if isinstance(all_metrics[0][key], (int, float)):
                averaged_metrics[key] = sum(metrics[key] for metrics in all_metrics) / len(all_metrics)
            else:
                averaged_metrics[key] = all_metrics[0][key]
        
        averaged_metrics['n_trials'] = len(all_metrics)
        averaged_metrics['n_valid_classes'] = len(valid_classes)
        
        return averaged_metrics
    
    def _single_trial_evaluation(self, validation_state: Dict, valid_classes: List, 
                                gallery_per_class: int, trial: int) -> Optional[Dict[str, float]]:
        """单次Top-k评估试验，保证确定性"""
        try:
            gallery_embeddings = []
            gallery_labels = []
            query_embeddings = []
            query_labels = []
            
            for cls_idx, cls in enumerate(valid_classes):
                cls_embeddings = validation_state['class_data'][cls]
                if len(cls_embeddings) <= gallery_per_class:
                    continue
                
                # 使用统一种子管理器保证随机性可控
                from utils.deterministic_seed_manager import get_seed_manager, create_validation_context
                
                manager = get_seed_manager()
                context = create_validation_context(
                    epoch=getattr(self, 'current_epoch', 0),
                    trial=trial, 
                    class_idx=cls_idx
                )
                indices = manager.generate_trial_indices(len(cls_embeddings), context)
                
                gallery_indices = indices[:gallery_per_class]
                query_indices = indices[gallery_per_class:gallery_per_class + self._SAMPLES_PER_QUERY_PER_CLASS]
                
                for idx in gallery_indices:
                    emb = self._prepare_embedding_for_eval(cls_embeddings[idx])
                    gallery_embeddings.append(emb)
                    gallery_labels.append(cls)
                
                for idx in query_indices:
                    emb = self._prepare_embedding_for_eval(cls_embeddings[idx])
                    query_embeddings.append(emb)
                    query_labels.append(cls)
            
            if not gallery_embeddings or not query_embeddings:
                return None
            
            gallery_tensor = torch.cat(gallery_embeddings, dim=0)
            gallery_labels_tensor = torch.tensor(gallery_labels)
            query_tensor = torch.cat(query_embeddings, dim=0)
            query_labels_tensor = torch.tensor(query_labels)
            
            # 归一化特征
            gallery_tensor = self.feature_normalizer(gallery_tensor, training=False)
            query_tensor = self.feature_normalizer(query_tensor, training=False)
            
            similarities = torch.mm(query_tensor, gallery_tensor.t())
            
            topk_metrics = self.metrics_calculator.compute_structured_topk_metrics(
                similarities, query_labels_tensor, gallery_labels_tensor, k_values=[1, 3, 5]
            )
            
            return topk_metrics
            
        except Exception as e:
            log_utils.error(f"评估试验 {trial} 失败: {e}", tag="VALIDATION_ENGINE")
            return None
    
    def _prepare_embedding_for_eval(self, embedding: torch.Tensor) -> torch.Tensor:
        """归一化嵌入向量用于评估"""
        return ValidationFeatureAnalyzer.normalize_embedding(embedding, self.feature_normalizer)
    
    def _compute_feature_quality_metrics(self, validation_state: Dict) -> Dict[str, float]:
        """计算特征质量指标，依赖注入MetricsCalculator"""
        metrics = {}
        
        try:
            # 计算边界样本比例
            unified_boundary_ratio = self._compute_unified_boundary_samples(validation_state)
            metrics['unified_boundary_ratio'] = unified_boundary_ratio
            metrics['boundary_ratio'] = unified_boundary_ratio
            
            # 计算特征质量
            if validation_state['class_data']:
                embeddings, labels = self._convert_validation_data_for_metrics(validation_state)
                if embeddings.size(0) > 0:
                    self.shared_metrics_calculator.compute_feature_metrics(embeddings, labels)
                    
                    intra_avg, inter_avg = self.shared_metrics_calculator.get_avg_distances()
                    latest_separation_ratio = self.shared_metrics_calculator.get_latest_separation_ratio()
                    
                    if self.shared_metrics_calculator.feature_quality_metrics:
                        latest_quality = self.shared_metrics_calculator.feature_quality_metrics[-1]
                        metrics['fisher_score'] = latest_quality.get('fisher_score', 0.1)
                        metrics['avg_intra_distance'] = intra_avg
                        metrics['avg_inter_distance'] = inter_avg
                        metrics['intra_std'] = latest_quality.get('intra_std', 0.0)
                        metrics['inter_std'] = latest_quality.get('inter_std', 0.0)
                        metrics['separation_ratio'] = latest_separation_ratio
                        
                        log_utils.info(f"✅ 使用共享MetricsCalculator计算特征质量: 分离比={latest_separation_ratio:.4f}, Fisher={metrics['fisher_score']:.4f}", tag="VALIDATION_ENGINE")
                    else:
                        metrics.update(self._get_default_quality_metrics())
                else:
                    metrics.update(self._get_default_quality_metrics())
            else:
                metrics.update(self._get_default_quality_metrics())
            
        except Exception as e:
            log_utils.error(f"特征质量评估失败: {e}", tag="VALIDATION_ENGINE")
            metrics.update(self._get_default_quality_metrics())
        
        return metrics
    
    def _convert_validation_data_for_metrics(self, validation_state: Dict) -> Tuple[torch.Tensor, torch.Tensor]:
        """转换验证数据为MetricsCalculator格式"""
        all_embeddings = []
        all_labels = []
        
        for class_id, embeddings in validation_state['class_data'].items():
            if embeddings:
                for emb in embeddings:
                    normalized_emb = self.feature_normalizer(emb.unsqueeze(0), training=False)
                    all_embeddings.append(normalized_emb)
                    all_labels.append(class_id)
        
        if all_embeddings:
            embeddings_tensor = torch.cat(all_embeddings, dim=0)
            labels_tensor = torch.tensor(all_labels, dtype=torch.long)
            return embeddings_tensor, labels_tensor
        else:
            return torch.empty((0, 1)), torch.empty((0,), dtype=torch.long)
    
    def _get_default_quality_metrics(self) -> Dict[str, float]:
        """返回默认特征质量指标"""
        return {
            'fisher_score': 0.1,
            'avg_intra_distance': 0.0,
            'avg_inter_distance': 0.0,
            'intra_std': 0.0,
            'inter_std': 0.0,
            'separation_ratio': 0.0
        }
    
    def _compute_unified_boundary_samples(self, validation_state: Dict) -> float:
        """计算边界样本比例，调用统一边界挖掘接口"""
        if self._boundary_miner is None:
            log_utils.warning("边界挖掘器未初始化，跳过统一边界样本计算", tag="VALIDATION_ENGINE")
            return 0.0
        
        try:
            # 获取最近样本用于边界检测
            recent_embeddings = validation_state['all_embeddings'][-self._UNIFIED_METRICS_SAMPLE_COUNT:]
            recent_labels = validation_state['all_labels'][-self._UNIFIED_METRICS_SAMPLE_COUNT:]
            
            if len(recent_embeddings) < 5:
                log_utils.debug("样本数量不足，跳过边界样本计算", tag="VALIDATION_ENGINE")
                return 0.0
            
            # 数据类型检查和过滤
            valid_embeddings = []
            valid_labels = []
            
            for emb, lab in zip(recent_embeddings, recent_labels):
                if isinstance(emb, torch.Tensor) and isinstance(lab, torch.Tensor):
                    if emb.dim() >= 1 and lab.dim() >= 0:
                        valid_embeddings.append(emb)
                        valid_labels.append(lab)
            
            if len(valid_embeddings) < 5:
                log_utils.debug("有效Tensor数量不足，跳过边界样本计算", tag="VALIDATION_ENGINE")
                return 0.0
            
            # 拼接当前批次数据
            try:
                current_embeddings = torch.cat(valid_embeddings[-10:], dim=0) if len(valid_embeddings) >= 10 else torch.cat(valid_embeddings, dim=0)
                current_labels = torch.cat(valid_labels[-10:], dim=0) if len(valid_labels) >= 10 else torch.cat(valid_labels, dim=0)
            except Exception as tensor_error:
                log_utils.error(f"数据拼接失败: {tensor_error}", tag="VALIDATION_ENGINE")
                return 0.0
            
            # 准备历史数据
            if len(valid_embeddings) > 10:
                historical_embeddings = [emb.cpu() for emb in valid_embeddings[:-10] if isinstance(emb, torch.Tensor)]
                historical_labels = [lab.cpu() for lab in valid_labels[:-10] if isinstance(lab, torch.Tensor)]
            else:
                historical_embeddings = [current_embeddings.cpu()]
                historical_labels = [current_labels.cpu()]
            
            # 调用边界挖掘接口
            boundary_result = self._boundary_miner.compute_unified_boundary_samples(
                embeddings=current_embeddings,
                labels=current_labels,
                mode="validation",
                historical_embeddings=historical_embeddings,
                historical_labels=historical_labels
            )
            
            log_utils.debug(f"统一边界挖掘结果: 策略={boundary_result.strategy_used}, "
                           f"边界样本数={boundary_result.boundary_count}, "
                           f"边界比例={boundary_result.unified_boundary_ratio:.4f}", tag="VALIDATION_ENGINE")
            
            # 更新状态中的边界样本信息
            validation_state['boundary_samples_count'] = boundary_result.boundary_count
            validation_state['total_sample_pairs'] = boundary_result.unified_total_count
            validation_state['realtime_stats']['boundary_samples'] = boundary_result.boundary_count
            
            return boundary_result.unified_boundary_ratio
            
        except Exception as e:
            log_utils.error(f"统一边界样本计算失败: {e}", tag="VALIDATION_ENGINE")
            return 0.0
    
    def _extract_results(self, comprehensive_metrics: Dict, validation_state: Dict, 
                        gallery_per_class: int) -> Tuple[float, Dict]:
        """提取验证结果和特征分析"""
        structured_topk = comprehensive_metrics.get('structured_topk_metrics', {})
        feature_quality = comprehensive_metrics.get('feature_quality_metrics', {})
        
        accuracy = structured_topk.get('top1_accuracy', 0.0)
        
        # 计算特征质量得分
        feature_quality_score = self._calculate_comprehensive_quality_score(feature_quality)
        
        feature_analysis = {
            'structured_topk_metrics': structured_topk,
            'feature_quality_metrics': feature_quality,
            'feature_quality_score': feature_quality_score,
            'open_set_accuracy': accuracy,
            'gallery_per_class': gallery_per_class,
            'total_samples': validation_state['total'],
            'total_classes': len(validation_state['class_images']),
            'boundary_samples_count': validation_state['boundary_samples_count'],
            'total_sample_pairs': validation_state['total_sample_pairs'],
            'boundary_ratio': feature_quality.get('boundary_ratio', 0.0),
            'separation_ratio': feature_quality.get('separation_ratio', 0.0),
            'avg_intra_dist': feature_quality.get('avg_intra_distance', 0.0),
            'avg_inter_dist': feature_quality.get('avg_inter_distance', 0.0),
            'intra_std': feature_quality.get('intra_std', 0.0),
            'inter_std': feature_quality.get('inter_std', 0.0),
            'fisher_score': feature_quality.get('fisher_score', 0.0),
            'class_distribution': dict(validation_state['realtime_stats']['class_distribution'])
        }
        
        class_counts = list(validation_state['realtime_stats']['class_distribution'].values())
        if class_counts:
            feature_analysis['class_balance'] = min(class_counts) / max(class_counts) if max(class_counts) > 0 else 0.0
        else:
            feature_analysis['class_balance'] = 0.0
        
        return accuracy, feature_analysis
    
    def _calculate_comprehensive_quality_score(self, feature_quality: Dict[str, float]) -> float:
        """获取综合特征质量得分，直接使用MetricsCalculator的结果"""
        # 直接从共享MetricsCalculator获取已计算的质量分数
        if (self.shared_metrics_calculator and 
            self.shared_metrics_calculator.feature_quality_metrics):
            
            # 使用当前的特征质量指标，而不是历史记录
            separation_ratio = feature_quality.get('separation_ratio', 0.0)
            quality_score = self.shared_metrics_calculator.calculate_comprehensive_quality_score(
                feature_quality, separation_ratio
            )
            
            log_utils.debug(f"验证引擎特征质量获取: 使用共享MetricsCalculator结果，得分={quality_score:.4f}", 
                           tag="VALIDATION_ENGINE")
            return quality_score
        else:
            # 降级处理：使用默认计算
            log_utils.warning("共享MetricsCalculator无可用结果，使用默认质量分数", tag="VALIDATION_ENGINE")
            return 0.5
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, 'parallel_manager'):
                self.parallel_manager.shutdown()
        except Exception:
            pass