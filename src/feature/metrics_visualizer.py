"""
指标可视化器 - 专门负责训练指标的可视化和动画生成

职责分离：
- 从MetricsCalculator中分离出可视化功能
- 专注于图表生成、动画制作和数据可视化
- 支持多种可视化模式和自定义配置
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Optional, Any
from utils import log_utils
from feature.metrics_calculator import MetricsUtils


class MetricsVisualizer:
    """
    指标可视化器 - 专门处理训练指标的可视化
    
    功能：
    1. 训练损失和权重曲线绘制
    2. 特征质量指标可视化
    3. 边界样本分析图表
    4. 训练进度动画生成
    5. 权重平衡分析
    """
    
    def __init__(self, default_output_dir: str = "output"):
        """
        初始化可视化器
        
        Args:
            default_output_dir: 默认输出目录
        """
        self.default_output_dir = default_output_dir
        self.default_paths = {
            'plot': os.path.join(default_output_dir, "training_metrics.png"),
            'weight_balance': os.path.join(default_output_dir, "weight_balance_analysis.png"),
            'boundary': os.path.join(default_output_dir, "boundary_analysis.png"),
            'animation': os.path.join(default_output_dir, "animation")
        }
        
        log_utils.info(f"MetricsVisualizer 初始化完成，输出目录: {default_output_dir}", tag="METRICS_VISUALIZER")

    def plot_training_metrics(self, metrics_calculator, save_path: Optional[str] = None):
        """绘制训练指标图，重点关注特征质量"""
        if save_path is None:
            save_path = self.default_paths['plot']

        # 确保输出目录存在
        MetricsUtils.ensure_dir_exists(os.path.dirname(save_path))

        # 设置样式
        plt.style.use('seaborn-v0_8-whitegrid')

        # 主图表绘制
        with MetricsUtils.figure_context(figsize=(15, 16)):
            self._plot_loss_curves(metrics_calculator)
            self._plot_weight_curves(metrics_calculator)
            self._plot_distance_metrics(metrics_calculator)
            self._plot_separation_ratio(metrics_calculator)
            self._plot_feature_quality(metrics_calculator)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            log_utils.info(f"Training metrics saved to {save_path}", tag="METRICS_VISUALIZER")

        # 绘制边界样本和挖掘器权重分析
        if MetricsUtils.is_valid_data(metrics_calculator.boundary_stats):
            self.plot_boundary_analysis(metrics_calculator)

        # 绘制权重平衡分析
        if MetricsUtils.is_valid_data(metrics_calculator.circle_weights):
            self.plot_weight_balance_analysis(metrics_calculator)

        # 生成训练进度动画
        try:
            if len(metrics_calculator.separation_ratios) > 5:
                self.generate_training_animation(metrics_calculator)
        except Exception as e:
            log_utils.error(f"Failed to generate training animation: {e}", tag="METRICS_VISUALIZER")

    def _plot_loss_curves(self, calc):
        """绘制损失曲线"""
        plt.subplot(3, 2, 1)
        epochs = range(1, len(calc.total_losses) + 1)
        plt.plot(epochs, calc.total_losses, 'b-', label='Total Loss', linewidth=2)
        plt.plot(epochs, calc.center_losses, 'g--', label='Center Loss', linewidth=1.5)
        plt.plot(epochs, calc.circle_losses, 'm--', label='Circle Loss', linewidth=1.5)
        plt.title('Training Losses', fontsize=14)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('Loss', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(alpha=0.3)

    def _plot_weight_curves(self, calc):
        """绘制权重曲线"""
        plt.subplot(3, 2, 2)
        epochs = range(1, len(calc.total_losses) + 1)
        plt.plot(epochs, calc.circle_weights, 'g-', label='Circle Weight', linewidth=2)
        plt.plot(epochs, calc.center_weights, 'm-', label='Center Weight', linewidth=2)
        plt.title('Loss Weights', fontsize=14)
        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('Weight', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(alpha=0.3)

    def _plot_distance_metrics(self, calc):
        """绘制距离指标"""
        if not (calc.intra_dists and calc.inter_dists):
            return

        epochs = range(1, len(calc.total_losses) + 1)

        try:
            # 找到有数据的类别
            valid_classes = [c for c in calc.intra_dists if len(calc.intra_dists[c]) > 0]

            if not valid_classes:
                log_utils.warning("No intra-class distances available for plotting", tag="METRICS_VISUALIZER")
                return

            # 计算类内距离平均值
            intra_avgs = []
            for i in range(min(len(calc.intra_dists[valid_classes[0]]), len(epochs))):
                class_means = [np.mean(calc.intra_dists[c][:i+1]) for c in valid_classes
                             if i < len(calc.intra_dists[c])]
                if class_means:
                    intra_avgs.append(np.mean(class_means))

            # 计算类间距离平均值
            inter_avgs = []
            for i in range(min(len(calc.inter_dists), len(epochs))):
                inter_avgs.append(np.mean(calc.inter_dists[:i+1]))

            # 绘制类内距离
            if intra_avgs:
                plt.subplot(3, 2, 3)
                plt.plot(range(1, len(intra_avgs) + 1), intra_avgs, 'b-',
                         label='Intra-class Distance', linewidth=2)
                plt.title('Average Intra-class Distance', fontsize=14)
                plt.xlabel('Epoch', fontsize=12)
                plt.ylabel('Distance', fontsize=12)
                plt.grid(alpha=0.3)

            # 绘制类间距离
            if inter_avgs:
                plt.subplot(3, 2, 4)
                plt.plot(range(1, len(inter_avgs) + 1), inter_avgs, 'r-',
                         label='Inter-class Distance', linewidth=2)
                plt.title('Average Inter-class Distance', fontsize=14)
                plt.xlabel('Epoch', fontsize=12)
                plt.ylabel('Distance', fontsize=12)
                plt.grid(alpha=0.3)

        except Exception as e:
            log_utils.error(f"Error plotting distance metrics: {e}", tag="METRICS_VISUALIZER")

    def _plot_separation_ratio(self, calc):
        """绘制特征分离比"""
        if not MetricsUtils.is_valid_data(calc.separation_ratios):
            return

        try:
            plt.subplot(3, 2, 5)

            # 特征分离比数据
            y_values = calc.separation_ratios
            x_values = range(1, len(y_values) + 1)

            # 计算合适的Y轴范围
            max_y = max(y_values)
            use_log_scale = max_y > 100

            if use_log_scale:
                # 对数转换显示
                self._plot_log_scale_separation(x_values, y_values, max_y)
            else:
                # 直接显示原始值
                self._plot_normal_scale_separation(x_values, y_values)

        except Exception as e:
            log_utils.error(f"Error plotting separation ratio: {e}", tag="METRICS_VISUALIZER")

    def _plot_log_scale_separation(self, x_values, y_values, max_y):
        """使用对数比例绘制特征分离比"""
        log_values = [np.log10(1 + y) for y in y_values]
        plt.plot(x_values, log_values, 'g-', linewidth=2.5)
        plt.axhline(y=np.log10(1 + 10), color='r', linestyle='--', alpha=0.5,
                   label='Baseline (Original=10)')
        plt.axhline(y=np.log10(1 + 30), color='orange', linestyle='--', alpha=0.5,
                   label='Excellent Separation (Original=30)')
        plt.title('Feature Separation Ratio - Log Scale', fontsize=14)
        plt.ylim(bottom=0.0, top=max(np.log10(1 + 100), max(log_values)*1.1))

        # 添加双Y轴，右侧显示原始值
        ax2 = plt.gca().twinx()
        ax2.set_ylabel('Original Separation Value', fontsize=10, color='blue')
        y_ticks = [0, 10, 30, 50, 100, int(max_y)]
        y_ticks = sorted(list(set(y_ticks)))  # 去重并排序
        ax2.set_yticks([np.log10(1 + y) for y in y_ticks])
        ax2.set_yticklabels([str(y) for y in y_ticks])
        ax2.tick_params(axis='y', colors='blue')

        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('Separation Value (Log Scale)', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(alpha=0.3)

    def _plot_normal_scale_separation(self, x_values, y_values):
        """使用普通比例绘制特征分离比"""
        plt.plot(x_values, y_values, 'g-', linewidth=2.5)

        # 对实际数据范围调整参考线
        median_y = max(10, np.median(y_values))
        plt.axhline(y=max(5, median_y * 0.3), color='r', linestyle='--', alpha=0.5,
                   label=f'Baseline (Value={max(5, int(median_y * 0.3))})')
        plt.axhline(y=max(10, median_y * 0.7), color='orange', linestyle='--', alpha=0.5,
                   label=f'Excellent Separation (Value={max(10, int(median_y * 0.7))})')
        plt.title('Feature Separation Ratio (Cosine Distance)', fontsize=14)
        plt.ylim(bottom=0.0, top=max(median_y * 2, max(y_values) * 1.1))

        plt.xlabel('Epoch', fontsize=12)
        plt.ylabel('Separation Value', fontsize=12)
        plt.legend(fontsize=10)
        plt.grid(alpha=0.3)

    def _plot_feature_quality(self, calc):
        """绘制特征质量指标"""
        if not MetricsUtils.is_valid_data(calc.feature_quality_metrics):
            return

        try:
            plt.subplot(3, 2, 6)

            # 提取指标
            feature_epochs = [m['epoch'] for m in calc.feature_quality_metrics]

            # 绘制类中心距离
            if 'center_dist_mean' in calc.feature_quality_metrics[0]:
                center_dists = [m.get('center_dist_mean', 0) for m in calc.feature_quality_metrics]
                if any(center_dists):  # 确保至少有一个非零值
                    plt.plot(feature_epochs, center_dists, 'm-', label='Class Center Distance', linewidth=2)

            # 绘制最小类中心距离
            if 'center_dist_min' in calc.feature_quality_metrics[0]:
                min_dists = [m.get('center_dist_min', 0) for m in calc.feature_quality_metrics]
                if any(min_dists):  # 确保至少有一个非零值
                    plt.plot(feature_epochs, min_dists, 'c--', label='Min Center Distance', linewidth=1.5)

            plt.title('Feature Space Metrics', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Distance', fontsize=12)
            plt.legend(fontsize=10)
            plt.grid(alpha=0.3)

        except Exception as e:
            log_utils.error(f"Error plotting feature quality: {e}", tag="METRICS_VISUALIZER")

    def plot_boundary_analysis(self, calc, save_path: Optional[str] = None):
        """
        绘制边界样本分析图 (重构后)
        适配BoundaryResult的字典结构
        """
        if save_path is None:
            save_path = self.default_paths['boundary']

        if not MetricsUtils.is_valid_data(calc.boundary_stats):
            log_utils.info("No boundary stats available for plotting.", tag="METRICS_VISUALIZER")
            return

        with MetricsUtils.figure_context(figsize=(12, 10)):
            # 提取数据
            epochs = [s.get('epoch', i + 1) for i, s in enumerate(calc.boundary_stats)]
            boundary_counts = [s.get('boundary_count', 0) for s in calc.boundary_stats]
            avg_similarities = [s.get('avg_similarity', 0) for s in calc.boundary_stats]
            unified_ratios = [s.get('unified_boundary_ratio', 0) for s in calc.boundary_stats]

            # 绘制边界样本数量
            plt.subplot(2, 1, 1)
            plt.plot(epochs, boundary_counts, 'b-o', label='Boundary Samples Count', markersize=5)
            plt.title('Boundary Samples Analysis', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Count', fontsize=12)
            plt.legend()
            plt.grid(alpha=0.3)

            # 绘制平均相似度
            ax2 = plt.gca().twinx()
            ax2.plot(epochs, avg_similarities, 'r-s', label='Avg Similarity', markersize=5, alpha=0.6)
            ax2.set_ylabel('Avg Similarity', color='r', fontsize=12)
            ax2.tick_params(axis='y', labelcolor='r')

            # 绘制统一边界样本比例
            plt.subplot(2, 1, 2)
            plt.plot(epochs, unified_ratios, 'g-^', label='Unified Boundary Ratio', markersize=5)
            plt.title('Unified Boundary Ratio Over Epochs', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Ratio', fontsize=12)
            plt.legend()
            plt.grid(alpha=0.3)
            plt.ylim(bottom=0, top=max(0.1, max(unified_ratios) * 1.2 if unified_ratios else 0.1)) # 保证Y轴有合理范围

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            log_utils.info(f"Boundary analysis plot saved to {save_path}", tag="METRICS_VISUALIZER")

    def plot_weight_balance_analysis(self, calc, save_path: Optional[str] = None):
        """绘制损失权重比率和占比趋势图"""
        if save_path is None:
            save_path = self.default_paths['weight_balance']
            
        MetricsUtils.ensure_dir_exists(os.path.dirname(save_path))

        with MetricsUtils.figure_context(figsize=(12, 6)):
            # 计算Circle:Center比率
            e = range(1, len(calc.circle_weights) + 1)
            circle_center_ratios = [c/(cent+1e-8) for c, cent in zip(calc.circle_weights, calc.center_weights)]

            plt.subplot(1, 2, 1)
            plt.plot(e, circle_center_ratios, 'g-', linewidth=2.5)
            plt.axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='Min Reasonable Ratio (0.5)')
            plt.axhline(y=1.0, color='g', linestyle='--', alpha=0.7, label='Ideal Ratio Range (1.0)')
            plt.axhline(y=2.0, color='orange', linestyle='--', alpha=0.7, label='Max Reasonable Ratio (2.0)')
            plt.title('Circle:Center Weight Ratio Trend', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Ratio', fontsize=12)
            plt.legend(fontsize=10)
            plt.grid(alpha=0.3)

            # 计算各损失权重占比
            total_weights = []
            for i in range(len(calc.circle_weights)):
                circ = calc.circle_weights[i]
                cent = calc.center_weights[i]
                total = circ + cent
                total_weights.append([circ/total, cent/total])

            circ_percentages = [w[0] for w in total_weights]
            cent_percentages = [w[1] for w in total_weights]

            plt.subplot(1, 2, 2)
            plt.stackplot(e, circ_percentages, cent_percentages,
                          labels=['Circle', 'Center'],
                          colors=['#66b3ff', '#99ff99'], alpha=0.8)
            plt.title('Loss Weight Proportion Trend', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Proportion', fontsize=12)
            plt.legend(fontsize=10, loc='upper left')
            plt.grid(alpha=0.3)

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            log_utils.info(f"Loss weight balance analysis saved to {save_path}", tag="METRICS_VISUALIZER")

    def generate_training_animation(self, calc):
        """生成训练进度动画，展示特征空间随训练变化"""
        if not MetricsUtils.is_valid_data(calc.feature_quality_metrics) or len(calc.feature_quality_metrics) < 5:
            return  # 数据点不足，无法生成动画

        # 创建动画文件夹
        animation_dir = self.default_paths['animation']
        MetricsUtils.ensure_dir_exists(animation_dir)

        try:
            # 提取数据
            epochs = [m['epoch'] for m in calc.feature_quality_metrics]
            separation_ratios = [m['separation_ratio'] for m in calc.feature_quality_metrics]
            intra_dists = [m['intra_dist'] for m in calc.feature_quality_metrics]
            inter_dists = [m['inter_dist'] for m in calc.feature_quality_metrics]

            # 设置字体
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Helvetica']
            plt.rcParams['axes.unicode_minus'] = True

            # 生成训练进度可视化
            self._generate_progress_animation(animation_dir, epochs, separation_ratios, intra_dists, inter_dists)

            # 生成权重变化图
            if MetricsUtils.is_valid_data(calc.circle_weights):
                self._generate_weights_animation(animation_dir, calc)

        except Exception as e:
            log_utils.error(f"生成训练动画失败: {str(e)}", tag="METRICS_VISUALIZER")

    def _generate_progress_animation(self, animation_dir, epochs, separation_ratios, intra_dists, inter_dists):
        """生成训练进度动画"""
        animation_path = os.path.join(animation_dir, "training_progress.png")

        with MetricsUtils.figure_context(figsize=(10, 6)):
            # 绘制特征分离比
            plt.subplot(1, 2, 1)
            plt.plot(epochs, separation_ratios, 'g-', linewidth=2, label='Feature Separation Ratio')
            plt.title('Feature Separation Progress', fontsize=12)
            plt.xlabel('Epoch', fontsize=10)
            plt.ylabel('Separation Ratio', fontsize=10)
            plt.grid(alpha=0.3)

            # 绘制距离变化
            plt.subplot(1, 2, 2)
            plt.plot(epochs, intra_dists, 'b-', linewidth=2, label='Intra-class Distance')
            plt.plot(epochs, inter_dists, 'r-', linewidth=2, label='Inter-class Distance')
            plt.title('Feature Distance Changes', fontsize=12)
            plt.xlabel('Epoch', fontsize=10)
            plt.ylabel('Distance Value', fontsize=10)
            plt.legend()
            plt.grid(alpha=0.3)

            plt.tight_layout()
            plt.savefig(animation_path, dpi=300, bbox_inches='tight')
            log_utils.info(f"训练进度动画已保存至 {animation_path}", tag="METRICS_VISUALIZER")

    def _generate_weights_animation(self, animation_dir, calc):
        """生成权重动态变化图"""
        weights_path = os.path.join(animation_dir, "weights_animation.png")

        with MetricsUtils.figure_context(figsize=(12, 6)):
            epochs = range(1, len(calc.circle_weights) + 1)

            # 绘制主要损失权重
            plt.plot(epochs, calc.circle_weights, 'g-', linewidth=2, label='Circle Weight')
            plt.plot(epochs, calc.center_weights, 'b-', linewidth=2, label='Center Weight')

            # 绘制挖掘器权重（如果有）
            if MetricsUtils.is_valid_data(calc.hard_weights):
                plt.plot(epochs, calc.hard_weights, 'c--', linewidth=1.5, label='Hard Miner')
                plt.plot(epochs, calc.ms_weights, 'm--', linewidth=1.5, label='MS Miner')
                plt.plot(epochs, calc.boundary_weights, 'y--', linewidth=1.5, label='Boundary Miner')

            plt.title('Weight Dynamic Changes', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Weight Value', fontsize=12)
            plt.legend()
            plt.grid(alpha=0.3)

            plt.tight_layout()
            plt.savefig(weights_path, dpi=300, bbox_inches='tight')
            log_utils.info(f"权重动态变化图已保存至 {weights_path}", tag="METRICS_VISUALIZER")

    def plot_tsne(self, model, loader, perplexity=30, max_iter=1000, random_state=42):
        """
        使用t-SNE对特征空间进行增强可视化，同时提供2D和3D可视化

        Args:
            model: 特征提取模型
            loader: 数据加载器
            perplexity: t-SNE的困惑度参数
            max_iter: t-SNE迭代次数（替代已弃用的n_iter参数）
            random_state: 随机种子
        """
        import torch
        import sys
        from tqdm import tqdm
        from sklearn.manifold import TSNE
        import matplotlib.colors as mcolors
        from collections import Counter
        from datetime import datetime
        import gc
        
        model.eval()
        all_embeddings = []
        all_labels = []

        # 收集所有特征和标签
        feature_pbar = tqdm(
            loader,
            desc="Extracting features for t-SNE",
            leave=False,
            ncols=120,
            file=sys.stdout,
            dynamic_ncols=True,
            ascii=True
        )
        
        device = next(model.parameters()).device
        
        # 🔧 修复：使用torch.no_grad()减少内存使用，并添加内存管理
        with torch.no_grad():
            for batch_idx, (images, labels) in enumerate(feature_pbar):
                images = images.to(device)
                embeddings = model(images)
                
                # 立即移到CPU并从GPU内存中删除
                embeddings_cpu = embeddings.cpu().detach()
                all_embeddings.append(embeddings_cpu)
                all_labels.append(labels)
                
                # 清理GPU内存
                del embeddings
                if device.type == 'mps':
                    torch.mps.empty_cache()
                elif device.type == 'cuda':
                    torch.cuda.empty_cache()
                
                # 每处理10个批次进行一次垃圾回收
                if (batch_idx + 1) % 10 == 0:
                    gc.collect()
                
                # 🔧 修复：限制最大样本数量，避免内存溢出
                total_samples = sum(len(emb) for emb in all_embeddings)
                if total_samples >= 1500:  # 降低最大样本数
                    log_utils.info(f"已收集{total_samples}个样本，停止特征提取以避免内存溢出", tag="METRICS_VISUALIZER")
                    break

        # 🔧 修复：添加内存清理和更安全的数据合并
        if not all_embeddings:
            log_utils.warning("没有收集到任何特征数据，跳过t-SNE可视化", tag="METRICS_VISUALIZER")
            return
        
        # 合并特征和标签
        try:
            all_embeddings = torch.cat(all_embeddings, dim=0)
            all_labels = torch.cat(all_labels, dim=0)
            
            # 转为numpy数组
            embeddings_np = all_embeddings.detach().numpy()
            labels_np = all_labels.numpy()
            
            # 清理torch张量以释放内存
            del all_embeddings, all_labels
            gc.collect()
            
        except RuntimeError as e:
            log_utils.error(f"合并特征数据时出现内存错误: {e}", tag="METRICS_VISUALIZER")
            log_utils.info("尝试使用更小的数据集进行t-SNE可视化", tag="METRICS_VISUALIZER")
            
            # 如果内存不足，只使用前几个批次的数据
            if len(all_embeddings) > 3:
                all_embeddings = all_embeddings[:3]  # 只使用前3个批次
                all_labels = all_labels[:3]
                
                all_embeddings = torch.cat(all_embeddings, dim=0)
                all_labels = torch.cat(all_labels, dim=0)
                
                embeddings_np = all_embeddings.detach().numpy()
                labels_np = all_labels.numpy()
                
                del all_embeddings, all_labels
                gc.collect()
            else:
                log_utils.error("数据量太少，无法进行t-SNE可视化", tag="METRICS_VISUALIZER")
                return

        # 为可视化限制样本数量 - 进一步降低以避免内存问题
        max_samples = min(1000, len(embeddings_np))  # 从2000降低到1000
        if len(embeddings_np) > max_samples:
            # 🔧 使用统一种子管理器控制随机性
            from utils.deterministic_seed_manager import get_seed_manager, SeedContext
            
            manager = get_seed_manager()
            context = SeedContext(base_seed=random_state)
            indices = manager.sample_without_replacement(len(embeddings_np), max_samples, context)
            embeddings_np = embeddings_np[indices]
            labels_np = labels_np[indices]
            
        log_utils.info(f"使用{len(embeddings_np)}个样本进行t-SNE可视化", tag="METRICS_VISUALIZER")

        # 🔧 修复：调整t-SNE参数以减少内存使用和计算时间
        # 根据样本数量调整perplexity
        adjusted_perplexity = min(perplexity, len(embeddings_np) // 4, 30)
        adjusted_max_iter = min(max_iter, 500)  # 减少迭代次数
        
        log_utils.info(f"执行t-SNE降维 (样本数: {len(embeddings_np)}, perplexity: {adjusted_perplexity}, max_iter: {adjusted_max_iter})...", tag="METRICS_VISUALIZER")
        
        try:
            # 使用t-SNE降维到2D
            tsne_2d = TSNE(
                n_components=2, 
                perplexity=adjusted_perplexity, 
                max_iter=adjusted_max_iter, 
                random_state=random_state,
                n_jobs=1  # 限制并行度以减少内存使用
            )
            embeddings_2d = tsne_2d.fit_transform(embeddings_np)
            
            # 清理内存
            del tsne_2d
            gc.collect()
            
            # 使用t-SNE降维到3D - 只有在2D成功且样本数足够时才执行
            embeddings_3d = None
            if len(embeddings_np) <= 800:  # 只有样本数较少时才做3D
                tsne_3d = TSNE(
                    n_components=3, 
                    perplexity=adjusted_perplexity, 
                    max_iter=adjusted_max_iter, 
                    random_state=random_state,
                    n_jobs=1
                )
                embeddings_3d = tsne_3d.fit_transform(embeddings_np)
                del tsne_3d
                gc.collect()
            else:
                log_utils.info("样本数量较多，跳过3D t-SNE以节省内存", tag="METRICS_VISUALIZER")
                
        except Exception as e:
            log_utils.error(f"t-SNE降维失败: {e}", tag="METRICS_VISUALIZER")
            log_utils.info("尝试使用PCA作为备选方案", tag="METRICS_VISUALIZER")
            
            # 备选方案：使用PCA
            from sklearn.decomposition import PCA
            pca_2d = PCA(n_components=2, random_state=random_state)
            embeddings_2d = pca_2d.fit_transform(embeddings_np)
            
            if len(embeddings_np) <= 800:
                pca_3d = PCA(n_components=3, random_state=random_state)
                embeddings_3d = pca_3d.fit_transform(embeddings_np)
            else:
                embeddings_3d = None
                
            log_utils.info("使用PCA替代t-SNE完成降维", tag="METRICS_VISUALIZER")

        # 计算类别信息
        unique_labels = np.unique(labels_np)
        num_classes = len(unique_labels)

        # 创建颜色映射 - 使用推荐的API
        if num_classes <= 10:
            tableau_colors = list(mcolors.TABLEAU_COLORS.values())
            cmap = lambda i: tableau_colors[i % len(tableau_colors)]
        else:
            cmap = plt.cm.get_cmap('tab20')

        # 创建不同类别的标记列表
        markers = ['o', 's', '^', 'D', 'p', '*', 'X', 'P', '8', 'h']

        # 绘制2D t-SNE可视化
        plt.figure(figsize=(12, 10))

        # 计算每个类别的平均中心位置（用于标注类名）
        class_centers = {}
        for label in unique_labels:
            mask = (labels_np == label)
            if np.sum(mask) > 0:
                class_centers[label] = np.mean(embeddings_2d[mask], axis=0)

        # 绘制每个类别的样本
        for i, label in enumerate(unique_labels):
            mask = (labels_np == label)
            color = cmap(i) if num_classes <= 10 else cmap(i % 20)
            plt.scatter(
                embeddings_2d[mask, 0],
                embeddings_2d[mask, 1],
                color=color,  # 修复UserWarning - 使用color而不是c=[...]
                marker=markers[i % len(markers)],
                label=f'Class {label}',
                alpha=0.7,
                s=30
            )

            # 标注类别名称在类别中心
            if label in class_centers:
                center = class_centers[label]
                plt.annotate(
                    f'{label}',
                    xy=(center[0], center[1]),
                    xytext=(center[0], center[1]),
                    fontsize=8,
                    ha='center',
                    va='center',
                    bbox=dict(boxstyle='round,pad=0.1', fc='white', alpha=0.3)
                )

        plt.title('t-SNE Visualization of Feature Space (2D)', fontsize=14)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 保存具有日期时间戳的图像
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        save_path = os.path.join(self.default_output_dir, f"tsne_visualization_multitask_{timestamp}.png")
        MetricsUtils.ensure_dir_exists(os.path.dirname(save_path))
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        log_utils.info(f"2D t-SNE visualization saved to {save_path}", tag="METRICS_VISUALIZER")
        plt.close()

        # 🔧 修复：只有在3D数据存在时才进行3D可视化
        if embeddings_3d is not None:
            # 3D可视化
            fig = plt.figure(figsize=(12, 10))
            ax = fig.add_subplot(111, projection='3d')

            # 绘制3D图
            for i, label in enumerate(unique_labels):
                mask = (labels_np == label)
                color = cmap(i) if num_classes <= 10 else cmap(i % 20)
                ax.scatter(
                    embeddings_3d[mask, 0],
                    embeddings_3d[mask, 1],
                    embeddings_3d[mask, 2],
                    color=color,  # 修复UserWarning - 使用color而不是c=[...]
                    marker=markers[i % len(markers)],
                    label=f'Class {label}',
                    alpha=0.7,
                    s=30
                )

            ax.set_title('t-SNE Visualization of Feature Space (3D)', fontsize=14)
            ax.grid(True, linestyle='--', alpha=0.7)

            # 保存3D图
            save_path_3d = os.path.join(self.default_output_dir, f"tsne_visualization_multitask_3d_{timestamp}.png")
            plt.savefig(save_path_3d, dpi=300, bbox_inches='tight')
            log_utils.info(f"3D t-SNE visualization saved to {save_path_3d}", tag="METRICS_VISUALIZER")
            plt.close()
        else:
            log_utils.info("跳过3D可视化（未生成3D数据）", tag="METRICS_VISUALIZER")

        # 类间关系可视化 - 将类别中心连接成最小生成树
        if len(class_centers) > 2:
            try:
                # 计算类别中心之间的距离矩阵
                centers = np.array([class_centers[label] for label in class_centers.keys()])
                labels_list = list(class_centers.keys())

                # 使用scipy的最小生成树
                from scipy.spatial.distance import pdist, squareform
                from scipy.sparse.csgraph import minimum_spanning_tree

                # 计算距离矩阵
                dist_matrix = squareform(pdist(centers))

                # 计算最小生成树
                mst = minimum_spanning_tree(dist_matrix).toarray()

                # 绘制类别关系图
                plt.figure(figsize=(14, 12))

                # 首先绘制所有类别中心点
                for i, label in enumerate(labels_list):
                    color = cmap(i) if num_classes <= 10 else cmap(i % 20)
                    plt.scatter(
                        centers[i, 0],
                        centers[i, 1],
                        color=color,
                        marker='o',
                        s=100,
                        label=f'Class {label}'
                    )
                    plt.annotate(
                        f'{label}',
                        xy=(centers[i, 0], centers[i, 1]),
                        xytext=(centers[i, 0] + 0.2, centers[i, 1] + 0.2),
                        fontsize=10,
                        ha='center',
                        va='center',
                        bbox=dict(boxstyle='round,pad=0.1', fc='white', alpha=0.7)
                    )

                # 然后绘制最小生成树的边
                for i in range(len(labels_list)):
                    for j in range(i+1, len(labels_list)):
                        if mst[i, j] > 0 or mst[j, i] > 0:
                            plt.plot(
                                [centers[i, 0], centers[j, 0]],
                                [centers[i, 1], centers[j, 1]],
                                'k-',
                                alpha=0.5,
                                linewidth=1.0
                            )

                plt.title('Class Relationship Visualization (Minimum Spanning Tree)', fontsize=14)
                plt.grid(True, linestyle='--', alpha=0.7)

                # 保存类别关系图
                relation_path = os.path.join(self.default_output_dir, f"class_relationship_{timestamp}.png")
                plt.savefig(relation_path, dpi=300, bbox_inches='tight')
                log_utils.info(f"Class relationship visualization saved to {relation_path}", tag="METRICS_VISUALIZER")
                plt.close()
            except Exception as e:
                log_utils.error(f"Failed to create class relationship visualization: {str(e)}", tag="METRICS_VISUALIZER")
        
        # 特征聚类分析 - 类内分布
        if len(labels_np) > 10:
            try:
                # 选择最多10个类别进行类内分布分析
                top_classes = Counter(labels_np).most_common(10)
                top_class_labels = [label for label, count in top_classes]

                fig, axes = plt.subplots(2, 5, figsize=(20, 8))
                axes = axes.flatten()

                for i, label in enumerate(top_class_labels):
                    if i >= len(axes):
                        break

                    # 获取该类的样本
                    mask = (labels_np == label)
                    class_embeds_2d = embeddings_2d[mask]

                    if len(class_embeds_2d) > 3:  # 至少需要3个样本
                        # 计算类内样本分布
                        x, y = class_embeds_2d[:, 0], class_embeds_2d[:, 1]

                        # 绘制散点图 - 修复UserWarning
                        color = cmap(i) if num_classes <= 10 else cmap(i % 20)
                        axes[i].scatter(x, y, alpha=0.7, color=color)

                        # 尝试绘制2D密度等高线
                        try:
                            from scipy.stats import gaussian_kde
                            xy = np.vstack([x, y])
                            kernel = gaussian_kde(xy)

                            # 创建网格
                            xmin, xmax = x.min(), x.max()
                            ymin, ymax = y.min(), y.max()
                            xx, yy = np.mgrid[xmin:xmax:100j, ymin:ymax:100j]
                            positions = np.vstack([xx.ravel(), yy.ravel()])

                            # 计算核密度估计
                            z = kernel(positions).reshape(xx.shape)

                            # 绘制等高线
                            axes[i].contour(xx, yy, z, levels=5, cmap=f'Blues')
                        except Exception as e:
                            log_utils.error(f"Could not create density plot for class {label}: {str(e)}", tag="METRICS_VISUALIZER")

                        axes[i].set_title(f'Class {label} Distribution', fontsize=10)
                        axes[i].grid(True, linestyle='--', alpha=0.5)

                plt.tight_layout()
                intra_dist_path = os.path.join(self.default_output_dir, f"class_intra_distribution_{timestamp}.png")
                plt.savefig(intra_dist_path, dpi=300, bbox_inches='tight')
                log_utils.info(f"Class intra-distribution visualization saved to {intra_dist_path}", tag="METRICS_VISUALIZER")
                plt.close()
            except Exception as e:
                log_utils.error(f"Failed to create class intra-distribution visualization: {str(e)}", tag="METRICS_VISUALIZER")

        return save_path 