import torch
import torch.nn as nn
import torch.nn.functional as F
from utils import log_utils
from utils.feature_norm import create_normalizer_from_config
import time
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Union, Literal, Any
from pytorch_metric_learning.distances import CosineSimilarity

from feature.adaptive_weight_control import AdaptiveWeightController, BoundaryMonitorCallback, WeightNormalizer
from feature.miners.miner_manager import MinerManager, MinerModeType
from pytorch_metric_learning.losses import CircleLoss
from .subcenter_arcface_loss import SubCenterArcFaceLoss

# 导入默认配置和类型定义
from config import (
    DEFAULT_TRAIN_CONFIG, 
    TrainConfig
)

# 定义损失函数模式类型
LossModeType = Literal["balanced"]

# 多任务损失函数回调管理器
class MultiTaskLossCallbackManager:
    
    @staticmethod
    def create_callbacks(loss_mode: str, callback_config: TrainConfig) -> List:
        """创建权重调整回调"""
        from feature.adaptive_weight_control import create_advanced_callbacks
        return create_advanced_callbacks(callback_config)

class MultiTaskLossInitializer:
    """多任务损失初始化器，负责配置加载和参数覆盖"""
    
    @staticmethod
    def load_configs(config: Optional[TrainConfig] = None,
                    loss_weight_config: Optional[TrainConfig] = None,
                    callback_config: Optional[TrainConfig] = None,
                    ms_optimization_config: Optional[TrainConfig] = None) -> Tuple[TrainConfig, TrainConfig, TrainConfig, TrainConfig]:
        """加载所有配置对象"""
        return (
            config or DEFAULT_TRAIN_CONFIG,
            loss_weight_config or DEFAULT_TRAIN_CONFIG,
            callback_config or DEFAULT_TRAIN_CONFIG,
            ms_optimization_config or DEFAULT_TRAIN_CONFIG
        )
    
    @staticmethod
    def apply_parameter_overrides(config: TrainConfig, **kwargs) -> Dict[str, Any]:
        """应用参数覆盖"""
        params = {
            'feat_dim': kwargs.get('feat_dim') or config.default_feat_dim,
            'loss_mode': kwargs.get('loss_mode') or config.default_loss_mode,
            'miner_mode': kwargs.get('miner_mode') or config.default_miner_mode,
            'circle_margin': kwargs.get('circle_margin') or config.circle_margin,
            'circle_gamma': kwargs.get('circle_gamma') or config.circle_gamma,
            'confidence_threshold': kwargs.get('confidence_threshold') or config.confidence_threshold
        }
        return params
    
    @staticmethod
    def create_loss_components(num_classes: int, params: Dict[str, Any], config: TrainConfig) -> Dict[str, Any]:
        """创建损失函数组件 - 简化为 Circle Loss + Sub-center ArcFace"""
        cosine_dist = CosineSimilarity()
        normalizer = create_normalizer_from_config(config)
        return {
            'cosine_dist': cosine_dist,
            'normalizer': normalizer,
            'ce_loss': nn.CrossEntropyLoss(),
            'circle_loss': CircleLoss(m=params['circle_margin'], gamma=params['circle_gamma'], distance=cosine_dist),
            'subcenter_arcface': SubCenterArcFaceLoss(
                num_classes=num_classes,
                feat_dim=params['feat_dim'],
                margin=config.arcface_margin,
                scale=config.arcface_scale,
                sub_centers=config.arcface_sub_centers,
                center_alpha=0.3,  # 固定值，不再从配置读取
                center_weight=config.arcface_center_weight
            )
        }

class MultiTaskLossComputer:
    """多任务损失计算器，负责各类损失计算"""
    
    def __init__(self, loss_components: Dict[str, Any], config: TrainConfig):
        self.loss_components = loss_components
        self.config = config
    
    def compute_circle_loss(self, embeddings: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """计算CircleLoss"""
        return self.loss_components['circle_loss'](embeddings, labels)
    
    def compute_all_losses(self, embeddings: torch.Tensor, labels: torch.Tensor, 
                          loss_mode: str, boundary_pairs: Optional[Tuple] = None, 
                          current_epoch: int = -1) -> Dict[str, torch.Tensor]:
        """计算简化的损失项：Circle Loss + Sub-center ArcFace"""
        losses = {
            'circle': self.compute_circle_loss(embeddings, labels),
            'subcenter_arcface': self.loss_components['subcenter_arcface'](embeddings, labels)
        }
        return losses
    


class MultiTaskLossMetricsTracker:
    """多任务损失指标跟踪器，负责损失和分离度等历史记录"""
    
    def __init__(self, config: TrainConfig):
        self.config = config
        self.loss_histories = {
            'circle': [], 'subcenter_arcface': []
        }
        self.feature_separation_history = []
        self.separation_improvements = []
        self.boundary_samples_count = []
        self.boundary_similarity_avg = []
    
    def log_loss(self, name: str, value: torch.Tensor):
        """记录单项损失值到历史"""
        try:
            if isinstance(value, torch.Tensor):
                self.loss_histories[name].append(value.item())
            else:
                self.loss_histories[name].append(float(value))
        except (AttributeError, RuntimeError, ValueError):
            self.loss_histories[name].append(self.config.tensor_zero_value)
    
    def track_separation_metrics(self, intra_dist: float, inter_dist: float) -> float:
        """记录特征分离度指标，计算提升幅度"""
        from utils.distance_calculator import compute_separation_ratio
        current_separation = compute_separation_ratio(intra_dist, inter_dist)
        self.feature_separation_history.append(current_separation)
        if len(self.feature_separation_history) > 1:
            improvement = current_separation - self.feature_separation_history[-2]
            self.separation_improvements.append(improvement)
        return current_separation
    
    def track_basic_boundary_info(self, boundary_count: int):
        """跟踪基础边界信息"""
        self.boundary_samples_count.append(boundary_count)
    
    def get_recent_loss_trend(self, loss_name: str, window_size: int = 5) -> float:
        """获取最近损失趋势"""
        if loss_name not in self.loss_histories or len(self.loss_histories[loss_name]) < window_size:
            return 0.0
        recent_losses = self.loss_histories[loss_name][-window_size:]
        return sum(recent_losses) / len(recent_losses)

class MultiTaskLoss(nn.Module):
    """
    多任务损失函数V4，职责分离，包含初始化、回调、损失计算、指标跟踪
    重构后仅保留CircleLoss作为主要距离损失，移除TripletLoss
    """
    
    def __init__(self, num_classes: int, 
                 feat_dim: Optional[int] = None,
                 loss_mode: Optional[LossModeType] = None, 
                 miner_mode: Optional[MinerModeType] = None,
                 circle_margin: Optional[float] = None, 
                 circle_gamma: Optional[float] = None,
                 confidence_threshold: Optional[float] = None,
                 config: Optional[TrainConfig] = None,
                 loss_weight_config: Optional[TrainConfig] = None,
                 callback_config: Optional[TrainConfig] = None,
                 ms_optimization_config: Optional[TrainConfig] = None):
        """初始化多任务损失函数，加载配置和组件"""
        super().__init__()
        self.config, self.loss_weight_config, self.callback_config, self.ms_optimization_config = \
            MultiTaskLossInitializer.load_configs(config, loss_weight_config, callback_config, ms_optimization_config)
        params = MultiTaskLossInitializer.apply_parameter_overrides(
            self.config, feat_dim=feat_dim,
            loss_mode=loss_mode, miner_mode=miner_mode, circle_margin=circle_margin,
            circle_gamma=circle_gamma, 
            confidence_threshold=confidence_threshold
        )
        self.feat_dim = params['feat_dim']
        self.loss_mode = params['loss_mode']
        self.miner_mode = params['miner_mode']
        loss_components = MultiTaskLossInitializer.create_loss_components(num_classes, params, self.config)
        self.cosine_dist = loss_components['cosine_dist']
        self.normalizer = loss_components['normalizer']
        self.ce_loss = loss_components['ce_loss']
        self.circle_loss = loss_components['circle_loss']
        self.subcenter_arcface = loss_components['subcenter_arcface']
        self.miner_manager = MinerManager(mode=self.miner_mode, cosine_dist=self.cosine_dist)
        self.loss_computer = MultiTaskLossComputer(loss_components, self.config)
        self.metrics_tracker = MultiTaskLossMetricsTracker(self.config)
        self._init_weight_controller()
        self._apply_ms_optimization()
        self._init_state_variables()
        self._log_initialization()

    
    def _init_weight_controller(self):
        """初始化权重控制器和回调"""
        callbacks = MultiTaskLossCallbackManager.create_callbacks(self.loss_mode, self.callback_config)
        initial_weights = self._get_initial_weights_by_mode(self.loss_mode)
        initial_weights = WeightNormalizer.normalize_weights(initial_weights)
        self.weight_controller = AdaptiveWeightController(
            initial_weights=initial_weights,
            stability_factor=self.config.weight_stability_factor,
            adaptation_rate=self.config.weight_adaptation_rate,
            callbacks=callbacks
        )
        self.weight_callbacks = callbacks
    
    def _apply_ms_optimization(self):
        """应用MS权重优化配置，支持动态增强"""
        if self.miner_mode == "balanced":
            self.miner_manager.apply_optimized_weights(self.ms_optimization_config.balanced_mode_default_config)
            self.miner_manager.enable_dynamic_ms_enhancement(self.ms_optimization_config.enable_dynamic_enhancement)
            log_utils.info(f"已应用MS权重优化：{self.ms_optimization_config.balanced_mode_default_config} + 动态增强", tag="MULTI_TASK_LOSS")

    def _init_state_variables(self):
        """初始化内部状态变量"""
        self._logged_epochs = set()
        self._last_weight_update = time.time() - self.config.initial_weight_update_offset
        self._normalized_weights = None
        self._current_epoch = -1
    
    def _get_initial_weights_by_mode(self, mode: LossModeType) -> Dict[str, float]:
        """根据损失模式获取初始权重 - 简化为 Circle Loss + Sub-center ArcFace"""
        return {
            'circle': 0.7,  # Circle Loss 作为主要距离损失
            'subcenter_arcface': 0.3  # Sub-center ArcFace 集成类内紧凑性和边距控制
        }
    
    def _log_initialization(self):
        """记录初始化信息"""
        log_utils.info(f"简化多任务损失初始化完成 - 模式: {self.loss_mode}, 特征维度: {self.feat_dim}", tag="MULTI_TASK_LOSS")
        log_utils.info(f"Circle损失配置: m={getattr(self.circle_loss, 'm', 'N/A')}, gamma={getattr(self.circle_loss, 'gamma', 'N/A')}", tag="MULTI_TASK_LOSS")
        log_utils.info(f"Sub-center ArcFace配置: margin={self.subcenter_arcface.margin}, scale={self.subcenter_arcface.scale}, sub_centers={self.subcenter_arcface.sub_centers}", tag="MULTI_TASK_LOSS")
    
    def forward(self, embeddings: torch.Tensor, labels: torch.Tensor, epoch: Optional[int] = None, 
                intra_dist: Optional[float] = None, inter_dist: Optional[float] = None,
                curriculum_stage: Optional[int] = None, difficulty_level: Optional[float] = None, 
                easy_ratio: Optional[float] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """前向计算联合损失，包含权重调整和指标记录"""
        if epoch is not None and epoch != self._current_epoch:
            self._current_epoch = epoch
            self._normalized_weights = None
        if curriculum_stage is not None or difficulty_level is not None:
            self._apply_curriculum_aware_adjustments(curriculum_stage, difficulty_level, easy_ratio, epoch)
        
        # 获取边界样本信息用于ArcFace损失
        boundary_info = self.miner_manager.mine_pairs(embeddings, labels)
        boundary_pairs = boundary_info.get('boundary_pairs')
        
        # 计算所有损失
        losses = self.loss_computer.compute_all_losses(
            embeddings, labels, self.loss_mode, boundary_pairs, self._current_epoch
        )
        
        self._track_metrics(intra_dist, inter_dist, embeddings, boundary_info, losses)
        self.miner_manager.adjust_miner_weights(epoch, boundary_info)
        
        if epoch is not None:
            self.miner_manager.dynamic_adjust_ms_weight(epoch, boundary_info)
            # Calculate sample_separation for logging
            total_samples = boundary_info.get('total_samples', 1)
            num_hard = boundary_info.get('num_hard', 0)
            num_boundary = boundary_info.get('num_boundary', 0)
            if total_samples > 0:
                hard_ratio = num_hard / max(total_samples, 1)
                boundary_ratio = num_boundary / max(total_samples, 1)
                sample_separation = max(0.0, min(1.0, (1.0 - hard_ratio) * 0.7 + boundary_ratio * 0.3))
                self._log_sample_separation_stats(epoch, sample_separation)
        
        weights = self._maybe_update_weights(epoch, intra_dist, inter_dist, losses, boundary_info)
        total_loss = self._compute_weighted_loss(weights, losses)
        self._record_loss_components(losses)
        
        # 返回总损失、Circle损失、Circle损失（保持接口兼容性）
        return total_loss, losses['circle'], losses['circle']
    
    def _track_metrics(self, intra_dist: Optional[float], inter_dist: Optional[float], 
                      embeddings: torch.Tensor, boundary_info: Dict, losses: Dict[str, torch.Tensor]):
        """🔧 重构：训练阶段仅跟踪损失，移除验证相关计算"""
        # 仅记录损失值，不执行复杂的分离度或边界相似度计算
        for name, value in losses.items():
            self.metrics_tracker.log_loss(name, value)
        
        # 简化指标跟踪：仅记录基础边界信息用于损失权重调整
        if hasattr(self.metrics_tracker, 'track_basic_boundary_info'):
            self.metrics_tracker.track_basic_boundary_info(boundary_info.get('num_boundary', 0))
    
    def _maybe_update_weights(self, epoch: Optional[int], intra_dist: Optional[float], 
                             inter_dist: Optional[float], losses: Dict[str, torch.Tensor],
                             boundary_info: Dict) -> Dict[str, float]:
        """根据epoch和间隔更新权重控制器，返回归一化权重"""
        # 🔧 确保每次都更新挖掘器结果，支持采样器反馈
        self._latest_miner_results = boundary_info
        
        if self._normalized_weights is not None and epoch == self._current_epoch:
            return self._normalized_weights
        weights = self.weight_controller.get_weights()
        if epoch is not None and epoch > 0:
            current_time = time.time()
            if epoch not in self._logged_epochs and (current_time - self._last_weight_update) > self.config.weight_update_interval:
                self._last_weight_update = current_time
                metrics = self._collect_metrics(intra_dist, inter_dist, losses, boundary_info)
                self.weight_controller.update(metrics, epoch)
                weights = self.weight_controller.get_weights()
                normalized_weights = WeightNormalizer.normalize_weights(weights)
                self.weight_controller.set_weights(normalized_weights)
                self.weight_controller.log_weight_distribution(epoch)
                self._logged_epochs.add(epoch)
                self._normalized_weights = normalized_weights
                return normalized_weights
        normalized_weights = WeightNormalizer.normalize_weights(weights)
        self._normalized_weights = normalized_weights
        return normalized_weights
    
    def _collect_metrics(self, intra_dist: Optional[float], inter_dist: Optional[float], 
                        losses: Dict[str, torch.Tensor], boundary_info: Dict) -> Dict:
        """🔧 重构：训练阶段仅收集必要指标，移除验证相关计算"""
        self._latest_miner_results = boundary_info
        
        # 训练阶段仅收集损失和边界样本数，不计算分离度
        return {
            'boundary_samples': boundary_info['num_boundary'],
            'losses': {name: value.item() if isinstance(value, torch.Tensor) else value
                      for name, value in losses.items()},
            # 保留原始距离值但不计算分离度（交由验证阶段）
            'intra_dist': intra_dist or 0,
            'inter_dist': inter_dist or 0
        }
    
    def _compute_weighted_loss(self, weights: Dict[str, float], 
                              losses: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算简化的加权总损失"""
        return (
            weights['circle'] * losses['circle'] +
            weights['subcenter_arcface'] * losses['subcenter_arcface']
        )
    
    def _apply_curriculum_aware_adjustments(self, curriculum_stage: Optional[int], 
                                           difficulty_level: Optional[float], 
                                           easy_ratio: Optional[float], 
                                           epoch: Optional[int]) -> None:
        """优化后的课程联动调整机制 - 修复优先级错误"""
        if curriculum_stage is None and difficulty_level is None:
            return
        
        # 初始化状态跟踪
        if not hasattr(self, '_last_curriculum_stage'):
            self._last_curriculum_stage = -1
            self._curriculum_stable_epochs = 0
            self._global_adjustment_locks = set()  # 全局调整锁定，防止与其他组件冲突
            self._adjustment_counter = 0
        

        
        # 🔧 优先级1: 课程阶段变化检测（最高优先级）
        curriculum_changed = (curriculum_stage is not None and 
                            curriculum_stage != self._last_curriculum_stage)
        
        if curriculum_changed:
            self._last_curriculum_stage = curriculum_stage
            self._curriculum_stable_epochs = 0
            self._adjustment_counter += 1
            
            current_weights = self.weight_controller.get_weights()
            if self._adjust_weights_by_curriculum(curriculum_stage, current_weights):
                log_utils.info(f"📚 课程阶段变化触发权重调整: Stage {curriculum_stage}", tag="CURRICULUM")
                return
        
        # 🔧 优先级2: 课程稳定期检查
        if curriculum_stage is not None and curriculum_stage == self._last_curriculum_stage:
            self._curriculum_stable_epochs += 1
            
            # 课程稳定期内的精细调整
            if self._curriculum_stable_epochs <= 3:
                if difficulty_level is not None and difficulty_level > 0.7:
                    current_weights = self.weight_controller.get_weights()
                    # 高难度阶段，提升Circle权重
                    if current_weights.get('circle', 0) < 0.65:
                        self.weight_controller.weights['circle'] = min(0.65, current_weights['circle'] * 1.05)
                        log_utils.info(f"📚 高难度调整: Circle权重提升至 {self.weight_controller.weights['circle']:.3f}", tag="CURRICULUM")
                
                if easy_ratio is not None and easy_ratio > 0.8:
                    current_weights = self.weight_controller.get_weights()
                    # 简单样本过多，提升Center权重
                    if current_weights.get('center', 0) < 0.3:
                        self.weight_controller.weights['center'] = min(0.3, current_weights['center'] * 1.03)
                        log_utils.info(f"📚 简单样本调整: Center权重提升至 {self.weight_controller.weights['center']:.3f}", tag="CURRICULUM")
        else:
            # 课程阶段变化后的稳定期，暂停其他调整
            log_utils.debug(f"📚 课程稳定期: {self._curriculum_stable_epochs}/3 epochs", tag="CURRICULUM")
    
    def _adjust_weights_by_curriculum(self, curriculum_stage: int, current_weights: Dict[str, float]) -> bool:
        """根据课程阶段调整损失权重"""
        base_adjustment = 0.02
        stage_adjustment = 0.03 * curriculum_stage
        
        # 提升Circle权重作为主要距离损失
        circle_boost = base_adjustment + stage_adjustment
        new_circle = min(0.8, current_weights['circle'] + circle_boost)
        
        # 调整Sub-center ArcFace权重
        arcface_boost = base_adjustment + stage_adjustment * 0.5
        new_arcface = min(0.4, current_weights.get('subcenter_arcface', 0.3) + arcface_boost)
        
        if abs(new_circle - current_weights['circle']) > 0.005:
            self.weight_controller.weights['circle'] = new_circle
            self.weight_controller.weights['subcenter_arcface'] = new_arcface
            if self._adjustment_counter % 5 == 0:
                log_utils.info(f"📚 课程阶段调整 (Stage {curriculum_stage}): "
                              f"Circle {current_weights['circle']:.3f}→{new_circle:.3f}, "
                              f"Sub-center ArcFace {current_weights.get('subcenter_arcface', 0.3):.3f}→{new_arcface:.3f}", tag="MULTI_TASK_LOSS")
            return True
        return False
    
    def _record_loss_components(self, losses: Dict[str, torch.Tensor]) -> None:
        """记录各损失组件的值"""
        for name, value in losses.items():
            self.metrics_tracker.log_loss(name, value)
    
    def _log_sample_separation_stats(self, epoch: int, sample_separation: float) -> None:
        """记录样本分离度统计信息"""
        if epoch % 10 == 0:
            log_utils.info(f"样本分离度统计 (Epoch {epoch}): {sample_separation:.3f}", tag="MULTI_TASK_LOSS")
    
    def get_weights(self) -> Dict[str, float]:
        """获取当前权重"""
        return self.weight_controller.get_weights()
    
    def get_loss_histories(self) -> Dict[str, List[float]]:
        """获取损失历史记录"""
        return self.metrics_tracker.loss_histories
    
    def get_latest_boundary_result(self) -> Optional[Any]:
        """获取最新边界挖掘结果"""
        if hasattr(self, '_latest_miner_results') and 'boundary_result' in self._latest_miner_results:
            return self._latest_miner_results['boundary_result']
        return None
    
    def get_latest_miner_info(self) -> Optional[Dict]:
        """获取最新的挖掘器结果，用于采样器反馈"""
        if hasattr(self, '_latest_miner_results'):
            # 构造采样器所需的反馈信号
            miner_info = self._latest_miner_results.copy()
            
            # 添加计算的难度比例信息
            total_samples = miner_info.get('total_samples', 1)
            num_hard = miner_info.get('num_hard', 0)
            num_ms = miner_info.get('num_ms', 0) 
            num_boundary = miner_info.get('num_boundary', 0)
            
            # 计算各类样本比例
            hard_ratio = num_hard / max(total_samples, 1)
            ms_ratio = num_ms / max(total_samples, 1)
            boundary_ratio = num_boundary / max(total_samples, 1)
            
            # 🔧 新增：计算样本分离度指标
            # 基于硬样本和边界样本的比例计算整体样本分离度
            # 高分离度 = 低硬样本比例 + 高边界样本比例
            sample_separation = max(0.0, min(1.0, (1.0 - hard_ratio) * 0.7 + boundary_ratio * 0.3))
            
            # 更新miner_info
            miner_info.update({
                'hard_ratio': hard_ratio,
                'ms_ratio': ms_ratio, 
                'boundary_ratio': boundary_ratio,
                'sample_separation': sample_separation,  # 🔧 添加缺失字段
                'feedback_timestamp': time.time(),       # 🔧 添加时间戳用于调试
                'difficulty_signal': hard_ratio + ms_ratio * 0.5  # 🔧 综合难度信号
            })
            
            return miner_info
        return None
    
    def to(self, device):
        """重写to方法，确保所有组件都移动到正确的设备"""
        super().to(device)
        # 确保所有损失组件都移动到正确的设备
        for key, component in self.loss_computer.loss_components.items():
            if hasattr(component, 'to'):
                component.to(device)
        return self