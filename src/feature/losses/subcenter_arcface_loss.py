import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Dict, Any
from utils.feature_norm import l2_normalize
from utils import log_utils


class SubCenterArcFaceLoss(nn.Module):
    """
    Sub-center ArcFace Loss Implementation
    
    结合了 ArcFace 的角度边距机制和 Sub-center 的多中心策略，
    同时实现类内紧凑性（Center Loss功能）和边距控制（Center Margin功能）。
    
    核心特性：
    - 每个类别维护多个子中心，增强类内表示能力
    - 角度边距机制提供更强的类间分离
    - 自适应权重平衡角度损失和中心损失
    - 向量化实现，支持GPU并行计算
    """
    
    def __init__(self, 
                 num_classes: int,
                 feat_dim: int,
                 margin: float = 0.5,
                 scale: float = 64.0,
                 sub_centers: int = 3,
                 center_alpha: float = 0.3,
                 center_weight: float = 0.1,
                 easy_margin: bool = False):
        """
        初始化 Sub-center ArcFace Loss
        
        Args:
            num_classes: 类别数量
            feat_dim: 特征维度
            margin: 角度边距 (m)
            scale: 缩放因子 (s)
            sub_centers: 每个类别的子中心数量
            center_alpha: 中心更新率
            center_weight: 中心损失权重
            easy_margin: 是否使用简化边距计算
        """
        super().__init__()
        self.num_classes = num_classes
        self.feat_dim = feat_dim
        self.margin = margin
        self.scale = scale
        self.sub_centers = sub_centers
        self.center_alpha = center_alpha
        self.center_weight = center_weight
        self.easy_margin = easy_margin
        
        # 初始化权重矩阵 - 每个类别有多个子中心
        self.register_parameter('weight', 
                               nn.Parameter(torch.randn(num_classes * sub_centers, feat_dim)))
        
        # 初始化类中心 - 用于中心损失计算
        self.register_parameter('centers',
                               nn.Parameter(torch.randn(num_classes, feat_dim)))
        
        # 预计算常量
        self.cos_m = math.cos(margin)
        self.sin_m = math.sin(margin)
        self.th = math.cos(math.pi - margin)
        self.mm = math.sin(math.pi - margin) * margin
        
        # 初始化权重
        self._init_weights()
        
        log_utils.info(f"Sub-center ArcFace初始化: classes={num_classes}, sub_centers={sub_centers}, "
                      f"margin={margin}, scale={scale}, center_weight={center_weight}", 
                      tag="SUBCENTER_ARCFACE")
    
    def _init_weights(self):
        """初始化权重参数"""
        nn.init.xavier_uniform_(self.weight)
        nn.init.xavier_uniform_(self.centers)
    
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        前向传播计算损失
        
        Args:
            features: 输入特征 [batch_size, feat_dim]
            labels: 标签 [batch_size]
            
        Returns:
            总损失 = ArcFace损失 + 中心损失
        """
        # 1. 计算 ArcFace 损失
        arcface_loss = self._compute_arcface_loss(features, labels)
        
        # 2. 计算中心损失（类内紧凑性）
        center_loss = self._compute_center_loss(features, labels)
        
        # 3. 组合损失
        total_loss = arcface_loss + self.center_weight * center_loss
        
        return total_loss
    
    def _compute_arcface_loss(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """计算 Sub-center ArcFace 损失"""
        batch_size = features.size(0)
        
        # L2 归一化特征和权重
        features_norm = l2_normalize(features, p=2, dim=1)
        weight_norm = l2_normalize(self.weight, p=2, dim=1)
        
        # 计算余弦相似度
        cosine = F.linear(features_norm, weight_norm)  # [batch_size, num_classes * sub_centers]
        
        # 重塑为 [batch_size, num_classes, sub_centers]
        cosine = cosine.view(batch_size, self.num_classes, self.sub_centers)
        
        # 对每个类别选择最大的子中心相似度
        cosine, _ = torch.max(cosine, dim=2)  # [batch_size, num_classes]
        
        # 计算角度 - 避免in-place操作
        sine = torch.sqrt(torch.clamp(1.0 - torch.pow(cosine, 2), min=1e-8))
        phi = cosine * self.cos_m - sine * self.sin_m
        
        if self.easy_margin:
            phi = torch.where(cosine > 0, phi, cosine)
        else:
            phi = torch.where(cosine > self.th, phi, cosine - self.mm)
        
        # 🔧 修复MPS兼容性：使用非in-place操作创建one-hot编码
        one_hot = torch.zeros(cosine.size(), device=features.device, dtype=cosine.dtype)
        one_hot = one_hot.scatter(1, labels.view(-1, 1).long(), 1.0)
        
        # 应用边距 - 确保tensor连续性
        output = (one_hot * phi) + ((1.0 - one_hot) * cosine)
        output = output * self.scale
        
        # 计算交叉熵损失
        loss = F.cross_entropy(output, labels)
        
        return loss
    
    def _compute_center_loss(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        计算中心损失 - 实现类内紧凑性
        
        整合了原 Center Loss 的功能，确保特征向类中心收敛
        """
        # 获取每个样本对应的类中心
        centers_batch = self.centers[labels]  # [batch_size, feat_dim]
        
        # 计算特征到中心的距离
        features_norm = l2_normalize(features, p=2, dim=1)
        centers_norm = l2_normalize(centers_batch, p=2, dim=1)
        
        # 使用余弦距离计算中心损失
        cosine_sim = F.cosine_similarity(features_norm, centers_norm, dim=1)
        center_loss = torch.mean(1.0 - cosine_sim)
        
        # 更新类中心
        self._update_centers(features, labels)
        
        return center_loss
    
    def _update_centers(self, features: torch.Tensor, labels: torch.Tensor):
        """更新类中心 - 向量化实现，修复MPS兼容性"""
        unique_labels = torch.unique(labels)
        
        with torch.no_grad():
            for label in unique_labels:
                mask = labels == label
                if mask.sum() > 0:
                    # 计算当前类别的特征均值
                    class_features = features[mask]
                    new_center = class_features.mean(dim=0)
                    
                    # 🔧 修复MPS兼容性：使用非in-place操作更新中心
                    updated_center = (1 - self.center_alpha) * self.centers[label] + \
                                              self.center_alpha * new_center
                    
                    # 通过索引赋值而非in-place修改
                    self.centers.data[label] = updated_center
    
    def get_centers(self) -> torch.Tensor:
        """获取类中心"""
        return self.centers.detach()
    
    def get_sub_centers(self) -> torch.Tensor:
        """获取子中心权重"""
        return self.weight.detach().view(self.num_classes, self.sub_centers, self.feat_dim)
    
    def compute_inter_class_margin(self) -> torch.Tensor:
        """
        计算类间边距 - 替代原 Center Margin Loss 功能
        
        Returns:
            类间最小距离，用于监控类间分离度
        """
        with torch.no_grad():
            centers_norm = l2_normalize(self.centers, p=2, dim=1)
            
            # 计算类间距离矩阵
            dist_matrix = torch.cdist(centers_norm, centers_norm, p=2)
            
            # 获取上三角矩阵（排除对角线）
            mask = torch.triu(torch.ones_like(dist_matrix, dtype=torch.bool), diagonal=1)
            inter_distances = dist_matrix[mask]
            
            if len(inter_distances) > 0:
                return inter_distances.min()
            else:
                return torch.tensor(0.0, device=self.centers.device) 