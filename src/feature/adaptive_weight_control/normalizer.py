"""
Weight Normalizer Utilities

Responsibilities:
- 权重归一化和标准化
- 权重范围约束和精度控制
- 权重分布调整工具

Dependencies:
- src.config (配置管理)
- src.utils.log_utils (日志工具)
"""

from typing import Dict, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class WeightNormalizer:
    """权重归一化工具类"""
    
    @staticmethod
    def normalize_weights(weights_dict: Dict[str, float], target_sum: Optional[float] = None, 
                         config: Optional[TrainConfig] = None) -> Dict[str, float]:
        """
        归一化权重字典，确保所有权重总和为目标值
        
        Args:
            weights_dict: 包含损失名称和权重值的字典
            target_sum: 目标总和，如果为None则使用配置中的默认值
            config: 归一化配置，如果为None则使用默认配置
            
        Returns:
            归一化后的权重字典
        """
        config = config or DEFAULT_TRAIN_CONFIG
        target_sum = target_sum or getattr(config, 'target_sum', 1.0)
        total_weight = sum(weights_dict.values())
        
        if abs(total_weight - target_sum) > getattr(config, 'normalization_tolerance', 1e-6):
            log_utils.debug(f"权重归一化: 总和从 {total_weight:.4f} 调整为 {target_sum:.4f}", tag="WEIGHT_NORMALIZER")
            scale_factor = target_sum / total_weight if total_weight != 0 else 0
            normalized_weights = {k: v * scale_factor for k, v in weights_dict.items()}
        else:
            normalized_weights = weights_dict.copy()
        
        return {
            k: 0.0 if abs(v) < getattr(config, 'zero_threshold', 1e-8) else round(v, getattr(config, 'precision_digits', 4))
            for k, v in normalized_weights.items()
        } 