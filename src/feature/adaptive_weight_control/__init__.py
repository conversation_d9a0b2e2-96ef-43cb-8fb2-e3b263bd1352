"""
Adaptive Weight Control Module

Responsibilities:
- 自适应权重控制系统
- 权重归一化和回调机制
- 边界监控和性能分析
- 权重稳定性和历史分析

Dependencies:
- src.config (配置管理)
- src.utils.log_utils (日志工具)

Change Log:
2024-01-XX: 从 adaptive_weight_controller.py 重构拆分
"""

from .interfaces import WeightCallback
from .callbacks import (
    BoundaryMonitorCallback,
    PerformanceLoggerCallback, 
    WeightStabilityCallback
)
from .analyzers import WeightHistoryAnalyzer
from .normalizer import WeightNormalizer
from .controller import AdaptiveWeightController
from .factory import create_advanced_callbacks

__all__ = [
    # 接口
    'WeightCallback',
    
    # 回调实现
    'BoundaryMonitorCallback',
    'PerformanceLoggerCallback',
    'WeightStabilityCallback',
    
    # 分析器
    'WeightHistoryAnalyzer',
    
    # 核心组件
    'WeightNormalizer',
    'AdaptiveWeightController',
    
    # 工厂函数
    'create_advanced_callbacks'
]

__version__ = '3.0' 