"""
Weight Control Factory Functions

Responsibilities:
- 创建高级回调组合
- 提供便捷的回调配置工厂
- 统一回调组件初始化

Dependencies:
- .callbacks (回调实现)
- .analyzers (分析器)
- src.config (配置管理)
"""

from typing import List, Optional
from .interfaces import WeightCallback
from .callbacks import BoundaryMonitorCallback, PerformanceLoggerCallback, WeightStabilityCallback
from .analyzers import WeightHistoryAnalyzer
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from config import DEFAULT_TRAIN_CONFIG, TrainConfig


def create_advanced_callbacks(callback_config: Optional[TrainConfig] = None,
                            boundary_config: Optional[TrainConfig] = None,
                            log_interval: Optional[int] = None, 
                            stability_threshold: Optional[float] = None, 
                            analysis_interval: Optional[int] = None) -> List[WeightCallback]:
    """创建高级回调组合"""
    final_config = callback_config or DEFAULT_TRAIN_CONFIG
    boundary_cfg = boundary_config or DEFAULT_TRAIN_CONFIG
    
    # 使用提供的配置或默认配置
    # 注意：由于TrainConfig是dataclass，我们直接使用它而不是创建新的配置对象
    
    return [
        BoundaryMonitorCallback(config=boundary_cfg),
        PerformanceLoggerCallback(config=final_config),
        WeightStabilityCallback(config=final_config),
        WeightHistoryAnalyzer(config=final_config)
    ] 