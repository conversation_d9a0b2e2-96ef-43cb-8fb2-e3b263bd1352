"""
Weight Control Callbacks

Responsibilities:
- 边界样本监控回调实现
- 性能指标记录回调实现
- 权重稳定性监控回调实现

Dependencies:
- .interfaces (回调接口)
- src.config (配置管理)
- src.utils.log_utils (日志工具)
"""

from typing import Dict, List, Optional, Any
from .interfaces import WeightCallback
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class BoundaryMonitorCallback(WeightCallback):
    """边界样本监控回调实现"""
    
    def __init__(self, config: Optional[TrainConfig] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.boundary_samples_history = []
        self.boundary_samples_alert = False
        self.last_boundary_alert_epoch = 0
    
    def on_weight_update(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                        epoch: int, metrics: Dict[str, Any]) -> None:
        """监控边界样本变化"""
        if 'boundary_result' in metrics:
            boundary_result = metrics['boundary_result']
            self._check_boundary_samples_alert(boundary_result.boundary_count, epoch)
    
    def on_emergency_reset(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                          epoch: int, reason: str) -> None:
        """紧急重置时记录边界样本状态"""
        log_utils.warning(f"边界监控: 紧急重置触发 - {reason}", tag="WEIGHT_CALLBACK")

    def _get_trend_analysis_data(self):
        """获取用于趋势分析的数据"""
        recent = self.boundary_samples_history[-self.config.min_history_length:]
        current = recent[-1]
        window_size = self.config.window_size
        windows = [
            recent[-window_size:],
            recent[-2*window_size:-window_size] if len(recent) >= 2*window_size else recent[:-window_size] if len(recent) > window_size else []
        ]
        window_avgs = [sum(w)/len(w) if w else 0 for w in windows]
        rate = (window_avgs[0] - window_avgs[1]) / window_avgs[1] if window_avgs[1] > 0 else 0
        return current, rate, window_avgs, recent

    def _is_alert_triggered(self, current: int, rate: float, recent: List[int]) -> bool:
        """根据当前指标判断是否触发警报"""
        if current < self.config.boundary_min_threshold and rate < self.config.decline_trend_threshold:
            return True
        if current < self.config.consecutive_low_threshold and all(s < self.config.boundary_min_threshold for s in recent[-3:]):
            return True
        if rate < self.config.rapid_decline_threshold and current < self.config.current_low_threshold:
            return True
        return False

    def _handle_alert_state(self, alert_triggered: bool, epoch: int, current: int, rate: float, avgs: List[float], recent: List[int]):
        """处理警报状态和冷却期"""
        in_cooldown = (epoch - self.last_boundary_alert_epoch) <= self.config.alert_cooldown
        if not alert_triggered:
            return

        if not in_cooldown:
            self.boundary_samples_alert = True
            self.last_boundary_alert_epoch = epoch
            low_count = sum(1 for s in recent[-3:] if s < self.config.boundary_min_threshold)
            log_utils.warning(f"边界样本预警: 当前{current}个, 连续{low_count}轮低于阈值", tag="WEIGHT_CALLBACK")
            log_utils.warning(f"边界样本变化率: {rate*100:.1f}% (窗口平均: {avgs[0]:.1f} → {avgs[1]:.1f})", tag="WEIGHT_CALLBACK")
        else:
            log_utils.info(f"边界样本问题检测到，但处于冷却期 (至轮次 {self.last_boundary_alert_epoch + self.config.alert_cooldown})", tag="WEIGHT_CALLBACK")
    
    def _handle_recovery_and_logging(self, current: int, rate: float, epoch: int):
        """处理警报恢复和常规日志记录"""
        if self.boundary_samples_alert and current > self.config.recovery_threshold and rate >= 0:
            self.boundary_samples_alert = False
            log_utils.info(f"边界样本预警解除: 当前{current}个 > 阈值{self.config.recovery_threshold}", tag="WEIGHT_CALLBACK")

        if epoch % getattr(self.config, 'statistics_interval', 10) == 0 and self.boundary_samples_history:
            last_10 = self.boundary_samples_history[-10:]
            if not last_10: return
            avg = sum(last_10) / len(last_10)
            log_utils.info(f"边界样本10轮统计: 平均={avg:.1f}, 最小={min(last_10)}, 最大={max(last_10)}, 当前={current}", tag="WEIGHT_CALLBACK")

    def _check_boundary_samples_alert(self, boundary_samples: int, epoch: int):
        """监控边界样本趋势，检测异常减少情况"""
        self.boundary_samples_history.append(boundary_samples)
        if len(self.boundary_samples_history) < self.config.min_history_length:
            return

        current, rate, avgs, recent = self._get_trend_analysis_data()
        alert_triggered = self._is_alert_triggered(current, rate, recent)
        self._handle_alert_state(alert_triggered, epoch, current, rate, avgs, recent)
        self._handle_recovery_and_logging(current, rate, epoch)


class PerformanceLoggerCallback(WeightCallback):
    """性能指标记录回调 - 详细记录权重调整过程中的性能变化"""
    
    def __init__(self, config: Optional[TrainConfig] = None, 
                 log_interval: Optional[int] = None, save_history: Optional[bool] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.log_interval = log_interval or getattr(self.config, 'performance_log_interval', 10)
        self.save_history = save_history if save_history is not None else getattr(self.config, 'save_history', True)
        self.performance_history = []
        self.weight_change_history = []
        self.emergency_reset_history = []
        
    def _log_performance_details(self, epoch: int, max_change: float, metrics: Dict[str, Any], new_weights: Dict[str, float]):
        """记录详细的性能和权重日志"""
        boundary_result = metrics.get('boundary_result')
        boundary_samples = boundary_result.boundary_count if boundary_result else 0
        log_utils.info(f"Epoch {epoch} 权重调整记录: "
                      f"最大变化={max_change:.4f}, "
                      f"分离比={metrics.get('separation_ratio', 0):.3f}, "
                      f"边界样本={boundary_samples}", tag="WEIGHT_CALLBACK")
        log_utils.info(f"  - 权重分布: {', '.join([f'{k}={v:.3f}' for k, v in new_weights.items()])}", tag="WEIGHT_CALLBACK")

    def _log_trend_analysis(self):
        """分析并记录权重变化趋势"""
        if len(self.weight_change_history) < 3: return
        
        recent_changes = [h['max_change'] for h in self.weight_change_history[-3:]]
        avg_change = sum(recent_changes) / len(recent_changes)
        if avg_change < getattr(self.config, 'stable_change_threshold', 0.01):
            log_utils.info(f"  - 权重趋于稳定 (近3轮平均变化: {avg_change:.4f})", tag="WEIGHT_CALLBACK")
        elif avg_change > getattr(self.config, 'large_change_threshold', 0.1):
            log_utils.warning(f"  - 权重变化较大 (近3轮平均变化: {avg_change:.4f})", tag="WEIGHT_CALLBACK")

    def on_weight_update(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                        epoch: int, metrics: Dict[str, Any]) -> None:
        """记录权重更新和性能指标"""
        weight_changes = {k: new_weights.get(k, 0) - old_weights.get(k, 0) for k in old_weights}
        max_change = max(abs(v) for v in weight_changes.values()) if weight_changes else 0.0
        
        if self.save_history:
            self.weight_change_history.append({'epoch': epoch, 'max_change': max_change, 'metrics': metrics})
        
        if epoch % self.log_interval == 0:
            self._log_performance_details(epoch, max_change, metrics, new_weights)
            self._log_trend_analysis()
    
    def on_emergency_reset(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                          epoch: int, reason: str) -> None:
        """记录紧急重置事件"""
        reset_record = {'epoch': epoch, 'reason': reason, 'old_weights': old_weights, 'new_weights': new_weights}
        if self.save_history:
            self.emergency_reset_history.append(reset_record)
        
        log_utils.critical(f"紧急重置记录 - Epoch {epoch}: {reason}", tag="WEIGHT_CALLBACK")
        log_utils.critical(f"  - 重置前: {', '.join([f'{k}={v:.3f}' for k, v in old_weights.items()])}", tag="WEIGHT_CALLBACK")
        log_utils.critical(f"  - 重置后: {', '.join([f'{k}={v:.3f}' for k, v in new_weights.items()])}", tag="WEIGHT_CALLBACK")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        if not self.weight_change_history: return {}
        
        all_changes = [h['max_change'] for h in self.weight_change_history]
        return {
            'total_updates': len(all_changes),
            'emergency_resets': len(self.emergency_reset_history),
            'avg_weight_change': sum(all_changes) / len(all_changes),
            'max_weight_change': max(all_changes),
            'min_weight_change': min(all_changes),
        }


class WeightStabilityCallback(WeightCallback):
    """权重稳定性监控回调 - 监控权重收敛和稳定性"""
    
    def __init__(self, config: Optional[TrainConfig] = None, 
                 stability_threshold: Optional[float] = None, 
                 stability_window: Optional[int] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.stability_threshold = stability_threshold or getattr(self.config, 'stability_threshold', 0.01)
        self.stability_window = stability_window or getattr(self.config, 'stability_window', 5)
        self.stable_epochs = 0
        self.instability_count = 0
        self.recent_changes = []
        self.convergence_detected = False
        
    def _update_stability_state(self, max_change: float):
        """根据最大变化更新稳定性状态"""
        if max_change < self.stability_threshold:
            self.stable_epochs += 1
            self.instability_count = 0
        else:
            self.stable_epochs = 0
            self.instability_count += 1
    
    def _report_stability(self, epoch: int, new_weights: Dict[str, float], max_change: float):
        """报告稳定性或不稳定性"""
        if self.stable_epochs >= self.stability_window and not self.convergence_detected:
            self.convergence_detected = True
            avg_change = sum(self.recent_changes) / len(self.recent_changes) if self.recent_changes else 0
            log_utils.info(f"权重收敛检测 - Epoch {epoch}: 连续{self.stable_epochs}轮稳定, 平均变化 {avg_change:.5f}", tag="WEIGHT_CALLBACK")
        
        elif self.instability_count >= 3:
            log_utils.warning(f"权重不稳定警告 - Epoch {epoch}: 连续{self.instability_count}轮不稳定", tag="WEIGHT_CALLBACK")
            
        elif self.convergence_detected and max_change > self.stability_threshold * 2:
            self.convergence_detected = False
            log_utils.info(f"权重收敛状态重置 - Epoch {epoch}", tag="WEIGHT_CALLBACK")

    def on_weight_update(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                        epoch: int, metrics: Dict[str, Any]) -> None:
        """监控权重稳定性"""
        max_change = max(abs(new_weights.get(k, 0) - old_weights.get(k, 0)) for k in old_weights) if old_weights else 0
        self.recent_changes.append(max_change)
        
        if len(self.recent_changes) > self.stability_window:
            self.recent_changes.pop(0)
        
        self._update_stability_state(max_change)
        self._report_stability(epoch, new_weights, max_change)
    
    def on_emergency_reset(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                          epoch: int, reason: str) -> None:
        """紧急重置时重置稳定性状态"""
        self.stable_epochs = 0
        self.instability_count = 0
        self.recent_changes.clear()
        self.convergence_detected = False
        log_utils.info(f"稳定性监控重置 - Epoch {epoch}: {reason}", tag="WEIGHT_CALLBACK")
    
    def is_stable(self) -> bool:
        """检查当前是否处于稳定状态"""
        return self.stable_epochs >= self.stability_window
    
    def get_stability_metrics(self) -> Dict[str, Any]:
        """获取稳定性指标"""
        return {
            'stable_epochs': self.stable_epochs,
            'instability_count': self.instability_count,
            'convergence_detected': self.convergence_detected,
            'recent_avg_change': sum(self.recent_changes) / len(self.recent_changes) if self.recent_changes else 0,
        } 