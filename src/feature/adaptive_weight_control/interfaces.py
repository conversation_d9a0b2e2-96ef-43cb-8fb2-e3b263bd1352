"""
Weight Control Interfaces

Responsibilities:
- 定义权重回调接口规范
- 提供权重调整和重置的抽象方法

Dependencies:
- abc (抽象基类)
- typing (类型注解)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class WeightCallback(ABC):
    """权重调整回调接口基类"""
    
    @abstractmethod
    def on_weight_update(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                        epoch: int, metrics: Dict[str, Any]) -> None:
        """权重更新时的回调"""
        pass
    
    @abstractmethod
    def on_emergency_reset(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                          epoch: int, reason: str) -> None:
        """紧急重置时的回调"""
        pass 