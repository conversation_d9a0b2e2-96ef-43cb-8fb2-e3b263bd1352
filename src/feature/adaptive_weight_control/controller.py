"""
Adaptive Weight Controller

Responsibilities:
- 自适应权重控制核心逻辑
- 权重调整策略和算法
- 紧急重置和扰动机制
- 权重历史管理和优化

Dependencies:
- .interfaces (回调接口)
- .normalizer (权重归一化)
- src.config (配置管理)
- src.utils.log_utils (日志工具)
"""

import time
import numpy as np
from typing import Dict, List, Optional, Any
from .interfaces import WeightCallback
from .normalizer import WeightNormalizer
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class AdaptiveWeightController:
    """
    自适应权重控制器 V3 - 重构版
    - 增强权重迭代频率,避免滞后：实现更快的权重更新响应，减少调整延迟
    - 提高边界样本敏感度：更精确地监控边界样本变化，快速响应边界样本减少情况
    - 引入灵活回调机制：支持自定义权重调整策略和监控逻辑
    - 增加权重变动幅度：针对关键损失允许更大的调整空间，加速收敛
    - 职责分离：专注于权重管理，移除挖掘器相关逻辑
    """
    
    def _initialize_configs(self, controller_config, weight_range_config, normalizer_config, stability_factor, adaptation_rate):
        """初始化所有配置对象"""
        self.controller_config = controller_config or DEFAULT_TRAIN_CONFIG
        self.weight_range_config = weight_range_config or DEFAULT_TRAIN_CONFIG
        self.normalizer_config = normalizer_config or DEFAULT_TRAIN_CONFIG
        
        if stability_factor is not None or adaptation_rate is not None:
            log_utils.info("使用自定义参数覆盖默认配置", tag="WEIGHT_CONTROLLER")
            # 直接更新配置参数
            if stability_factor is not None:
                self.controller_config.stability_factor = stability_factor
            if adaptation_rate is not None:
                self.controller_config.adaptation_rate = adaptation_rate

    def _initialize_state(self, initial_weights):
        """初始化控制器状态和历史记录"""
        self.weights = WeightNormalizer.normalize_weights(initial_weights, config=self.normalizer_config)
        self.weight_history = {k: [v] for k, v in self.weights.items()}
        self.performance_history = []
        self.epochs_since_improvement = 0
        self.stagnation_count = 0
        self._emergency_reset_count = 0
        self._last_reset_epoch = -1
        self._first_epoch_initialized = False

    def _initialize_advanced_mechanisms(self):
        """初始化高级控制机制（扰动、速率限制等）"""
        self._last_disturb_time = time.time()
        self._disturb_interval = self.controller_config.disturb_interval_seconds
        
        self._consecutive_amplifications = {'circle': 0, 'arcface': 0}
        self._amplification_history = []
        self._max_consecutive_amplifications = 3
        self._rate_limit_cooldown = 5
        self._last_rate_limit_epoch = -1
        
        self._disturbance_snapshots = []
        self._post_disturbance_performance = []
        self._disturbance_rollback_threshold = 0.05
        self._disturbance_monitoring_window = 3
        
        self._loss_contribution_history = []
        self._dominant_loss_tracker = {'circle': [], 'arcface': []}
        self._attribution_analysis_interval = 5

    def __init__(self, initial_weights: Dict[str, float], 
                 controller_config: Optional[TrainConfig] = None,
                 weight_range_config: Optional[TrainConfig] = None,
                 normalizer_config: Optional[TrainConfig] = None,
                 callbacks: Optional[List[WeightCallback]] = None,
                 stability_factor: Optional[float] = None, 
                 adaptation_rate: Optional[float] = None):
        """初始化自适应权重控制器"""
        self._initialize_configs(controller_config, weight_range_config, normalizer_config, stability_factor, adaptation_rate)
        self._initialize_state(initial_weights)
        self.callbacks = callbacks or []
        self._initialize_advanced_mechanisms()
        
        log_utils.info(f"初始化自适应权重控制器 V3 - 稳定因子: {self.controller_config.stability_factor}, 适应率: {self.controller_config.adaptation_rate}", tag="WEIGHT_CONTROLLER")
        log_utils.info(f"初始权重: {', '.join([f'{k}={v:.3f}' for k, v in self.weights.items()])}", tag="WEIGHT_CONTROLLER")
        log_utils.info(f"注册回调数量: {len(self.callbacks)}", tag="WEIGHT_CONTROLLER")
        log_utils.info("🔧 已启用优化功能: 速率限制 + 扰动回退 + 归因分析", tag="WEIGHT_CONTROLLER")

    def add_callback(self, callback: WeightCallback) -> None:
        """添加权重调整回调"""
        self.callbacks.append(callback)
        log_utils.info(f"添加权重回调: {callback.__class__.__name__}", tag="WEIGHT_CONTROLLER")

    def remove_callback(self, callback: WeightCallback) -> None:
        """移除权重调整回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            log_utils.info(f"移除权重回调: {callback.__class__.__name__}", tag="WEIGHT_CONTROLLER")

    def _trigger_callbacks(self, event_type: str, old_weights: Dict[str, float], 
                          new_weights: Dict[str, float], epoch: int, 
                          metrics: Optional[Dict[str, Any]] = None, reason: str = "") -> None:
        """触发回调事件"""
        for callback in self.callbacks:
            try:
                if event_type == "update":
                    callback.on_weight_update(old_weights, new_weights, epoch, metrics or {})
                elif event_type == "emergency_reset":
                    callback.on_emergency_reset(old_weights, new_weights, epoch, reason)
            except Exception as e:
                log_utils.error(f"回调执行失败 {callback.__class__.__name__}: {e}", tag="WEIGHT_CONTROLLER")

    def _run_adjustment_cycle(self, metrics: Dict[str, Any], epoch: int) -> Dict[str, float]:
        """执行一轮完整的权重调整周期"""
        self.performance_history.append(metrics)
        self._monitor_post_disturbance_performance(metrics, epoch)
        
        if epoch % self._attribution_analysis_interval == 0:
            self._perform_loss_attribution_analysis(metrics, epoch)
        
        if len(self.performance_history) < self.controller_config.min_history_length:
            return self.get_weights()

        new_weights = self._execute_weight_adjustment_strategy(metrics, epoch)
        
        boundary_alert = any(hasattr(cb, 'boundary_samples_alert') and cb.boundary_samples_alert for cb in self.callbacks)
        
        if not boundary_alert:
            new_weights = WeightNormalizer.normalize_weights(new_weights, config=self.normalizer_config)
        else:
            log_utils.info("边界样本权重提升模式：跳过归一化", tag="WEIGHT_CONTROLLER")
        
        return new_weights

    def update(self, metrics: Dict[str, Any], epoch: int) -> Dict[str, float]:
        """根据性能指标更新权重"""
        old_weights = self.weights.copy()
        
        # Guard clauses for early exit
        if (epoch == self._last_reset_epoch and self._emergency_reset_count > 0) or \
           (epoch < self.controller_config.early_training_epochs):
            return self.get_weights()

        if not self._first_epoch_initialized and epoch == 1:
            self._initialize_first_epoch()
            return self.get_weights()

        if self._check_emergency_reset_needed(epoch):
            self._perform_emergency_reset(epoch, old_weights)
            return self.get_weights()

        # Core logic
        new_weights = self._run_adjustment_cycle(metrics, epoch)
        self.weights = new_weights
        self._update_weight_history()
        
        self._trigger_callbacks("update", old_weights, new_weights, epoch, metrics)
        return self.weights

    def get_weights(self) -> Dict[str, float]:
        """获取当前所有损失权重"""
        return self.weights.copy()

    def get_history(self) -> Dict[str, List[float]]:
        """获取权重历史记录"""
        return self.weight_history.copy()

    def set_weights(self, new_weights: Dict[str, float]) -> Dict[str, float]:
        """设置新的权重值并确保归一化"""
        original_sum = sum(self.weights.values())
        self.weights = WeightNormalizer.normalize_weights(new_weights, original_sum)
        self._update_weight_history()
        return self.weights

    def log_weight_distribution(self, epoch: int) -> None:
        """分析并记录当前损失权重分布"""
        total = sum(self.weights.values())
        if total == 0: return

        percentages = {k: (v / total) * 100 for k, v in self.weights.items()}
        ratio = self.weights.get('circle', 0) / (self.weights.get('center', 0) + 1e-8)
        
        dist_str = ", ".join([f"{k}: {v:.1f}%" for k, v in percentages.items()])
        log_utils.info(f"轮次 {epoch} - 损失权重分布: {dist_str} (T:C比率 = {ratio:.3f})", tag="WEIGHT_CONTROLLER")

        if percentages.get('circle', 0) < 15:
            log_utils.warning(f"警告: Circle损失权重过低 ({percentages.get('circle', 0):.1f}%)", tag="WEIGHT_CONTROLLER")
        if percentages.get('center', 0) < 10:
            log_utils.warning(f"警告: Center损失权重过低 ({percentages.get('center', 0):.1f}%)", tag="WEIGHT_CONTROLLER")

    def _initialize_first_epoch(self) -> None:
        """初始化第一轮的权重分配"""
        init_total = sum(self.weights.values())
        
        self.weights['circle'] = max(self.weight_range_config.circle_min_init, min(self.weight_range_config.circle_max_init, self.weights['circle']))
        self.weights['center'] = max(self.weight_range_config.center_min_init, min(self.weight_range_config.center_max_init, self.weights['center']))
        
        new_total = self.weights['circle'] + self.weights['center']
        other_weights = {k: v for k, v in self.weights.items() if k not in ['circle', 'center']}
        other_total = sum(other_weights.values())
        
        if other_total > 0:
            scale_factor = (init_total - new_total) / other_total
            for k in other_weights:
                self.weights[k] *= scale_factor
        
        self._first_epoch_initialized = True
        log_utils.info(f"初始权重配置: Circle={self.weights['circle']:.2f}, Center={self.weights['center']:.2f}", tag="WEIGHT_CONTROLLER")

    def _check_emergency_reset_needed(self, epoch: int) -> bool:
        """检查是否需要紧急重置"""
        if epoch != self._last_reset_epoch:
            self._emergency_reset_count = 0
        if self._emergency_reset_count > 0:
            return False
            
        total_weight = sum(self.weights.values())
        if total_weight == 0: return False

        circle_percent = (self.weights.get('circle', 0) / total_weight) * 100
        center_percent = (self.weights.get('center', 0) / total_weight) * 100
        
        boundary_alert = any(hasattr(cb, 'boundary_samples_alert') and cb.boundary_samples_alert for cb in self.callbacks)
        
        # 🔧 修复：复用现有配置而不是期望不存在的emergency参数
        # 使用现有的权重范围配置作为紧急阈值
        circle_emergency_min = self.weight_range_config.circle_min_init * 100  # 转换为百分比
        circle_emergency_max = self.weight_range_config.circle_max_init * 100
        center_emergency_min = self.weight_range_config.center_min_init * 100
        center_emergency_max = self.weight_range_config.center_max_init * 100
        boundary_alert_min = self.weight_range_config.center_min_init * 100  # 复用center_min作为boundary阈值
        
        is_unbalanced = (circle_percent < circle_emergency_min or 
                         center_percent < center_emergency_min or
                         circle_percent > circle_emergency_max or
                         center_percent > center_emergency_max)
        
        is_boundary_problem = boundary_alert and center_percent < boundary_alert_min
        
        return is_unbalanced or is_boundary_problem

    def _perform_emergency_reset(self, epoch: int, old_weights: Dict[str, float]) -> None:
        """执行紧急权重重置"""
        self._emergency_reset_count += 1
        self._last_reset_epoch = epoch
        
        total_weight = sum(old_weights.values())
        circle_percent = (old_weights.get('circle', 0) / total_weight) * 100 if total_weight > 0 else 0
        center_percent = (old_weights.get('center', 0) / total_weight) * 100 if total_weight > 0 else 0
        
        reason = f"权重不平衡: Circle={circle_percent:.1f}%, Center={center_percent:.1f}%"
        log_utils.warning(f"检测到严重{reason}. 执行紧急权重重置", tag="WEIGHT_CONTROLLER")
        
        # 重置到稳定比例
        self.weights['circle'] = self.weight_range_config.circle_reset_target
        self.weights['center'] = self.weight_range_config.center_reset_target
        
        other_weights_keys = [k for k in old_weights if k not in ['circle', 'center']]
        other_total_old = sum(old_weights.get(k, 0) for k in other_weights_keys)
        
        if other_total_old > 0:
            remaining = total_weight - self.weights['circle'] - self.weights['center']
            scale = remaining / other_total_old
            for k in other_weights_keys:
                self.weights[k] = old_weights[k] * scale
        
        self._trigger_callbacks("emergency_reset", old_weights, self.weights, epoch, reason=reason)
        log_utils.warning(f"权重重置后: Circle={self.weights['circle']:.3f}, Center={self.weights['center']:.3f}", tag="WEIGHT_CONTROLLER")

    def _execute_weight_adjustment_strategy(self, metrics: Dict[str, Any], epoch: int) -> Dict[str, float]:
        """执行权重调整策略"""
        prev_sep = self.performance_history[-2].get('separation_ratio', 0) if len(self.performance_history) >= 2 else 0
        improvement = metrics.get('separation_ratio', 0) - prev_sep
        
        if improvement <= 0.001:
            self.epochs_since_improvement += 1
        else:
            self.epochs_since_improvement = 0
        
        new_weights = self.weights.copy()
        
        if self._handle_stagnation(new_weights, epoch) or self._apply_timed_disturbance(new_weights, epoch, improvement):
            return new_weights
        
        self._apply_strategic_adjustments(new_weights, {**metrics, 'epoch': epoch}, improvement, metrics.get('intra_dist', 0))
        return new_weights

    def _handle_stagnation(self, new_weights: Dict[str, float], epoch: int) -> bool:
        """处理训练停滞情况"""
        if self.epochs_since_improvement < 5:
            return False

        self.stagnation_count += 1
        if self.stagnation_count < 2:
            return False

        pre_disturbance_weights = self.weights.copy()
        self._disturbance_snapshots.append({
            'epoch': epoch, 'weights': pre_disturbance_weights,
            'performance': self.performance_history[-1] if self.performance_history else {}
        })
        
        new_weights['circle'] = np.random.uniform(0.60, 0.80)
        new_weights['arcface'] = np.random.uniform(0.20, 0.40)
        
        self.stagnation_count = 0
        self.epochs_since_improvement = 0
        
        log_utils.info(f"执行权重扰动：连续停滞，应用随机扰动", tag="WEIGHT_CONTROLLER")
        return True

    def _apply_timed_disturbance(self, new_weights: Dict[str, float], epoch: int, improvement: float) -> bool:
        """应用定时扰动"""
        if not ((time.time() - self._last_disturb_time) > self._disturb_interval and epoch > 10 and improvement < 0.03):
            return False
        
        self._last_disturb_time = time.time()
        disturb_factor = 0.985 + np.random.random() * 0.03
        
        for k in ['circle', 'center']:
            if k in new_weights:
                new_weights[k] *= disturb_factor
        
        log_utils.info(f"应用定时权重扰动 (±1.5%)", tag="WEIGHT_CONTROLLER")
        return True

    def _apply_strategic_adjustments(self, new_weights: Dict[str, float], metrics: Dict[str, Any], 
                                   improvement: float, intra_dist: float) -> None:
        """应用策略性权重调整"""
        epoch = metrics.get('epoch', 0)
        
        boundary_alert = any(hasattr(cb, 'boundary_samples_alert') and cb.boundary_samples_alert for cb in self.callbacks)
        
        if boundary_alert:
            self._adjust_for_boundary_alert(new_weights, epoch)
        elif metrics.get('separation_ratio', 0) < 1.2:
            self._adjust_for_low_separation(new_weights, epoch)
        elif intra_dist > 0.5:
            self._adjust_for_high_intra_dist(new_weights, epoch)
        else:
            self._adjust_default(new_weights, improvement)

    def _adjust_for_boundary_alert(self, new_weights: Dict[str, float], epoch: int):
        """边界样本预警时的权重调整策略"""
        log_utils.warning(f"检测到边界样本预警，执行权重提升策略 (Epoch {epoch})", tag="WEIGHT_CONTROLLER")
        
        base_adjustment = 0.08
        circle_boost = min(base_adjustment, self.weights['circle'] * 0.4)
        new_weights['circle'] = min(0.55, self.weights['circle'] + circle_boost)
        
        if self._can_amplify('circle', epoch):
            new_weights['circle'] = min(0.70, self.weights['circle'] * 1.15)
            self._record_amplification('circle', 1.15, epoch)
        else:
            new_weights['circle'] = min(0.70, self.weights['circle'] + 0.04)

        if 'center' in self.weights:
            new_weights['center'] = min(0.40, self.weights['center'] + circle_boost * 0.6)

    def _adjust_for_low_separation(self, new_weights: Dict[str, float], epoch: int):
        """特征分离不足时的权重调整策略"""
        for k in ['circle', 'center']:
            if self._can_amplify(k, epoch):
                new_weights[k] = min(0.60, self.weights[k] * 1.05)
                self._record_amplification(k, 1.05, epoch)
            else:
                new_weights[k] = min(0.60, self.weights[k] + 0.01)
                log_utils.warning(f"{k}权重触发速率限制，使用线性调整", tag="WEIGHT_CONTROLLER")

    def _adjust_for_high_intra_dist(self, new_weights: Dict[str, float], epoch: int):
        """类内距离过大时的权重调整策略"""
        for k in ['circle', 'center']:
            if self._can_amplify(k, epoch):
                new_weights[k] = min(0.60, self.weights[k] * 1.08)
                self._record_amplification(k, 1.08, epoch)
            else:
                new_weights[k] = min(0.60, self.weights[k] + 0.015)
        
        if 'center' in self.weights:
            if self._can_amplify('center', epoch):
                new_weights['center'] = min(0.35, self.weights['center'] * 1.05)
                self._record_amplification('center', 1.05, epoch)
            else:
                new_weights['center'] = min(0.35, self.weights['center'] + 0.01)

    def _adjust_default(self, new_weights: Dict[str, float], improvement: float):
        """默认情况下的缓慢权重调整策略"""
        change_rate = self.controller_config.adaptation_rate * (1 - self.controller_config.stability_factor)
        adjust = change_rate * np.sign(improvement)
        
        new_weights['circle'] = max(0.60, min(0.80, self.weights['circle'] * (1 + adjust)))
        new_weights['arcface'] = max(0.20, min(0.40, self.weights['arcface'] * (1 + adjust)))

    def _update_weight_history(self) -> None:
        """更新权重历史记录"""
        for k, v in self.weights.items():
            self.weight_history.setdefault(k, []).append(v)

    def _can_amplify(self, loss_type: str, epoch: int) -> bool:
        """检查是否可以进行指数放大"""
        if epoch - self._last_rate_limit_epoch < self._rate_limit_cooldown:
            return False
        return self._consecutive_amplifications.get(loss_type, 0) < self._max_consecutive_amplifications
    
    def _record_amplification(self, loss_type: str, factor: float, epoch: int) -> None:
        """记录指数放大操作"""
        self._consecutive_amplifications[loss_type] += 1
        self._amplification_history.append({'epoch': epoch, 'loss_type': loss_type, 'factor': factor})
        
        if self._consecutive_amplifications[loss_type] >= self._max_consecutive_amplifications:
            self._last_rate_limit_epoch = epoch
            log_utils.warning(f"{loss_type}权重连续放大{self._max_consecutive_amplifications}次，触发速率限制", tag="WEIGHT_CONTROLLER")
    
    def _monitor_post_disturbance_performance(self, metrics: Dict[str, Any], epoch: int) -> None:
        """监控扰动后性能变化"""
        if not self._disturbance_snapshots: return

        latest_disturbance = self._disturbance_snapshots[-1]
        epochs_since = epoch - latest_disturbance['epoch']
        
        if epochs_since <= self._disturbance_monitoring_window:
            current_perf = metrics.get('separation_ratio', 0)
            prev_perf = latest_disturbance.get('performance', {}).get('separation_ratio', 0)
            change = current_perf - prev_perf
            
            self._post_disturbance_performance.append({'epoch': epoch, 'change': change})
            
            if change < -self._disturbance_rollback_threshold and epochs_since >= 2:
                log_utils.warning(f"扰动后性能下降 {change:.3f}，执行回退", tag="WEIGHT_CONTROLLER")
                self._perform_disturbance_rollback(latest_disturbance, epoch)
    
    def _perform_disturbance_rollback(self, disturbance_snapshot: Dict, epoch: int) -> None:
        """执行扰动回退"""
        old_weights = self.weights.copy()
        self.weights = disturbance_snapshot['weights'].copy()
        self._update_weight_history()
        
        self._disturbance_snapshots.remove(disturbance_snapshot)
        self._post_disturbance_performance.clear()
        
        log_utils.warning(f"执行扰动回退 (Epoch {epoch})", tag="WEIGHT_CONTROLLER")
        self._trigger_callbacks("emergency_reset", old_weights, self.weights, epoch, reason="扰动后效回退")
    
    def _perform_loss_attribution_analysis(self, metrics: Dict[str, Any], epoch: int) -> None:
        """执行主导损失归因分析"""
        losses = metrics.get('losses', {})
        total_loss = sum(losses.values())
        if not losses or total_loss == 0: return
        
        contributions = {
            k: (v * self.weights.get(k, 0)) / total_loss
            for k, v in losses.items() if k in ['circle', 'arcface']
        }
        self._loss_contribution_history.append({'epoch': epoch, 'contributions': contributions})
        
        if contributions:
            dominant_loss, dominant_value = max(contributions.items(), key=lambda x: x[1])
            self._dominant_loss_tracker.setdefault(dominant_loss, []).append(epoch)
            log_utils.info(f"Epoch {epoch} 主导损失: {dominant_loss} (贡献度: {dominant_value:.2f})", tag="WEIGHT_CONTROLLER")

    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化功能总结"""
        return {
            'rate_limiting': {
                'history': self._amplification_history[-10:],
                'consecutive_counts': self._consecutive_amplifications,
            },
            'disturbance_rollback': {
                'count': len(self._disturbance_snapshots),
                'performance_history': self._post_disturbance_performance[-5:],
            },
            'loss_attribution': {
                'history': self._loss_contribution_history[-5:],
                'dominant_loss_tracker': {k: v[-5:] for k, v in self._dominant_loss_tracker.items()},
            }
        } 