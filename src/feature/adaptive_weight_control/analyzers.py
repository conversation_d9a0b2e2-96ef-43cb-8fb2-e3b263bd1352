"""
Weight History Analyzers

Responsibilities:
- 权重历史分析和趋势检测
- 权重振荡模式识别
- 权重变化统计和可视化

Dependencies:
- .interfaces (回调接口)
- src.utils.log_utils (日志工具)
- src.config (配置管理)
"""

import numpy as np
from typing import Dict, List, Optional, Any
from .interfaces import WeightCallback
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class WeightHistoryAnalyzer(WeightCallback):
    """权重历史分析回调 - 深度分析权重变化模式和趋势"""
    
    def __init__(self, config: Optional[TrainConfig] = None, analysis_interval: Optional[int] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.analysis_interval = analysis_interval or getattr(self.config, 'analysis_interval', 10)
        self.weight_snapshots = []
        self.trend_analysis = {}
        self.oscillation_detection = {}
        
    def on_weight_update(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                        epoch: int, metrics: Dict[str, Any]) -> None:
        """记录权重快照并进行趋势分析"""
        boundary_result = metrics.get('boundary_result')
        boundary_samples = boundary_result.boundary_count if boundary_result else 0
        snapshot = {
            'epoch': epoch, 'weights': new_weights.copy(),
            'metrics': {'separation_ratio': metrics.get('separation_ratio', 0), 'boundary_samples': boundary_samples}
        }
        self.weight_snapshots.append(snapshot)
        
        if epoch % self.analysis_interval == 0 and len(self.weight_snapshots) >= 5:
            self._perform_trend_analysis(epoch)
            self._detect_oscillations(epoch)
    
    def on_emergency_reset(self, old_weights: Dict[str, float], new_weights: Dict[str, float], 
                          epoch: int, reason: str) -> None:
        """记录紧急重置对历史分析的影响"""
        log_utils.info(f"权重历史分析: 紧急重置事件 - Epoch {epoch}", tag="WEIGHT_ANALYZER")
        if len(self.weight_snapshots) < 3: return
        
        for weight_name in old_weights.keys():
            values = [s['weights'].get(weight_name) for s in self.weight_snapshots[-3:]]
            log_utils.info(f"  - {weight_name} 重置前趋势: {self._calculate_trend(values)}", tag="WEIGHT_ANALYZER")
    
    def _analyze_weight_trends(self, epoch: int, snapshots: List[Dict]):
        """分析单个权重的趋势"""
        log_utils.info(f"权重趋势分析 - Epoch {epoch}:", tag="WEIGHT_ANALYZER")
        for name in snapshots[0]['weights'].keys():
            values = [s['weights'][name] for s in snapshots]
            trend = self._calculate_trend(values)
            volatility = self._calculate_volatility(values)
            self.trend_analysis[name] = {'trend': trend, 'volatility': volatility, 'epoch': epoch}
            log_utils.info(f"  - {name}: 趋势={trend}, 波动性={volatility:.4f}, 当前值={values[-1]:.3f}", tag="WEIGHT_ANALYZER")

    def _analyze_metric_trends(self, snapshots: List[Dict]):
        """分析关键性能指标的趋势"""
        sep_values = [s['metrics']['separation_ratio'] for s in snapshots]
        log_utils.info(f"  - 分离比率趋势: {self._calculate_trend(sep_values)} (当前: {sep_values[-1]:.3f})", tag="WEIGHT_ANALYZER")
        
        bound_values = [s['metrics']['boundary_samples'] for s in snapshots]
        log_utils.info(f"  - 边界样本趋势: {self._calculate_trend(bound_values)} (当前: {bound_values[-1]})", tag="WEIGHT_ANALYZER")

    def _perform_trend_analysis(self, current_epoch: int) -> None:
        """执行权重趋势分析"""
        if len(self.weight_snapshots) < 5: return
        
        recent_snapshots = self.weight_snapshots[-5:]
        self._analyze_weight_trends(current_epoch, recent_snapshots)
        self._analyze_metric_trends(recent_snapshots)
    
    def _detect_oscillations(self, current_epoch: int) -> None:
        """检测权重振荡模式"""
        if len(self.weight_snapshots) < 6: return
        
        for name in self.weight_snapshots[0]['weights'].keys():
            values = [s['weights'][name] for s in self.weight_snapshots[-6:]]
            score = self._calculate_oscillation_score(values)
            if score > 0.3:
                self.oscillation_detection[name] = {'score': score, 'epoch': current_epoch}
                log_utils.warning(f"权重振荡检测 - {name}: 振荡评分={score:.3f}", tag="WEIGHT_ANALYZER")
    
    def _calculate_trend(self, values: List[float]) -> str:
        """计算数值趋势"""
        if len(values) < 2 or any(v is None for v in values): return "insufficient_data"
        
        x = list(range(len(values)))
        slope, _, _, _, _ = np.polyfit(x, values, 1, full=True)
        slope = slope[0]
        
        if abs(slope) < 0.001: return "stable"
        return "increasing" if slope > 0 else "decreasing"
    
    def _calculate_volatility(self, values: List[float]) -> float:
        """计算波动性"""
        return np.std(values) if len(values) >= 2 else 0.0
    
    def _calculate_oscillation_score(self, values: List[float]) -> float:
        """计算振荡评分"""
        if len(values) < 4: return 0.0
        
        changes = 0
        for i in range(1, len(values) - 1):
            if (values[i] > values[i-1] and values[i] > values[i+1]) or \
               (values[i] < values[i-1] and values[i] < values[i+1]):
                changes += 1
        return changes / (len(values) - 2)
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析总结"""
        return {
            'total_snapshots': len(self.weight_snapshots),
            'trend_analysis': self.trend_analysis.copy(),
            'oscillation_detection': self.oscillation_detection.copy(),
        } 