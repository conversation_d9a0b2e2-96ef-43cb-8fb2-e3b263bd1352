"""
训练回调机制模块

提供标准化的训练过程监控、指标记录和状态管理功能。
将原本分散在train.py中的监控逻辑集中管理，提高代码的可维护性和可扩展性。

重构历史:
- v1.0: 初始版本
- v2.0: 方法体行数重构，遵循Python开发规范
        - 将 `ProgressMonitorCallback.on_epoch_end` 分解为多个独立的分析和日志记录方法
        - 将 `BoundaryProtectionCallback` 的逻辑拆分为更小的辅助函数
        - 优化 `CallbackManager` 的事件分发逻辑
        - 提高代码可读性和可维护性
"""

import time
import sys
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from tqdm import tqdm
import torch
import torch.nn.functional as F
from utils import log_utils
from utils.feature_norm import l2_normalize
from config import (
    DEFAULT_TRAINING_CALLBACKS_CONFIG,
    DEFAULT_VAL_CONFIG,
    DEFAULT_STOPPING_CONFIG
)


@dataclass
class TrainingState:
    """训练状态数据类"""
    epoch: int
    batch_idx: int
    total_batches: int
    loss: float
    metrics: Dict[str, Any]
    model_state: Optional[Dict] = None


@dataclass
class BoundaryProtectionState:
    """边界保护状态数据类"""
    active: bool = False
    target_ratio: float = 0.20
    min_samples: int = 20
    protection_count: int = 0
    last_activation: int = 0
    threshold_similarity: float = 0.55


class TrainingCallback(ABC):
    """训练回调接口"""
    
    @abstractmethod
    def on_epoch_start(self, state: TrainingState) -> None:
        pass
    
    @abstractmethod
    def on_batch_start(self, state: TrainingState) -> None:
        pass
    
    @abstractmethod
    def on_batch_end(self, state: TrainingState) -> None:
        pass
    
    @abstractmethod
    def on_epoch_end(self, state: TrainingState) -> None:
        pass


class ProgressMonitorCallback(TrainingCallback):
    """进度监控回调"""
    
    def __init__(self, config: Optional[dict] = None):
        # 使用现有的TrainConfig配置
        default_config = {
            'log_interval': DEFAULT_TRAINING_CALLBACKS_CONFIG.log_interval,
            'progress_interval': DEFAULT_TRAINING_CALLBACKS_CONFIG.progress_interval,
            'metric_precision': 3,
            'loss_precision': 4
        }
        self.config = type('Config', (), {**default_config, **(config or {})})()
        self.progress_bar: Optional[tqdm] = None
        self.start_time: Optional[float] = None
        self.prev_quality_score: float = 0.0
        self.prev_fisher_score: float = 0.0
        self.prev_separation_ratio: float = 0.0
        self.epoch_count: int = 0
    
    def on_epoch_start(self, state: TrainingState) -> None:
        self._initialize_progress_bar(state)
    
    def _initialize_progress_bar(self, state: TrainingState):
        """初始化或重置轮次进度条 - 已禁用，避免与主进度条冲突"""
        self.start_time = time.time()
        # 🔧 禁用callback进度条，避免与主训练循环的tqdm进度条冲突
        # 主训练循环已经有完整的进度条显示，无需重复创建
        self.progress_bar = None
        log_utils.debug(f"跳过创建callback进度条，使用主训练循环进度条 (Epoch {state.epoch})", tag="TRAINING_CALLBACKS")

    def on_batch_start(self, state: TrainingState) -> None:
        pass
    
    def on_batch_end(self, state: TrainingState) -> None:
        # 🔧 由于禁用了callback进度条，这里只处理日志记录
        # 进度条更新由主训练循环处理
        if state.batch_idx % self.config.progress_interval == 0:
            self._log_batch_progress(state)

    def _log_batch_progress(self, state: TrainingState):
        """记录详细的批次进度日志"""
        progress_pct = state.batch_idx / state.total_batches * 100
        log_utils.debug(f"Epoch {state.epoch} 训练进度: {progress_pct:.1f}% "
                      f"({state.batch_idx}/{state.total_batches}) - "
                      f"Loss: {state.loss:.{self.config.loss_precision}f}", tag="TRAINING_CALLBACKS")

    def on_epoch_end(self, state: TrainingState) -> None:
        log_utils.info(f"📊 Epoch {state.epoch} 训练完成分析:", tag="TRAINING_CALLBACKS")
        self._log_basic_info(state)
        
        metrics_map = {
            'feature_quality_score': self._log_feature_quality,
            'fisher_score': self._log_fisher_analysis,
            'avg_inter_dist': self._log_distance_analysis,
            'feature_std': self._log_feature_health,
            'boundary_result': self._log_boundary_analysis,
        }
        
        for key, logger in metrics_map.items():
            if key in state.metrics:
                logger(state)
        
        self._log_overall_assessment(state)
        log_utils.info(f"✅ Epoch {state.epoch} 分析完成", tag="TRAINING_CALLBACKS")
        self.epoch_count += 1

    def _log_basic_info(self, state: TrainingState):
        """记录基础训练信息"""
        log_utils.info(f"  ┌─ 基础训练指标:", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  ├─ 当前损失: {state.loss:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  └─ 批次总数: {state.batch_idx + 1}", tag="TRAINING_CALLBACKS")

    def _log_feature_quality(self, state: TrainingState):
        """记录特征质量分析"""
        score = state.metrics['feature_quality_score']
        log_utils.info(f"  ├─ 特征质量综合得分: {score:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        
        change = score - self.prev_quality_score
        trend = "📈 上升" if change > 0 else "📉 下降" if change < 0 else "➡️ 持平"
        log_utils.info(f"  ├─ 质量变化趋势: {trend} ({change:+.{self.config.metric_precision}f})", tag="TRAINING_CALLBACKS")
        self.prev_quality_score = score
        
        level, advice = self._get_quality_level_and_advice(score)
        log_utils.info(f"  ├─ 质量等级: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  └─ 优化建议: {advice}", tag="TRAINING_CALLBACKS")

    def _get_quality_level_and_advice(self, score: float) -> Tuple[str, str]:
        """根据分数获取质量等级和建议"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的quality_*_threshold配置
        if score > cfg.quality_excellent_threshold:
            return "🟢 优秀", "特征空间结构优异，继续保持"
        if score > cfg.quality_good_threshold:
            return "🟡 良好", "特征空间结构良好，可适当调整学习率"
        if score > cfg.quality_baseline_threshold:
            return "🟠 基线", "特征空间结构基本合理，建议增强数据增强"
        if score > cfg.quality_poor_threshold:
            return "🔴 较差", "特征空间结构需要改进，考虑调整损失权重"
        return "⚫ 很差", "特征空间结构严重不合理，建议重新设计架构"

    def _log_fisher_analysis(self, state: TrainingState):
        """记录Fisher判别分析"""
        score = state.metrics['fisher_score']
        log_utils.info(f"  ├─ Fisher判别分数: {score:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        
        change = score - self.prev_fisher_score
        trend = "📈 增强" if change > 0 else "📉 减弱" if change < 0 else "➡️ 稳定"
        log_utils.info(f"  ├─ Fisher判别趋势: {trend} ({change:+.{self.config.metric_precision}f})", tag="TRAINING_CALLBACKS")
        self.prev_fisher_score = score
        
        level, advice = self._get_fisher_level_and_advice(score)
        log_utils.info(f"  ├─ Fisher判别能力: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  └─ Fisher优化建议: {advice}", tag="TRAINING_CALLBACKS")

    def _get_fisher_level_and_advice(self, score: float) -> Tuple[str, str]:
        """根据Fisher分数获取等级和建议"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的fisher_*_threshold配置
        if score > cfg.fisher_excellent_threshold:
            return "🟢 极强", "类间类内分离度极佳，模型收敛良好"
        if score > cfg.fisher_good_threshold:
            return "🟡 良好", "类间类内分离度良好，可考虑增加难样本挖掘"
        if score > cfg.fisher_baseline_threshold:
            return "🟠 基线", "类间类内分离度基本合理，建议调整边界样本策略"
        if score > cfg.fisher_weak_threshold:
            return "🔴 较弱", "类间类内分离度较弱，需要增强特征学习"
        return "⚫ 很差", "类间类内分离度很差，建议重新审视损失函数设计"

    def _log_distance_analysis(self, state: TrainingState):
        """记录距离分析"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的separation_*_threshold配置
        inter_dist = state.metrics['avg_inter_dist']
        intra_dist = state.metrics['avg_intra_dist']
        # 使用统一距离计算器
        from utils.distance_calculator import compute_separation_ratio
        ratio = compute_separation_ratio(intra_dist, inter_dist)
        
        log_utils.info(f"  ├─ 特征空间距离分析:", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 平均类间距离: {inter_dist:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 平均类内距离: {intra_dist:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  └─ 分离比 (Inter/Intra): {ratio:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        
        change = ratio - self.prev_separation_ratio
        trend = "📈 改善" if change > 0 else "📉 恶化" if change < 0 else "➡️ 稳定"
        log_utils.info(f"  │  ├─ 分离比趋势: {trend} ({change:+.{self.config.metric_precision}f})", tag="TRAINING_CALLBACKS")
        self.prev_separation_ratio = ratio
        
        level, advice = self._get_separation_level_and_advice(ratio)
        log_utils.info(f"  │  ├─ 分离比评估: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  └─ 分离比建议: {advice}", tag="TRAINING_CALLBACKS")

    def _get_separation_level_and_advice(self, ratio: float) -> Tuple[str, str]:
        """根据分离比获取等级和建议"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的separation_*_threshold配置
        if ratio > cfg.separation_excellent_threshold:
            return "🟢 优秀", "类间类内距离比例优异"
        if ratio > cfg.separation_good_threshold:
            return "🟡 良好", "类间类内距离比例良好"
        if ratio > cfg.separation_baseline_threshold:
            return "🟠 基线", "类间类内距离比例基本合理"
        if ratio > cfg.separation_poor_threshold:
            return "🔴 较差", "类间类内距离比例需要改进"
        return "⚫ 很差", "类间类内距离比例严重不合理"

    def _log_feature_health(self, state: TrainingState):
        """记录特征空间健康度"""
        std = state.metrics['feature_std']
        mean_norm = state.metrics['feature_mean_norm']
        log_utils.info(f"  ├─ 特征空间健康度分析:", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 特征标准差: {std:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 特征均值范数: {mean_norm:.{self.config.metric_precision}f}", tag="TRAINING_CALLBACKS")
        
        level, advice = self._get_health_level_and_advice(std, mean_norm)
        log_utils.info(f"  │  ├─ 健康度评估: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  └─ 健康度建议: {advice}", tag="TRAINING_CALLBACKS")

    def _get_health_level_and_advice(self, std: float, mean_norm: float) -> Tuple[str, str]:
        """根据特征统计数据评估健康度 - 使用统一健康度评估模块"""
        from utils.feature_space_health import evaluate_feature_statistics_health
        
        level, desc = evaluate_feature_statistics_health(std, mean_norm)
        return level.value[0], desc

    def _log_boundary_analysis(self, state: TrainingState):
        """记录边界样本分析"""
        result = state.metrics['boundary_result']
        ratio = result.unified_boundary_ratio
        log_utils.info(f"  ├─ 边界样本分析:", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 边界样本对数: {result.boundary_count}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  ├─ 总样本对数: {result.unified_total_count}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  └─ 边界样本比例: {ratio:.4f}", tag="TRAINING_CALLBACKS")
        
        level, advice = self._get_boundary_level_and_advice(ratio)
        log_utils.info(f"  │  ├─ 边界样本评估: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"  │  └─ 边界样本建议: {advice}", tag="TRAINING_CALLBACKS")

    def _get_boundary_level_and_advice(self, ratio: float) -> Tuple[str, str]:
        """根据边界样本比例评估并提供建议 - 使用统一健康度评估模块"""
        from utils.feature_space_health import evaluate_boundary_health
        
        level, desc = evaluate_boundary_health(ratio)
        return level.value[0], desc

    def _log_overall_assessment(self, state: TrainingState):
        """记录训练状态综合评估"""
        health_score, score_components = self._calculate_health_score(state)
        if not score_components:
            return
            
        log_utils.info(f"  └─ 训练状态综合评估:", tag="TRAINING_CALLBACKS")
        log_utils.info(f"     ├─ 综合健康度得分: {health_score:.4f}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"     ├─ 得分构成: {' + '.join(score_components)}", tag="TRAINING_CALLBACKS")
        
        level, advice = self._get_overall_level_and_advice(health_score)
        log_utils.info(f"     ├─ 综合评估等级: {level}", tag="TRAINING_CALLBACKS")
        log_utils.info(f"     └─ 综合优化建议: {advice}", tag="TRAINING_CALLBACKS")

    def _calculate_health_score(self, state: TrainingState) -> Tuple[float, List[str]]:
        """计算综合健康度得分"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的配置
        health_score = 0.0
        components = []
        
        if 'feature_quality_score' in state.metrics:
            comp = min(state.metrics['feature_quality_score'], 1.0)
            health_score += comp * cfg.quality_score_weight
            components.append(f"质量({comp:.2f}×{cfg.quality_score_weight})")
        
        if 'fisher_score' in state.metrics:
            comp = min(state.metrics['fisher_score'] / cfg.fisher_normalization_factor, 1.0)
            health_score += comp * cfg.fisher_score_weight
            components.append(f"Fisher({comp:.2f}×{cfg.fisher_score_weight})")

        if 'avg_inter_dist' in state.metrics:
            # 使用统一距离计算器
            from utils.distance_calculator import compute_separation_ratio
            ratio = compute_separation_ratio(state.metrics['avg_intra_dist'], state.metrics['avg_inter_dist'])
            comp = min(ratio / cfg.separation_normalization_factor, 1.0)
            health_score += comp * cfg.separation_score_weight
            components.append(f"分离比({comp:.2f}×{cfg.separation_score_weight})")
            
        return health_score, components

    def _get_overall_level_and_advice(self, score: float) -> Tuple[str, str]:
        """获取综合评估等级和建议"""
        cfg = DEFAULT_VAL_CONFIG  # 使用ValConfig中的overall_*_threshold配置
        if score > cfg.overall_excellent_threshold:
            return "🟢 优秀", "训练状态优异，继续当前策略"
        if score > cfg.overall_good_threshold:
            return "🟡 良好", "训练状态良好，可考虑微调参数"
        if score > cfg.overall_baseline_threshold:
            return "🟠 基线", "训练状态基本正常，建议优化策略"
        return "🔴 需要改进", "训练状态需要改进，建议调整超参数"


class BoundaryProtectionCallback(TrainingCallback):
    """边界样本保护回调"""
    
    def __init__(self, config: Optional[dict] = None):
        # 使用现有的StoppingConfig配置
        default_config = {
            'boundary_threshold': DEFAULT_STOPPING_CONFIG.boundary_threshold,
            'protection_enabled': True,
            'target_ratio': 0.20,
            'threshold_similarity': 0.55,
            'min_samples': 20,
            'adjustment_check_interval': 5,
            'long_term_protection_threshold': 10,
            'threshold_adjustment_step_large': 0.05,
            'threshold_adjustment_step_small': 0.02,
            'threshold_relaxed_limit': 0.3,
            'quality_analysis_min_samples': 10,
            'threshold_min_limit': 0.2,
            'threshold_protection_step': 0.03,
            'threshold_max_limit': 0.8,
            'threshold_recovery_step': 0.02
        }
        self.config = type('Config', (), {**default_config, **(config or {})})()
        self.protection_state = BoundaryProtectionState(
            target_ratio=self.config.target_ratio,
            min_samples=self.config.min_samples,
            threshold_similarity=self.config.threshold_similarity
        )
        self.last_boundary_count = 0
    
    def on_epoch_start(self, state: TrainingState) -> None:
        if state.epoch > 0 and state.epoch % self.config.adjustment_check_interval == 0:
            self._adjust_threshold_proactively(state.epoch)
    
    def on_batch_start(self, state: TrainingState) -> None:
        pass
    
    def on_batch_end(self, state: TrainingState) -> None:
        if 'boundary_result' in state.metrics and state.batch_idx == state.total_batches - 1:
            self._log_boundary_quality(state.metrics['boundary_result'], state)
    
    def on_epoch_end(self, state: TrainingState) -> None:
        if 'boundary_result' in state.metrics:
            self._evaluate_and_update_protection(state.metrics['boundary_result'], state.epoch)
            boundary_result = state.metrics['boundary_result']
            self.last_boundary_count = boundary_result.boundary_count if boundary_result else 0
        else:
            self.last_boundary_count = 0
    
    def _adjust_threshold_proactively(self, epoch: int):
        """根据训练进展动态调整边界样本阈值"""
        state = self.protection_state
        is_long_term_protection = state.active and epoch - state.last_activation > self.config.long_term_protection_threshold
        is_preventive_adjustment = not state.active and self.last_boundary_count < 15

        if is_long_term_protection or is_preventive_adjustment:
            step = self.config.threshold_adjustment_step_large if is_long_term_protection else self.config.threshold_adjustment_step_small
            state.threshold_similarity = max(
                self.config.threshold_relaxed_limit, state.threshold_similarity - step
            )
            log_utils.info(f"放宽边界样本判定: 相似度阈值调整为 {state.threshold_similarity:.2f}", tag="TRAINING_CALLBACKS")

    def _log_boundary_quality(self, boundary_result: Any, state: TrainingState):
        """记录边界样本质量"""
        if boundary_result.boundary_count >= self.config.quality_analysis_min_samples:
            log_utils.info(f"边界样本质量分析 (Epoch {state.epoch}): "
                          f"数={boundary_result.boundary_count}, "
                          f"比例={boundary_result.boundary_ratio:.3f}", tag="TRAINING_CALLBACKS")

    def _evaluate_and_update_protection(self, boundary_result: Any, epoch: int):
        """评估并更新保护状态"""
        needs_protection, reason = self._check_if_protection_needed(boundary_result, epoch)
        
        if needs_protection and not self.protection_state.active:
            self._activate_protection(epoch, reason)
        elif not needs_protection and self.protection_state.active:
            self._deactivate_protection(boundary_result)

    def _check_if_protection_needed(self, boundary_result: Any, epoch: int) -> Tuple[bool, str]:
        """检查是否需要激活边界保护"""
        state = self.protection_state
        count = boundary_result.boundary_count
        ratio = boundary_result.boundary_ratio
        
        if count < state.min_samples:
            return True, f"数量({count})低于最低要求({state.min_samples})"
        if ratio < state.target_ratio:
            return True, f"比例({ratio:.2%})低于目标({state.target_ratio:.2%})"
        return False, ""

    def _activate_protection(self, epoch: int, reason: str):
        """激活保护机制"""
        state = self.protection_state
        state.active = True
        state.protection_count += 1
        state.last_activation = epoch
        
        old_threshold = state.threshold_similarity
        state.threshold_similarity = max(
            self.config.threshold_min_limit, 
            old_threshold - self.config.threshold_protection_step
        )
        
        log_utils.warning(f"边界保护激活(第{state.protection_count}次): {reason}", tag="TRAINING_CALLBACKS")
        log_utils.warning(f"降低判定阈值: {old_threshold:.2f}→{state.threshold_similarity:.2f}", tag="TRAINING_CALLBACKS")

    def _deactivate_protection(self, boundary_result: Any):
        """解除保护机制"""
        state = self.protection_state
        state.active = False
        
        old_threshold = state.threshold_similarity
        state.threshold_similarity = min(
            self.config.threshold_max_limit, 
            old_threshold + self.config.threshold_recovery_step
        )
        
        log_utils.info(f"边界保护解除: 当前样本数={boundary_result.boundary_count}", tag="TRAINING_CALLBACKS")


class CallbackManager:
    """回调管理器"""
    
    def __init__(self, config: Optional[dict] = None):
        # 使用简单的回调管理器配置
        default_config = {
            'max_callbacks': 10,
            'enable_logging': True,
            'enable_error_logging': True,
            'max_error_count': 5,
            'continue_on_error': False
        }
        self.config = type('Config', (), {**default_config, **(config or {})})()
        self.callbacks: List[TrainingCallback] = []
        self.error_count = 0

    def add_callback(self, callback: TrainingCallback):
        self.callbacks.append(callback)

    def on_epoch_start(self, state: TrainingState) -> None:
        self._execute_callbacks("on_epoch_start", state)
    
    def on_batch_start(self, state: TrainingState) -> None:
        self._execute_callbacks("on_batch_start", state)
    
    def on_batch_end(self, state: TrainingState) -> None:
        self._execute_callbacks("on_batch_end", state)
    
    def on_epoch_end(self, state: TrainingState) -> None:
        self._execute_callbacks("on_epoch_end", state)

    def _execute_callbacks(self, phase: str, state: TrainingState):
        """统一执行指定阶段的回调"""
        for callback in self.callbacks:
            try:
                getattr(callback, phase)(state)
            except Exception as e:
                self._handle_callback_error(callback, phase, e)
    
    def _handle_callback_error(self, callback: TrainingCallback, phase: str, error: Exception):
        """处理回调错误"""
        self.error_count += 1
        
        if self.config.enable_error_logging:
            log_utils.error(f"回调 {callback.__class__.__name__} 在 {phase} 时出错: {error}", tag="TRAINING_CALLBACKS")
        
        if self.error_count >= self.config.max_error_count and not self.config.continue_on_error:
            raise RuntimeError(f"回调错误次数达到上限({self.config.max_error_count})，停止执行")


def create_default_callbacks(config: Optional[dict] = None) -> List[TrainingCallback]:
    """创建默认的训练回调列表"""
    # 直接创建回调，使用默认配置
    return [
        ProgressMonitorCallback(),
        BoundaryProtectionCallback(),
    ] 