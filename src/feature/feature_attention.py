import torch.nn as nn

class FeatureAttention(nn.Module):
    """通道注意力机制: 结合平均池化和最大池化增强特征表达"""
    def __init__(self, in_features, reduction=16):
        super().__init__()
        # 全局池化层
        self.avg_pool = nn.AdaptiveAvgPool1d(1) 
        self.max_pool = nn.AdaptiveMaxPool1d(1)
        
        # 两层全连接网络进行特征变换
        self.fc = nn.Sequential(
            nn.Linear(in_features, in_features // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_features // reduction, in_features, bias=False)
        )
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        b, c = x.size()
        x_reshape = x.unsqueeze(-1)  # 增加维度以适配池化层
        
        # 分别计算平均池化和最大池化的注意力权重
        avg_out = self.fc(self.avg_pool(x_reshape).view(b, c))
        max_out = self.fc(self.max_pool(x_reshape).view(b, c))
        
        # 融合注意力并应用于输入特征
        out = self.sigmoid(avg_out + max_out)
        return x * out