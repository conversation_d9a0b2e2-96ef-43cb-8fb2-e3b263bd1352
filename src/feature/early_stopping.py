from utils import log_utils
import dataclasses
from typing import Dict, List, Tuple, Optional, Any, Union
from enum import Enum
import math

# 导入早停相关配置
from config import DEFAULT_STOPPING_CONFIG, StoppingConfig

class StopReason(Enum):
    """早停原因枚举"""
    NO_IMPROVEMENT = "连续多轮无显著改进"
    LOSS_INCREASE = "验证损失持续上升"
    LOW_BOUNDARY_SAMPLES = "边界样本持续低于阈值"
    HIGH_STAGNATION = "训练停滞无实质进展"
    NEGATIVE_TREND = "检测到连续负面趋势"
    STABLE_VAL_BOUNDARY = "验证集边界样本稳定在低水平"
    SEPARATION_STAGNATION = "特征分离比不再改进"
    COMBINED_TREND = "负面趋势与边界样本稳定性共同表明收敛"
    WEIGHT_STABILITY = "损失权重已稳定且验证损失接近最佳"
    LOW_IMPROVEMENT_RATE = "近期平均改进率低于阈值"

    GENERALIZATION_DECLINE = "泛化能力达上限, 边界信息塌缩"

@dataclasses.dataclass
class GeneralizationStatus:
    """泛化能力状态结构体"""
    inter_dist_declined: bool = False
    boundary_samples_low: bool = False
    met_count: int = 0

    def is_declining(self) -> bool:
        return self.met_count >= 2

# 早停机制，综合多指标判断是否停止训练
class EarlyStopping:
    """综合多指标的早停机制"""

    def __init__(self, 
                 config: Optional[StoppingConfig] = None,
                 threshold_config: Optional[StoppingConfig] = None,
                 algorithm_config: Optional[StoppingConfig] = None,
                 patience=None, delta=None, min_epochs=None,
                 boundary_threshold=None, weight_stability_epochs=None,
                 improvement_rate_threshold=None, loss_patience=None,
                 separation_improvement_threshold=None, weight_change_threshold=None,
                 inter_distance_decline_threshold=None, intra_distance_threshold=None,
                 inter_intra_safety_ratio=None,
                 pseudo_improve_circle_boost=None, pseudo_improve_center_boost=None,
                 adaptive_patience=None, performance_plateau_threshold=None,
                 overfit_detection_window=None, generalization_gap_threshold=None):
        self._init_configs(config, threshold_config, algorithm_config)
        self._handle_legacy_params(locals())
        self._init_core_params()
        self._init_state_variables()
        self._init_history_tracking()
        self._init_detection_params()
        self._init_intelligent_stopping()

    def _init_configs(self, config, threshold_config, algorithm_config):
        """初始化配置"""
        self.config = config if config is not None else DEFAULT_STOPPING_CONFIG
        self.threshold_config = threshold_config if threshold_config is not None else DEFAULT_STOPPING_CONFIG
        self.algorithm_config = algorithm_config if algorithm_config is not None else DEFAULT_STOPPING_CONFIG

    def _handle_legacy_params(self, params):
        """兼容旧参数"""
        legacy_params = [
            'patience', 'delta', 'min_epochs', 'boundary_threshold', 
            'weight_stability_epochs', 'improvement_rate_threshold', 
            'loss_patience', 'separation_improvement_threshold', 
            'weight_change_threshold', 'inter_distance_decline_threshold', 
            'intra_distance_threshold', 'inter_intra_safety_ratio',
            'pseudo_improve_circle_boost', 'pseudo_improve_center_boost',
            'adaptive_patience', 'performance_plateau_threshold',
            'overfit_detection_window', 'generalization_gap_threshold'
        ]
        if any(params.get(param) is not None for param in legacy_params):
            self._apply_legacy_overrides(params, legacy_params)

    def _apply_legacy_overrides(self, params, legacy_params):
        """应用旧参数覆盖"""
        log_utils.info("EarlyStopping - 使用自定义参数覆盖默认配置", tag="EARLY_STOPPING")
        self.config = dataclasses.replace(self.config)
        for param in legacy_params:
            if params.get(param) is not None:
                if param == 'min_epochs':
                    # min_epochs只设置到实例属性
                    self.min_epochs = params[param]
                else:
                    setattr(self.config, param, params[param])
        log_utils.debug(f"EarlyStopping - 参数覆盖完成: patience={self.config.patience}", tag="EARLY_STOPPING")

    def _init_core_params(self):
        """初始化核心参数"""
        self.patience = self.config.patience
        self.delta = self.config.delta
        # min_epochs优先外部传入，否则用默认值
        if not hasattr(self, 'min_epochs'):
            self.min_epochs = 50
        self.boundary_threshold = self.config.boundary_threshold
        self.weight_stability_epochs = self.config.weight_stability_epochs
        self.improvement_rate_threshold = self.config.improvement_rate_threshold
        self.loss_patience = self.config.loss_patience
        self.separation_improvement_threshold = self.config.separation_improvement_threshold
        self.weight_change_threshold = self.config.weight_change_threshold
        self.inter_distance_decline_threshold = self.config.inter_distance_decline_threshold
        self.intra_distance_threshold = self.config.intra_distance_threshold
        self.inter_intra_safety_ratio = self.config.inter_intra_safety_ratio

    def _init_state_variables(self):
        """初始化状态变量"""
        # 伪提升检测参数
        self.pseudo_improve_circle_boost = self.config.pseudo_improve_circle_boost
        self.pseudo_improve_center_boost = self.config.pseudo_improve_center_boost
        self.pseudo_improvement_detected = False
        self.pseudo_improvement_count = 0
        self.weight_adjustment_needed = False
        self.weight_adjustment_suggestions = {}

        # 最佳状态
        self.best_score = None
        self.best_val_acc = 0
        self.best_separation = 0
        self.best_inter_distance = 0
        self.best_intra_distance = float('inf')
        self.best_epoch = 0
        self.best_model_state = None
        self.best_feature_quality_score = 0
        self.current_weights = None
        self.best_weights = None
        self.counter = 0

        # 最佳模型存储
        self.best_feature_quality_model = {}
        self.best_accuracy_model = {}
        self.best_separation_model = {}

        # 早停状态
        self.early_stop = False
        self.stop_reason = None
        self.early_stop_reason = None
        self.trigger_early_stop_flag = False
        self.last_model_improved = False

    def _init_history_tracking(self):
        """初始化历史记录"""
        self.val_acc_history = []
        self.separation_history = []
        self.weight_history = []
        self.boundary_history = []
        self.val_boundary_history = []
        self.val_separation_history = []
        self.inter_distance_history = []
        self.intra_distance_history = []
        self.feature_quality_scores = []
        self.improvement_rates = []

    def _init_detection_params(self):
        """初始化检测参数"""
        # 阈值参数
        self.val_boundary_ratio_threshold = self.threshold_config.val_boundary_ratio_threshold
        self.boundary_stability_counter = 0
        self.boundary_stability_threshold = self.threshold_config.boundary_stability_threshold
        self.min_improvement_rate = self.threshold_config.min_improvement_rate

        # 停滞检测参数
        self.stagnation_level = 0
        self.max_stagnation = self.threshold_config.max_stagnation
        self.peak_performance_counter = getattr(self.threshold_config, 'peak_performance_counter_init', 0)

        # 趋势检测参数
        self.trend_window = self.threshold_config.trend_window
        self.negative_trend_counter = 0
        self.max_negative_trends = self.threshold_config.max_negative_trends

        # 日志控制参数
        self._last_warning_epoch = self.threshold_config.last_warning_epoch_init
        self.warning_mode = False

        # 泛化约束早停参数
        self.patience_val_epoch = self.threshold_config.patience_val_epoch
        self.patience_sep_epoch = self.threshold_config.patience_sep_epoch
        self.patience_inter_epoch = self.threshold_config.patience_inter_epoch
        self.min_boundary_threshold = self.threshold_config.min_boundary_threshold
        self.inter_distance_decline_count = 0
        self.boundary_low_count = 0

    def _init_intelligent_stopping(self):
        """初始化智能早停参数"""
        self.adaptive_patience = self.config.adaptive_patience
        self.performance_plateau_threshold = self.config.performance_plateau_threshold
        self.overfit_detection_window = self.config.overfit_detection_window
        self.generalization_gap_threshold = self.config.generalization_gap_threshold
        self.dynamic_patience = self.config.patience
        # 智能状态跟踪
        self.consecutive_plateau_epochs = 0
        self.performance_stability_score = 0.0
        self.overfit_confidence = 0.0
        self.true_convergence_detected = False

    def val_acc_stable(self, epoch):
        """判断验证准确率是否稳定"""
        if len(self.val_acc_history) <= self.patience_val_epoch:
            return False
        recent_accs = self.val_acc_history[-self.patience_val_epoch:]
        return self._is_acc_fluctuation_stable(recent_accs) and self._is_close_to_best_acc(recent_accs)

    def _is_acc_fluctuation_stable(self, recent_accs):
        """判断准确率波动是否稳定"""
        min_recent_acc = min(recent_accs)
        max_recent_acc = max(recent_accs)
        fluctuation = (max_recent_acc - min_recent_acc) / max(0.001, max_recent_acc)
        return fluctuation < self.algorithm_config.val_acc_fluctuation_threshold

    def _is_close_to_best_acc(self, recent_accs):
        """判断是否接近最佳准确率"""
        max_recent_acc = max(recent_accs)
        threshold = self.best_val_acc * (2.0 - self.algorithm_config.best_loss_gap_threshold)
        return max_recent_acc > threshold



    def inter_distance_decline(self):
        """判断类间距离是否持续下降"""
        if len(self.inter_distance_history) < self.patience_inter_epoch:
            return False
        recent_distances = self.inter_distance_history[-self.patience_inter_epoch:]
        is_declining = self._is_distance_declining(recent_distances)
        if is_declining:
            self.inter_distance_decline_count += 1
            decline_percent = self._calculate_decline_percent(recent_distances)
            is_significant = decline_percent > self.algorithm_config.significant_decline_threshold
            return self.inter_distance_decline_count >= 1 and is_significant
        else:
            self.inter_distance_decline_count = 0
            return False

    def _is_distance_declining(self, distances):
        """判断距离序列是否持续下降"""
        return all(distances[i] >= distances[i+1] for i in range(len(distances)-1))

    def _calculate_decline_percent(self, distances):
        """计算下降百分比"""
        return (distances[0] - distances[-1]) / max(0.001, distances[0])

    def boundary_samples_low_for_long(self):
        """判断边界样本数量是否持续低于阈值"""
        if len(self.boundary_history) < self.algorithm_config.boundary_observation_window:
            return False
        recent_samples = self.boundary_history[-self.algorithm_config.boundary_observation_window:]
        is_low = self._is_boundary_samples_low(recent_samples)
        if is_low:
            self.boundary_low_count += 1
            return self.boundary_low_count >= self.algorithm_config.consecutive_observation_threshold
        else:
            self.boundary_low_count = 0
            return False

    def _is_boundary_samples_low(self, samples):
        """判断边界样本大部分时间低于阈值"""
        low_samples_count = sum(1 for sample in samples if sample <= self.min_boundary_threshold)
        threshold_count = int(len(samples) * self.algorithm_config.boundary_low_ratio_threshold)
        return low_samples_count >= threshold_count

    def detect_generalization_decline(self, epoch) -> Tuple[bool, List[str]]:
        """统一检测泛化能力衰退指标"""
        if epoch < self.min_epochs:
            return False, []
        decline_indicators = self._check_decline_indicators(epoch)
        conditions_met = self._build_condition_messages(decline_indicators)
        is_declining = len(conditions_met) >= 2
        return is_declining, conditions_met

    def _check_decline_indicators(self, epoch):
        """检查各项衰退指标"""
        return {
            'val_acc_stagnant': self.val_acc_stable(epoch),
            'inter_dist_declined': self.inter_distance_decline(),
            'boundary_samples_low': self.boundary_samples_low_for_long()
        }

    def _build_condition_messages(self, indicators):
        """构建满足条件的消息列表"""
        conditions_met = []
        if indicators['val_acc_stagnant']:
            conditions_met.append(f"验证准确率停滞 >= {self.patience_val_epoch}轮")
        if indicators['inter_dist_declined']:
            conditions_met.append(self._get_inter_distance_message())
        if indicators['boundary_samples_low']:
            conditions_met.append(f"边界样本持续低于阈值{self.min_boundary_threshold}")
        return conditions_met

    def _get_inter_distance_message(self):
        """获取类间距离下降信息"""
        if len(self.inter_distance_history) >= self.patience_inter_epoch:
            recent_distances = self.inter_distance_history[-self.patience_inter_epoch:]
            decline_percent = self._calculate_decline_percent(recent_distances) * 100
            return f"类间距离下降{decline_percent:.1f}%"
        else:
            return "类间距离持续下降"

    def trigger_early_stop(self, conditions_met):
        """触发早停并记录原因"""
        if not conditions_met:
            return False
        self._log_early_stop_conditions(conditions_met)
        self._set_early_stop_state()
        return True

    def _log_early_stop_conditions(self, conditions_met):
        """记录早停条件"""
        log_utils.warning("检测到特征空间退化趋势:", tag="EARLY_STOPPING")
        for condition in conditions_met:
            log_utils.warning(f"- {condition}", tag="EARLY_STOPPING")
        current_epoch = len(self.val_acc_history)
        log_utils.warning(f"✓ 满足早停标准，已训练至性能上限，自动终止训练于第{current_epoch}轮。", tag="EARLY_STOPPING")

    def _set_early_stop_state(self):
        """设置早停状态"""
        self.early_stop = True
        self.stop_reason = StopReason.GENERALIZATION_DECLINE.value
        self.early_stop_reason = "泛化能力达上限, 边界信息塌缩, 终止训练"
        self.trigger_early_stop_flag = True

    def update_best_if_improved(self, val_acc, model, separation_ratio, epoch,
                  weights=None, val_boundary_info=None,
                  boundary_result: Optional[Any] = None, shared_quality_score=None):
        """模型更新逻辑，集成智能早停"""
        # 保存共享的质量分数，避免重复计算
        self.shared_quality_score = shared_quality_score
        
        # 记录基础指标
        self._record_basic_metrics(val_acc, separation_ratio, weights, boundary_result, val_boundary_info)
        # 检查最小训练轮数
        if epoch < self.min_epochs:
            return False
        # 智能检测与动态调整
        if self._perform_intelligent_detection(epoch):
            return False
        # 评估特征质量并更新模型
        return self._evaluate_and_update_model(val_acc, model, separation_ratio, epoch, val_boundary_info)

    def _record_basic_metrics(self, val_acc, separation_ratio, weights, boundary_result, val_boundary_info):
        """记录基础指标"""
        self.current_weights = weights
        self._append_to_history('val_acc_history', val_acc)
        self._append_to_history('separation_history', separation_ratio)
        # 记录边界样本
        self._handle_boundary_data(boundary_result, val_boundary_info)
        # 记录距离
        self._handle_distance_data(val_boundary_info)
        # 记录验证集指标
        self._handle_validation_metrics(val_boundary_info)
        # 记录权重历史
        if weights:
            self._append_to_history('weight_history', weights)

    def _handle_boundary_data(self, boundary_result, val_boundary_info):
        """记录边界样本数据"""
        if boundary_result:
            self._append_to_history('boundary_history', boundary_result.boundary_count)
        else:
            boundary_samples = val_boundary_info.get('boundary_samples', 0) if val_boundary_info else 0
            self._append_to_history('boundary_history', boundary_samples)

    def _handle_distance_data(self, val_boundary_info):
        """记录距离数据 - 🔧 修复：使用正确的键名映射"""
        if not val_boundary_info:
            inter_distance = 0
            intra_distance = 1
        else:
            # 🔧 修复：验证引擎返回的键名是avg_inter_dist和avg_intra_dist，不是inter_distance和intra_distance
            inter_distance = val_boundary_info.get('avg_inter_dist', val_boundary_info.get('inter_distance', 0))
            intra_distance = val_boundary_info.get('avg_intra_dist', val_boundary_info.get('intra_distance', 1))
            
        log_utils.debug(f"_handle_distance_data: inter_distance={inter_distance}, intra_distance={intra_distance}", tag="EARLY_STOPPING")
        
        self._append_to_history('inter_distance_history', inter_distance)
        self._append_to_history('intra_distance_history', intra_distance)
        # 更新最佳距离
        if inter_distance > self.best_inter_distance:
            self.best_inter_distance = inter_distance
        if intra_distance < self.best_intra_distance:
            self.best_intra_distance = intra_distance

    def _handle_validation_metrics(self, val_boundary_info):
        """记录验证集指标"""
        log_utils.debug(f"_handle_validation_metrics called with val_boundary_info: {val_boundary_info}", tag="EARLY_STOPPING")
        if not val_boundary_info:
            log_utils.debug("val_boundary_info 为空，提前返回", tag="EARLY_STOPPING")
            return
        
        # 🔧 修复：使用验证引擎实际返回的键名
        val_boundary_ratio = val_boundary_info.get('boundary_ratio', 0)  # 修复键名
        val_separation_ratio = val_boundary_info.get('separation_ratio', 0)  # 修复键名
        
        log_utils.debug(f"添加到历史: val_boundary_ratio={val_boundary_ratio}, val_separation_ratio={val_separation_ratio}", tag="EARLY_STOPPING")
        self._append_to_history('val_boundary_history', val_boundary_ratio)
        self._append_to_history('val_separation_history', val_separation_ratio)
        log_utils.debug(f"添加后: val_boundary_history 长度={len(self.val_boundary_history)}", tag="EARLY_STOPPING")

    def _perform_intelligent_detection(self, epoch):
        """执行智能检测和动态调整"""
        is_overfitting, overfit_confidence, overfit_analysis = self.detect_intelligent_overfitting(epoch)
        is_plateau, plateau_length, stability_score = self.detect_performance_plateau(epoch)
        self.adjust_dynamic_patience(epoch, overfit_confidence, plateau_length, stability_score)
        # 检查智能早停
        is_declining, conditions_met = self.detect_generalization_decline(epoch)
        if is_declining:
            self._log_intelligent_early_stop(epoch, overfit_analysis, plateau_length, stability_score)
            self.trigger_early_stop(conditions_met)
            return True
        # 检查动态耐心值
        if self.counter >= self.dynamic_patience:
            self._handle_dynamic_patience_exceeded()
            return True
        return False

    def _log_intelligent_early_stop(self, epoch, overfit_analysis, plateau_length, stability_score):
        """记录智能早停信息"""
        log_utils.warning(f"智能早停触发 - Epoch {epoch}", tag="EARLY_STOPPING")
        log_utils.warning(f"过拟合分析: {overfit_analysis}", tag="EARLY_STOPPING")
        log_utils.warning(f"平台期分析: 长度={plateau_length}, 稳定性={stability_score:.3f}", tag="EARLY_STOPPING")

    def _handle_dynamic_patience_exceeded(self):
        """处理动态耐心值超限"""
        log_utils.warning(f"达到动态耐心值限制: {self.counter}/{self.dynamic_patience}", tag="EARLY_STOPPING")
        self.early_stop = True
        self.stop_reason = f"连续{self.dynamic_patience}轮无显著改进(动态调整)"

    def _evaluate_and_update_model(self, val_acc, model, separation_ratio, epoch, val_boundary_info):
        """评估并更新模型"""
        # 检查是否有改进
        improved = self._check_improvement(val_acc, separation_ratio)
        if improved:
            self._save_best_model(model, val_acc, separation_ratio, epoch)
            self._reset_patience()
        else:
            self._increment_patience()
        # 检查早停条件
        should_stop = self._check_early_stopping_conditions(epoch, val_acc, separation_ratio, val_boundary_info)
        self._update_history(val_acc, separation_ratio, improved)
        return should_stop

    def _check_improvement(self, val_acc, separation_ratio):
        """检查是否有改进"""
        return val_acc > self.best_val_acc or separation_ratio > self.best_separation

    def _save_best_model(self, model, val_acc, separation_ratio, epoch):
        """保存最佳模型"""
        # 正确计算特征质量得分
        feature_quality_score = self._calculate_feature_quality(separation_ratio, self.best_inter_distance, self.best_intra_distance)
        self._update_best_scores(separation_ratio, val_acc, epoch, feature_quality_score)
        self._save_model_state(model, epoch, separation_ratio, val_acc, self.best_inter_distance, self.best_intra_distance, feature_quality_score)
        self._reset_counters()
        self._log_model_update(epoch, separation_ratio, val_acc, self.best_inter_distance, self.best_intra_distance, feature_quality_score)
        return True

    def _reset_patience(self):
        """重置早停计数器"""
        self.counter = 0
        self.stagnation_level = 0

    def _increment_patience(self):
        """增加早停计数器"""
        self.counter += 1
        self.stagnation_level += 1

    def _check_early_stopping_conditions(self, epoch, val_acc, separation_ratio, val_boundary_info):
        """检查早停条件"""
        # 检查最小训练轮数
        if epoch < self.min_epochs:
            return False
        if self.counter >= self.patience:
            log_utils.info(f"早停触发: 连续{self.patience}轮无改善", tag="EARLY_STOPPING")
            return True
        # 检查高级早停条件
        advanced_conditions = self._check_advanced_early_stopping(epoch, val_acc, separation_ratio, val_boundary_info)
        return advanced_conditions

    def _check_advanced_early_stopping(self, epoch, val_acc, separation_ratio, val_boundary_info):
        """检查高级早停条件"""
        conditions_met = []
        # 构建参数
        params = {
            'epoch': epoch,
            'val_acc': val_acc,
            'separation_ratio': separation_ratio,
            'val_boundary_info': val_boundary_info or {}
        }
        # 检查所有早停条件
        for condition_type in [
            "plateau_detection", "overfitting_detection", "convergence_detection",
            "nan_detection", 
            "manual_stop", "validation_degradation", "oscillation_detection"
        ]:
            if self._evaluate_early_stopping_condition(condition_type, params):
                conditions_met.append(condition_type)
        # 记录触发条件
        if conditions_met:
            log_utils.info(f"早停条件触发: {', '.join(conditions_met)}", tag="EARLY_STOPPING")
            return True
        return False

    def _evaluate_early_stopping_condition(self, condition_type, params):
        """评估单个早停条件"""
        try:
            if condition_type == "plateau_detection":
                return self._check_plateau_detection(params)
            elif condition_type == "overfitting_detection":
                return self._check_overfitting_detection(params)
            elif condition_type == "convergence_detection":
                return self._check_convergence_detection(params)
            elif condition_type == "nan_detection":
                return self._check_nan_detection(params)
            elif condition_type == "manual_stop":
                return self._check_manual_stop(params)
            elif condition_type == "validation_degradation":
                return self._check_validation_degradation(params)
            elif condition_type == "oscillation_detection":
                return self._check_oscillation_detection(params)
            else:
                return False
        except Exception as e:
            log_utils.error(f"早停条件 {condition_type} 评估失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_plateau_detection(self, params):
        """检查平台期条件"""
        return self.val_acc_stable(params['epoch'])

    def _check_overfitting_detection(self, params):
        """检查过拟合条件"""
        try:
            # 提取过拟合检测数据，检测信号，计算置信度，判断是否过拟合
            overfitting_data = self._extract_overfitting_data()
            signals = self._detect_all_overfitting_signals(overfitting_data)
            confidence = self._calculate_overfitting_confidence(signals['count'], overfitting_data['avg_gap'])
            return self._determine_overfitting(signals['count'], confidence)
        except Exception as e:
            log_utils.error(f"过拟合检测失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_convergence_detection(self, params):
        """检查收敛条件"""
        try:
            epoch = params['epoch']
            is_plateau, plateau_length, stability_score = self.detect_performance_plateau(epoch)
            return is_plateau and plateau_length >= 3  # 连续3轮平台期视为收敛
        except Exception as e:
            log_utils.error(f"收敛检测失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_nan_detection(self, params):
        """检查NaN条件"""
        try:
            val_acc = params.get('val_acc', 0)
            separation_ratio = params.get('separation_ratio', 0)
            return (math.isnan(val_acc) or math.isnan(separation_ratio) or 
                   math.isinf(val_acc) or math.isinf(separation_ratio))
        except Exception as e:
            log_utils.error(f"NaN检测失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_validation_degradation(self, params):
        """检查验证集退化条件"""
        try:
            if len(self.val_acc_history) < 5:
                return False
            recent_acc = self.val_acc_history[-5:]
            # 检查验证准确率是否持续下降
            acc_decline = (recent_acc[0] - recent_acc[-1]) / max(0.001, recent_acc[0])
            return acc_decline > 0.1  # 下降超10%视为退化
        except Exception as e:
            log_utils.error(f"验证集退化检测失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_oscillation_detection(self, params):
        """检查震荡条件"""
        try:
            if len(self.val_acc_history) < 5:
                return False
            recent_acc = self.val_acc_history[-5:]
            acc_mean = sum(recent_acc) / len(recent_acc)
            acc_variance = sum((acc - acc_mean)**2 for acc in recent_acc) / len(recent_acc)
            return acc_variance > 0.01  # 方差大视为震荡
        except Exception as e:
            log_utils.error(f"震荡检测失败: {e}", tag="EARLY_STOPPING")
            return False

    def _check_manual_stop(self, params):
        """检查手动停止条件"""
        return self.early_stop



    def _update_history(self, val_acc, separation_ratio, improved):
        """更新历史记录"""
        log_utils.debug(f"_update_history called: val_acc={val_acc}, separation_ratio={separation_ratio}", tag="EARLY_STOPPING")
        log_utils.debug(f"Before update: val_boundary_history length={len(self.val_boundary_history)}", tag="EARLY_STOPPING")
        # 只添加基础指标，验证集相关数据已在_record_basic_metrics处理
        self._append_to_history('val_acc_history', val_acc)
        self._append_to_history('separation_history', separation_ratio)
        # 使用共享的质量分数，避免重复计算
        current_quality_score = self.shared_quality_score if hasattr(self, 'shared_quality_score') and self.shared_quality_score is not None else 0.5
        self._append_to_history('feature_quality_scores', current_quality_score)
        self._append_to_history('improvement_rates', self._calculate_improvement_rate(separation_ratio))
        self._append_to_history('weight_history', self.current_weights)
        # 检查并补充默认值，避免缺失
        if not self.boundary_history:
            self._append_to_history('boundary_history', 0)
        if not self.val_boundary_history:
            self._append_to_history('val_boundary_history', 0)
        if not self.val_separation_history:
            self._append_to_history('val_separation_history', 0)

        if not self.inter_distance_history:
            self._append_to_history('inter_distance_history', 0)
        if not self.intra_distance_history:
            self._append_to_history('intra_distance_history', 1)
        log_utils.debug(f"After update: val_boundary_history length={len(self.val_boundary_history)}", tag="EARLY_STOPPING")

    def _calculate_feature_quality(self, separation_ratio, inter_distance, intra_distance):
        """获取特征质量得分，避免重复计算"""
        # 检查是否有共享的MetricsCalculator结果可用
        if hasattr(self, 'shared_quality_score') and self.shared_quality_score is not None:
            log_utils.debug(f"早停机制特征质量获取: 使用共享结果，得分={self.shared_quality_score:.4f}", tag="EARLY_STOPPING")
            return self.shared_quality_score
        
        # 降级处理：基于分离比的简化计算
        quality_score = min(1.0, separation_ratio / 0.8) * 0.6 + min(1.0, inter_distance / max(0.001, intra_distance) / 5.0) * 0.4
        log_utils.debug(f"早停机制特征质量计算: 使用简化方法，得分={quality_score:.4f}", tag="EARLY_STOPPING")
        return quality_score

    def _calculate_improvement_rate(self, separation_ratio):
        """计算改进率"""
        if len(self.separation_history) < 2:
            return 0
        current_improvement = ((separation_ratio - self.separation_history[-2]) /
                              max(self.separation_history[-2], self.algorithm_config.min_separation_denominator))
        self.improvement_rates.append(current_improvement)
        if len(self.improvement_rates) >= self.algorithm_config.improvement_rate_window:
            return (sum(self.improvement_rates[-self.algorithm_config.improvement_rate_window:]) /
                   self.algorithm_config.improvement_rate_window)
        return 0

    def _is_weight_stable(self):
        """判断权重是否稳定"""
        if len(self.weight_history) <= self.weight_stability_epochs:
            return False
        recent_weights = self.weight_history[-self.weight_stability_epochs:]
        changes = []
        try:
            for i in range(1, len(recent_weights)):
                if isinstance(recent_weights[i], dict) and isinstance(recent_weights[i-1], dict):
                    # 比较字典类型权重
                    change = sum(abs(recent_weights[i].get(k, 0) - recent_weights[i-1].get(k, 0))
                                for k in set(recent_weights[i].keys()).union(recent_weights[i-1].keys()))
                    changes.append(change)
                elif hasattr(recent_weights[i], '__getitem__') and hasattr(recent_weights[i-1], '__getitem__') and not isinstance(recent_weights[i], str) and not isinstance(recent_weights[i-1], str):
                    # 比较元组或列表类型权重
                    min_len = min(len(recent_weights[i]), len(recent_weights[i-1]))
                    if min_len > 0:
                        change = sum(abs(recent_weights[i][j] - recent_weights[i-1][j]) for j in range(min_len))
                        changes.append(change)
            if changes:
                avg_change = sum(changes) / len(changes)
                return avg_change < self.weight_change_threshold
            else:
                return False
        except Exception as e:
            log_utils.error(f"权重稳定性计算失败: {e}", tag="EARLY_STOPPING")
            return False

    def _update_best_scores(self, separation_ratio, val_acc, epoch, feature_quality_score):
        """更新最佳分数"""
        self.best_feature_quality_score = feature_quality_score
        self.best_separation = separation_ratio
        self.best_val_acc = val_acc
        self.best_epoch = epoch

    def _save_model_state(self, model, epoch, separation_ratio, val_acc, inter_distance, 
                         intra_distance, feature_quality_score):
        """保存模型状态"""
        self.best_model_state = model.state_dict() if hasattr(model, 'state_dict') else None
        self.best_feature_quality_model = {
            'epoch': epoch,
            'feature_quality_score': feature_quality_score,
            'separation_ratio': separation_ratio,
            'inter_distance': inter_distance,
            'intra_distance': intra_distance,
            'val_acc': val_acc,
            'state_dict': model.state_dict(),
            'weights': self.current_weights
        }
        self.best_weights = self.current_weights

    def _reset_counters(self):
        """重置计数器"""
        self.counter = 0
        self.stagnation_level = 0

    def _log_model_update(self, epoch, separation_ratio, val_acc, inter_distance, 
                         intra_distance, feature_quality_score):
        """记录模型更新信息"""
        improvements = self._calculate_improvements(separation_ratio, inter_distance, 
                                                   intra_distance, feature_quality_score, val_acc)
        self._log_core_metrics(epoch, feature_quality_score, separation_ratio, 
                              inter_distance, intra_distance, improvements)
        self._log_quality_assessment(feature_quality_score)
        self._log_validation_performance(val_acc, improvements['acc_improvement'])
        self._log_space_health(inter_distance, intra_distance)
        self._log_training_progress(epoch)
        self._log_weight_info()
        log_utils.info("✅ 最佳模型已更新并保存 (基于特征质量综合评估)", tag="EARLY_STOPPING")

    def _calculate_improvements(self, separation_ratio, inter_distance, intra_distance, feature_quality_score, val_acc):
        """计算各项指标的改进百分比"""
        return {
            'sep_improvement': (separation_ratio / max(0.001, self.best_separation) - 1) * 100,
            'inter_improvement': (inter_distance / max(0.001, self.best_inter_distance) - 1) * 100,
            'intra_change': (1 - intra_distance / max(0.001, self.best_intra_distance)) * 100,
            'quality_improvement': (feature_quality_score / max(0.001, self.best_feature_quality_score) - 1) * 100,
            'acc_improvement': ((val_acc / max(0.001, self.best_val_acc) - 1) * 100)
        }

    def _log_core_metrics(self, epoch, feature_quality_score, separation_ratio, inter_distance, intra_distance, improvements):
        """日志记录核心特征指标"""
        log_utils.info(f"🏆 最佳模型更新 - Epoch {epoch}", tag="EARLY_STOPPING")
        log_utils.info("  ┌─ 核心特征质量指标:", tag="EARLY_STOPPING")
        log_utils.info(f"  ├─ 特征质量得分: {feature_quality_score:.6f} ({improvements['quality_improvement']:+.2f}%)", tag="EARLY_STOPPING")
        log_utils.info(f"  ├─ 特征分离比: {separation_ratio:.6f} ({improvements['sep_improvement']:+.2f}%)", tag="EARLY_STOPPING")
        log_utils.info(f"  ├─ 类间距离: {inter_distance:.6f} ({improvements['inter_improvement']:+.2f}%)", tag="EARLY_STOPPING")
        log_utils.info(f"  ├─ 类内距离: {intra_distance:.6f} ({improvements['intra_change']:+.2f}%)", tag="EARLY_STOPPING")

    def _log_quality_assessment(self, feature_quality_score):
        """日志记录特征空间质量评估"""
        quality_grade, quality_desc = self._get_quality_grade(feature_quality_score)
        log_utils.info(f"  ├─ 质量评级: {quality_grade}", tag="EARLY_STOPPING")
        log_utils.info(f"  ├─ 质量描述: {quality_desc}", tag="EARLY_STOPPING")

    def _get_quality_grade(self, score):
        """获取特征空间质量等级"""
        if score > 0.8:
            return "🟢 优秀", "特征空间结构优异，判别能力强"
        elif score > 0.6:
            return "🟡 良好", "特征空间结构良好，判别能力较强"
        elif score > 0.4:
            return "🟠 基线", "特征空间结构基本合理"
        else:
            return "🔴 需要改进", "特征空间结构需要优化"

    def _log_validation_performance(self, val_acc, acc_improvement):
        """日志记录验证集性能"""
        log_utils.info("  ├─ 验证性能指标:", tag="EARLY_STOPPING")
        log_utils.info(f"  │  └─ 验证准确率: {val_acc:.4f} ({acc_improvement:+.2f}%)", tag="EARLY_STOPPING")

    def _log_space_health(self, inter_distance, intra_distance):
        """日志记录特征空间健康度"""
        from utils.distance_calculator import compute_separation_ratio  # 计算距离比
        distance_ratio = compute_separation_ratio(intra_distance, inter_distance)
        space_health, health_desc = self._get_space_health(distance_ratio)
        log_utils.info(f"  ├─ 特征空间健康度: {space_health}", tag="EARLY_STOPPING")
        log_utils.info(f"  │  ├─ 距离比 (Inter/Intra): {distance_ratio:.4f}", tag="EARLY_STOPPING")
        log_utils.info(f"  │  └─ 健康描述: {health_desc}", tag="EARLY_STOPPING")

    def _get_space_health(self, ratio):
        """获取空间健康度等级 - 使用统一健康度评估模块"""
        from utils.feature_space_health import evaluate_distance_ratio_health
        
        # 根据ratio计算虚拟的inter_distance和intra_distance
        inter_distance = ratio
        intra_distance = 1.0
        
        level, desc = evaluate_distance_ratio_health(inter_distance, intra_distance)
        return level.value[0], desc

    def _log_training_progress(self, epoch):
        """日志记录训练进度"""
        total_epochs = len(self.val_acc_history)
        progress_pct = (epoch / max(1, total_epochs)) * 100
        epochs_since_last_best = epoch - getattr(self, 'last_best_epoch', 0)
        self.last_best_epoch = epoch
        log_utils.info("  ├─ 训练进度分析:", tag="EARLY_STOPPING")
        log_utils.info(f"  │  ├─ 当前轮次: {epoch} (进度: {progress_pct:.1f}%)", tag="EARLY_STOPPING")
        log_utils.info(f"  │  ├─ 距上次最佳: {epochs_since_last_best} 轮", tag="EARLY_STOPPING")
        log_utils.info(f"  │  └─ 无改进计数重置: {self.counter} → 0", tag="EARLY_STOPPING")

    def _log_weight_info(self):
        """日志记录损失权重信息"""
        if not self.current_weights:
            return
        log_utils.info("  └─ 当前损失权重配置:", tag="EARLY_STOPPING")
        if isinstance(self.current_weights, dict):
            for loss_name, weight in self.current_weights.items():
                log_utils.info(f"     ├─ {loss_name}: {weight:.4f}", tag="EARLY_STOPPING")
        else:
            log_utils.info(f"     └─ 权重: {self.current_weights}", tag="EARLY_STOPPING")

    def _update_stagnation_level(self):
        """更新训练停滞度，综合多项指标"""
        stagnation_factors = 0

        # 检查验证准确率停滞
        if len(self.val_acc_history) >= self.algorithm_config.stagnation_history_window:
            recent_acc = self.val_acc_history[-self.algorithm_config.stagnation_history_window:]
            relative_to_best = [(acc - max(self.val_acc_history)) / max(0.01, max(self.val_acc_history)) for acc in recent_acc]
            # 检查准确率波动但无提升
            if all(r > self.algorithm_config.accuracy_relative_gap_threshold for r in relative_to_best) and max(recent_acc) <= self.best_val_acc:
                stagnation_factors += 1
            # 检查准确率锯齿波动
            ups_and_downs = sum(1 for i in range(1, len(recent_acc)) if (recent_acc[i] > recent_acc[i-1] and i < len(recent_acc)-1 and recent_acc[i+1] < recent_acc[i]))
            if ups_and_downs >= self.algorithm_config.zigzag_fluctuation_threshold:
                stagnation_factors += 1

        # 检查特征分离比停滞
        if len(self.separation_history) >= self.algorithm_config.stagnation_history_window:
            recent_sep = self.separation_history[-self.algorithm_config.stagnation_history_window:]
            changes = [abs(recent_sep[i] - recent_sep[i-1]) / max(0.01, recent_sep[i-1]) for i in range(1, len(recent_sep))]
            avg_change = sum(changes) / len(changes)
            if avg_change < self.algorithm_config.separation_change_rate_threshold:
                stagnation_factors += 1

        # 检查验证集边界样本极少
        if len(self.val_boundary_history) >= 3:
            recent_boundary = self.val_boundary_history[-3:]
            if all(br < self.algorithm_config.val_boundary_low_threshold for br in recent_boundary):
                stagnation_factors += 1

        # 检查训练集边界样本极少
        if len(self.boundary_history) >= self.algorithm_config.boundary_observation_window:
            recent_train_boundary = self.boundary_history[-self.algorithm_config.boundary_observation_window:]
            if all(b < self.boundary_threshold for b in recent_train_boundary):
                stagnation_factors += 1

        # 更新停滞级别，最大不超过max_stagnation
        self.stagnation_level = min(self.max_stagnation, stagnation_factors)

    def get_weight_adjustment(self):
        """获取权重调整建议，无需调整时返回None"""
        if self.weight_adjustment_needed:
            return self.weight_adjustment_suggestions
        return None

    def is_pseudo_improvement(self):
        """返回当前是否检测到伪提升"""
        return self.pseudo_improvement_detected

    def get_status_summary(self):
        """获取早停状态摘要，便于训练监控"""
        summary = []
        # 基本状态
        summary.append(f"无改进计数: {self.counter}/{self.patience}")
        summary.append(f"停滞级别: {self.stagnation_level}/{self.max_stagnation}")
        # 关键指标状态
        if self.negative_trend_counter > 0:
            summary.append(f"负面趋势: {self.negative_trend_counter}/{self.max_negative_trends}")
        if self.boundary_stability_counter > 0:
            summary.append(f"边界稳定: {self.boundary_stability_counter}/{self.boundary_stability_threshold}")
        # 验证准确率
        if len(self.val_acc_history) > 0:
            curr_acc = self.val_acc_history[-1]
            best_acc = self.best_val_acc
            acc_gap = ((curr_acc - best_acc) / max(0.01, best_acc)) * 100
            summary.append(f"当前准确率: {curr_acc*100:.2f}% (距最佳: {acc_gap:+.2f}%)")
        # 验证集边界样本
        if len(self.val_boundary_history) > 0:
            val_boundary = self.val_boundary_history[-1] * 100
            summary.append(f"验证集边界样本: {val_boundary:.2f}% (稳定计数: {self.boundary_stability_counter}/{self.boundary_stability_threshold})")
        # 训练边界样本
        if len(self.boundary_history) > 0:
            boundary = self.boundary_history[-1]
            summary.append(f"训练边界样本: {boundary} (阈值: {self.boundary_threshold})")
        # 特征分离比趋势
        if len(self.separation_history) > 0:
            curr_sep = self.separation_history[-1]
            if curr_sep > self.best_separation:
                sep_status = f"分离比: {curr_sep:.2f} ↗️"
            elif curr_sep < self.best_separation * 0.95:
                sep_status = f"分离比: {curr_sep:.2f} ↘️"
            else:
                sep_status = f"分离比: {curr_sep:.2f} ➡️"
            summary.append(sep_status)
        # 泛化约束评估
        generalization_checks = []
        if len(self.val_acc_history) >= self.patience_val_epoch:
            val_stagnant = self.val_acc_stable(len(self.val_acc_history))
            generalization_checks.append(f"Val Acc{'✓' if val_stagnant else '✗'}")

        if len(self.inter_distance_history) >= self.patience_inter_epoch:
            inter_decline = self.inter_distance_decline()
            generalization_checks.append(f"Inter Dist{'✓' if inter_decline else '✗'}")
        if len(self.boundary_history) >= 3:
            boundary_low = self.boundary_samples_low_for_long()
            generalization_checks.append(f"边界样本低{'✓' if boundary_low else '✗'}")
        if generalization_checks:
            summary.append("泛化约束: " + " ".join(generalization_checks))
        # 趋势信息
        if self.negative_trend_counter > 0:
            summary.append(f"负面趋势: {self.negative_trend_counter}/{self.max_negative_trends}")
        # 改进率信息
        if len(self.improvement_rates) >= self.algorithm_config.improvement_rate_window:
            avg_improvement = sum(self.improvement_rates[-self.algorithm_config.improvement_rate_window:]) / self.algorithm_config.improvement_rate_window
            summary.append(f"近期改进率: {avg_improvement:.6f}")
        # 伪提升检测
        if self.pseudo_improvement_detected:
            summary.append(f"⚠️ 伪提升: 第{self.pseudo_improvement_count}次")
        # 最佳轮次信息
        summary.append(f"最佳轮次: {self.best_epoch} (当前距离: {len(self.val_acc_history) - self.best_epoch}轮)")
        return " | ".join(summary)

    def _detect_pseudo_improvement(self, inter_distance, intra_distance):
        """检测是否存在伪提升"""
        self.pseudo_improvement_detected = False
        # 仅有历史记录时才检测
        if len(self.inter_distance_history) <= 1 or not hasattr(self, 'intra_distance_history') or len(self.intra_distance_history) <= 1:
            return False
        last_inter = self.inter_distance_history[-2]
        last_intra = self.intra_distance_history[-2]
        # 检查类间类内距离是否同时下降
        if inter_distance < last_inter and intra_distance < last_intra:
            inter_decline_percent = (last_inter - inter_distance) / max(self.algorithm_config.min_distance_denominator, last_inter)
            intra_decline_percent = (last_intra - intra_distance) / max(self.algorithm_config.min_distance_denominator, last_intra)
            # 判断是否为伪提升
            safety_ratio = self.inter_intra_safety_ratio
            if inter_decline_percent / max(self.algorithm_config.min_distance_denominator, intra_decline_percent) > safety_ratio:
                self.pseudo_improvement_detected = True
                self.pseudo_improvement_count += 1
                self._log_pseudo_improvement(inter_distance, intra_distance, inter_decline_percent, intra_decline_percent, safety_ratio)
                return True
        return False

    def _log_pseudo_improvement(self, inter_distance, intra_distance, inter_decline_percent, intra_decline_percent, safety_ratio):
        """日志记录伪提升详情"""
        last_inter = self.inter_distance_history[-2]
        last_intra = self.intra_distance_history[-2]
        log_utils.warning(f"⚠️⚠️⚠️ 检测到特征空间伪提升 (第{self.pseudo_improvement_count}次)", tag="EARLY_STOPPING")
        log_utils.warning(f"⚠️ 类间距离下降:{inter_decline_percent:.2%}, 类内距离下降:{intra_decline_percent:.2%}", tag="EARLY_STOPPING")
        log_utils.warning(f"⚠️ 下降比例比值:{inter_decline_percent/max(self.algorithm_config.min_distance_denominator, intra_decline_percent):.2f} > 安全阈值:{safety_ratio:.2f}", tag="EARLY_STOPPING")
        # 计算分离比变化
        from utils.distance_calculator import compute_separation_ratio
        prev_sep = compute_separation_ratio(last_intra, last_inter)
        curr_sep = compute_separation_ratio(intra_distance, inter_distance)
        sep_change = (curr_sep / max(self.algorithm_config.min_separation_denominator, prev_sep) - 1) * 100
        log_utils.warning(f"⚠️ 分离比从 {prev_sep:.4f} 变为 {curr_sep:.4f} ({sep_change:+.2f}%)，但这是不健康的特征结构退化", tag="EARLY_STOPPING")

    def _process_weights(self, weights):
        """
        标准化权重格式
        """
        if not weights:
            return {}

        try:
            if isinstance(weights, dict):
                # 字典直接复制
                return weights.copy()
            elif hasattr(weights, '__getitem__') and not isinstance(weights, str):
                # 元组或列表转为字典
                weight_dict = {
                    'circle': weights[0] if len(weights) > 0 else 0,
                    'circle': weights[1] if len(weights) > 1 else 0,
                    'center': weights[2] if len(weights) > 2 else 0
                }
                return weight_dict
            else:
                # 其他类型返回空字典
                return {}
        except Exception as e:
            log_utils.error(f"权重处理失败: {e}", tag="EARLY_STOPPING")
            return {}

    def _append_to_history(self, attr_name, value, default_value=None):
        """
        向历史记录安全添加值
        """
        if not hasattr(self, attr_name):
            setattr(self, attr_name, [] if default_value is None else default_value)

        history = getattr(self, attr_name)
        if isinstance(history, list):
            history.append(value)

    def _is_weight_stable(self):
        """
        检查权重是否稳定
        """
        if len(self.weight_history) <= self.weight_stability_epochs:
            return False

        recent_weights = self.weight_history[-self.weight_stability_epochs:]
        changes = []

        try:
            for i in range(1, len(recent_weights)):
                if isinstance(recent_weights[i], dict) and isinstance(recent_weights[i-1], dict):
                    # 比较字典权重
                    change = sum(abs(recent_weights[i].get(k, 0) - recent_weights[i-1].get(k, 0))
                                for k in set(recent_weights[i].keys()).union(recent_weights[i-1].keys()))
                    changes.append(change)
                elif hasattr(recent_weights[i], '__getitem__') and hasattr(recent_weights[i-1], '__getitem__') and not isinstance(recent_weights[i], str) and not isinstance(recent_weights[i-1], str):
                    # 比较元组或列表权重
                    min_len = min(len(recent_weights[i]), len(recent_weights[i-1]))
                    if min_len > 0:
                        change = sum(abs(recent_weights[i][j] - recent_weights[i-1][j]) for j in range(min_len))
                        changes.append(change)

            if changes:
                # 有效变化时计算平均变化
                avg_change = sum(changes) / len(changes)
                return avg_change < self.weight_change_threshold
            else:
                # 无法计算变化时默认不稳定
                return False
        except Exception as e:
            log_utils.error(f"权重稳定性计算失败: {e}", tag="EARLY_STOPPING")
            return False

    def detect_intelligent_overfitting(self, epoch) -> Tuple[bool, float, str]:
        """
        智能过拟合检测，基于多指标综合判断
        """
        if len(self.val_acc_history) < self.overfit_detection_window:
            return False, 0.0, "数据不足"
        
        # 提取近期数据
        recent_data = self._extract_overfitting_data()
        
        # 检测各项信号
        signals = self._detect_all_overfitting_signals(recent_data)
        
        # 计算置信度
        confidence = self._calculate_overfitting_confidence(signals['count'], recent_data['avg_gap'])
        is_overfitting = self._determine_overfitting(signals['count'], confidence)
        
        # 构建分析结果
        analysis = f"信号:{signals['count']}/4, 置信度:{confidence:.3f}, 因素:[{'; '.join(signals['factors'])}]"
        
        return is_overfitting, confidence, analysis

    def _extract_overfitting_data(self):
        """
        提取过拟合检测数据
        """
        recent_val_acc = self.val_acc_history[-self.overfit_detection_window:]
        recent_sep = self.separation_history[-self.overfit_detection_window:]
        
        # 计算泛化差距
        estimated_train_acc = [acc * self.algorithm_config.train_acc_estimation_factor for acc in recent_val_acc]
        generalization_gaps = [train - val for train, val in zip(estimated_train_acc, recent_val_acc)]
        
        return {
            'val_acc': recent_val_acc,
            'separation': recent_sep,
            'generalization_gaps': generalization_gaps,
            'avg_gap': sum(generalization_gaps) / len(generalization_gaps),
            'gap_trend': generalization_gaps[-1] - generalization_gaps[0]
        }

    def _detect_all_overfitting_signals(self, data):
        """
        检测所有过拟合信号
        """
        signals = {'count': 0, 'factors': []}
        
        # 泛化差距信号
        if self._check_generalization_gap_signal(data):
            signals['count'] += 1
            signals['factors'].append(f"泛化差距{data['avg_gap']:.3f}且扩大{data['gap_trend']:.3f}")
        
        # 性能震荡信号
        if self._check_performance_oscillation_signal(data):
            signals['count'] += 1
            val_acc_var, sep_var = self._calculate_performance_variance(data)
            signals['factors'].append(f"验证性能震荡(Acc:{val_acc_var:.6f}, Sep:{sep_var:.6f})")
        
        # 边界样本质量衰退信号
        if self._check_boundary_decline_signal():
            signals['count'] += 1
            signals['factors'].append("边界样本质量显著衰退")
        
        # 距离比例失衡信号
        if self._check_distance_ratio_signal():
            signals['count'] += 1
            ratio_decline = self._calculate_distance_ratio_decline()
            signals['factors'].append(f"距离比例失衡下降{ratio_decline:.2%}")
        
        return signals

    def _check_generalization_gap_signal(self, data):
        # 检查泛化差距信号
        return (data['avg_gap'] > self.generalization_gap_threshold and 
                data['gap_trend'] > self.algorithm_config.gap_trend_threshold)

    def _check_performance_oscillation_signal(self, data):
        # 检查性能震荡信号
        val_acc_var, sep_var = self._calculate_performance_variance(data)
        return (val_acc_var > self.algorithm_config.val_acc_variance_threshold and 
                sep_var > self.algorithm_config.sep_variance_threshold)

    def _calculate_performance_variance(self, data):
        # 计算验证准确率和分离度方差
        val_acc_mean = sum(data['val_acc']) / len(data['val_acc'])
        sep_mean = sum(data['separation']) / len(data['separation'])
        val_acc_var = sum((acc - val_acc_mean)**2 for acc in data['val_acc']) / len(data['val_acc'])
        sep_var = sum((sep - sep_mean)**2 for sep in data['separation']) / len(data['separation'])
        return val_acc_var, sep_var

    def _check_boundary_decline_signal(self):
        # 检查边界样本质量衰退信号
        if len(self.val_boundary_history) < self.overfit_detection_window:
            return False
        recent_boundary = self.val_boundary_history[-self.overfit_detection_window:]
        boundary_decline_rate = ((recent_boundary[0] - recent_boundary[-1]) / 
                                max(self.algorithm_config.min_boundary_denominator, recent_boundary[0]))
        return boundary_decline_rate > self.algorithm_config.boundary_decline_threshold

    def _check_distance_ratio_signal(self):
        # 检查距离比例失衡信号
        if (len(self.inter_distance_history) < 3 or len(self.intra_distance_history) < 3):
            return False
        ratio_decline = self._calculate_distance_ratio_decline()
        return ratio_decline > self.algorithm_config.distance_ratio_decline_threshold

    def _calculate_distance_ratio_decline(self):
        # 计算距离比例下降幅度
        recent_ratio = [self.inter_distance_history[i] / max(self.algorithm_config.min_distance_denominator, 
                                                            self.intra_distance_history[i]) 
                       for i in range(-3, 0)]
        return ((recent_ratio[0] - recent_ratio[-1]) / 
                max(self.algorithm_config.min_distance_denominator, recent_ratio[0]))

    def _calculate_overfitting_confidence(self, signal_count, avg_gap):
        # 计算过拟合置信度 - 🔧 修复：复用现有配置而不是期望不存在的属性
        # 复用现有的 overfit_confidence_threshold 作为最大置信度
        max_confidence = self.algorithm_config.overfit_confidence_threshold * 2.5  # 稍微放大作为上限
        # 复用现有的 total_overfit_signals 计算权重因子
        signal_weight_factor = 1.0 / self.algorithm_config.total_overfit_signals
        # 复用现有的 generalization_gap_threshold 计算gap权重因子
        gap_weight_factor = 1.0 / max(0.01, self.generalization_gap_threshold)
        
        return min(max_confidence, 
                  signal_count * signal_weight_factor * 0.6 + 
                  avg_gap * gap_weight_factor * 0.4)

    def _determine_overfitting(self, signal_count, confidence):
        # 判断是否过拟合
        return (signal_count >= self.algorithm_config.min_overfit_signals and 
                confidence > self.algorithm_config.overfit_confidence_threshold)

    def detect_performance_plateau(self, current_epoch) -> Tuple[bool, int, float]:
        # 检测是否进入性能平台期
        if len(self.val_acc_history) < self.algorithm_config.min_plateau_detection_window:
            return False, 0, 0.0
        plateau_data = self._extract_plateau_data()
        is_plateau = self._check_plateau_conditions(plateau_data)
        self._update_plateau_counter(is_plateau)
        stability_score = self._calculate_stability_score(plateau_data)
        self.performance_stability_score = stability_score
        return is_plateau, self.consecutive_plateau_epochs, stability_score

    def _extract_plateau_data(self):
        # 提取平台期检测相关数据
        recent_window = min(self.algorithm_config.plateau_detection_window, len(self.val_acc_history))
        recent_acc = self.val_acc_history[-recent_window:]
        recent_sep = self.separation_history[-recent_window:]
        # 🔧 修复：复用现有配置而不是期望不存在的min_performance_denominator
        # 使用现有的numerical_epsilon作为最小分母值
        min_denominator = getattr(self.algorithm_config, 'numerical_epsilon', 1e-8)
        acc_ratios = [acc / max(min_denominator, self.best_val_acc) 
                     for acc in recent_acc]
        sep_ratios = [sep / max(min_denominator, self.best_separation) 
                     for sep in recent_sep]
        return {
            'window_size': recent_window,
            'acc_ratios': acc_ratios,
            'sep_ratios': sep_ratios,
            'high_performance_count': sum(1 for ratio in acc_ratios if ratio >= self.performance_plateau_threshold),
            'sep_high_count': sum(1 for ratio in sep_ratios if ratio >= self.performance_plateau_threshold)
        }

    def _check_plateau_conditions(self, data):
        # 检查平台期判定条件
        acc_variance = self._calculate_ratio_variance(data['acc_ratios'])
        sep_variance = self._calculate_ratio_variance(data['sep_ratios'])
        return (data['high_performance_count'] >= data['window_size'] * self.algorithm_config.high_performance_ratio_threshold and
                data['sep_high_count'] >= data['window_size'] * self.algorithm_config.sep_high_ratio_threshold and
                acc_variance < self.algorithm_config.acc_variance_plateau_threshold and
                sep_variance < self.algorithm_config.sep_variance_plateau_threshold)

    def _calculate_ratio_variance(self, ratios):
        # 计算性能比例方差
        mean_ratio = sum(ratios) / len(ratios)
        return sum((ratio - mean_ratio)**2 for ratio in ratios) / len(ratios)

    def _update_plateau_counter(self, is_plateau):
        # 更新连续平台期计数
        if is_plateau:
            self.consecutive_plateau_epochs += 1
        else:
            self.consecutive_plateau_epochs = 0

    def _calculate_stability_score(self, data):
        # 计算平台期稳定性得分
        acc_variance = self._calculate_ratio_variance(data['acc_ratios'])
        sep_variance = self._calculate_ratio_variance(data['sep_ratios'])
        performance_ratio = data['high_performance_count'] / data['window_size']
        return (1 - acc_variance) * (1 - sep_variance) * performance_ratio

    def adjust_dynamic_patience(self, epoch, overfit_confidence, plateau_length, stability_score):
        # 动态调整耐心值
        if not self.adaptive_patience:
            return
        factors = self._calculate_adjustment_factors(epoch, overfit_confidence, plateau_length, stability_score)
        new_patience = self._compute_new_patience(factors)
        self._update_dynamic_patience(new_patience, factors)

    def _calculate_adjustment_factors(self, epoch, overfit_confidence, plateau_length, stability_score):
        # 计算耐心值调整因子
        return {
            'overfit': self._get_overfit_factor(overfit_confidence),
            'plateau': self._get_plateau_factor(plateau_length),
            'stability': self._get_stability_factor(stability_score),
            'stage': self._get_stage_factor(epoch)
        }

    def _get_overfit_factor(self, overfit_confidence):
        # 获取过拟合因子
        if overfit_confidence > self.algorithm_config.high_overfit_threshold:
            return self.algorithm_config.high_overfit_patience_factor
        elif overfit_confidence > self.algorithm_config.medium_overfit_threshold:
            return self.algorithm_config.medium_overfit_patience_factor
        else:
            return self.algorithm_config.low_overfit_patience_factor

    def _get_plateau_factor(self, plateau_length):
        # 获取平台期因子
        if plateau_length > self.algorithm_config.long_plateau_threshold:
            return self.algorithm_config.long_plateau_patience_factor
        elif plateau_length > self.algorithm_config.medium_plateau_threshold:
            return self.algorithm_config.medium_plateau_patience_factor
        else:
            return self.algorithm_config.normal_plateau_patience_factor

    def _get_stability_factor(self, stability_score):
        # 获取稳定性因子
        if stability_score > self.algorithm_config.high_stability_threshold:
            return self.algorithm_config.high_stability_patience_factor
        elif stability_score > self.algorithm_config.medium_stability_threshold:
            return self.algorithm_config.medium_stability_patience_factor
        else:
            return self.algorithm_config.low_stability_patience_factor

    def _get_stage_factor(self, epoch):
        # 获取训练阶段因子
        if epoch < self.min_epochs * self.algorithm_config.early_stage_ratio:
            return self.algorithm_config.early_stage_patience_factor
        elif epoch < self.min_epochs:
            return self.algorithm_config.middle_stage_patience_factor
        else:
            return self.algorithm_config.late_stage_patience_factor

    def _compute_new_patience(self, factors):
        # 计算新的耐心值并应用边界约束
        base_patience = self.patience
        new_patience = int(base_patience * factors['overfit'] * factors['plateau'] * 
                          factors['stability'] * factors['stage'])
        return max(self.algorithm_config.min_dynamic_patience, 
                  min(self.algorithm_config.max_dynamic_patience, new_patience))

    def _update_dynamic_patience(self, new_patience, factors):
        # 更新动态耐心值并记录显著变化
        old_patience = self.dynamic_patience
        self.dynamic_patience = new_patience
        if abs(new_patience - old_patience) > self.algorithm_config.patience_change_log_threshold:
            log_utils.info(f"动态耐心值调整: {old_patience} → {new_patience} "
                          f"(过拟合:{factors['overfit']:.2f}, 平台:{factors['plateau']:.2f}, "
                          f"稳定性:{factors['stability']:.2f}, 阶段:{factors['stage']:.2f})", tag="EARLY_STOPPING")

    def check_early_stopping(self, epoch, val_acc, separation_ratio, model, 
                            weights=None, val_boundary_info=None,
                            enable_model_saving=True):
        # 检查是否应提前停止训练
        return self._evaluate_and_update_model(val_acc, model, separation_ratio, epoch, 
                                             val_boundary_info)

    def should_stop(self):
        # 判断是否早停
        return self.early_stop
