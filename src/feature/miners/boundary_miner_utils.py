"""
统一边界样本计算工具模块

本模块解决训练与验证阶段边界样本计算逻辑不一致的问题，提供统一的接口和配置化机制。

主要功能：
1. 统一的边界样本计算接口
2. 支持pair_margin和cosine_range两种策略
3. 线程安全的状态管理
4. 动态窗口机制
5. 高复用性和易用性设计
"""

import torch
import torch.nn.functional as F
import threading
from typing import Dict, List, Tuple, Optional, Union, Literal, Any
from dataclasses import dataclass
from pytorch_metric_learning.miners import PairMarginMiner
from pytorch_metric_learning.distances import CosineSimilarity
from utils.feature_norm import l2_normalize
from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig

# 边界挖掘策略类型
BoundaryStrategyType = Literal["pair_margin", "cosine_range"]

# 分母计算模式类型
DenominatorModeType = Literal["sample_based", "pair_based", "unified"]

# 🔧 新增：运行模式类型
RunModeType = Literal["train", "validation"]


@dataclass
class BoundaryConfig:
    """边界样本配置 - 与src.config保持一致"""
    # 通用配置 - 从src.config获取默认值
    boundary_low: float = None          # 边界相似度低阈值
    boundary_high: float = None         # 边界相似度高阈值
    
    # 动态窗口配置
    enable_dynamic_window: bool = True   # 是否启用动态窗口
    min_window_size: int = 50           # 最小窗口大小
    window_ratio: float = 0.1           # 窗口比例（相对于总样本数）
    
    # 质量过滤配置
    enable_quality_filter: bool = False  # 是否启用质量过滤
    quality_threshold: float = 0.5      # 质量阈值
    
    # 线程安全配置
    enable_thread_safety: bool = True    # 是否启用线程安全
    
    # 🔧 分母计算统一配置
    denominator_mode: DenominatorModeType = "unified"  # 分母计算模式
    enable_normalization: bool = True    # 是否启用归一化
    normalization_factor: Optional[float] = None  # 手动归一化因子
    
    # 🔧 新增：策略适配配置
    enable_strategy_adaptation: bool = True  # 是否启用策略适配
    train_strategy_override: Optional[BoundaryStrategyType] = None  # 训练策略覆盖
    validation_strategy_override: Optional[BoundaryStrategyType] = None  # 验证策略覆盖
    
    def __post_init__(self):
        """验证配置参数并设置默认值"""
        # 🔧 从src.config获取边界阈值默认值
        if self.boundary_low is None:
            self.boundary_low = DEFAULT_TRAIN_CONFIG.boundary_similarity_low
            
        if self.boundary_high is None:
            self.boundary_high = DEFAULT_TRAIN_CONFIG.boundary_similarity_high
        
        if not -1.0 <= self.boundary_low <= 1.0:
            raise ValueError(f"boundary_low必须在[-1.0, 1.0]范围内，当前值: {self.boundary_low}")
        
        if not -1.0 <= self.boundary_high <= 1.0:
            raise ValueError(f"boundary_high必须在[-1.0, 1.0]范围内，当前值: {self.boundary_high}")
        
        if self.boundary_low >= self.boundary_high:
            raise ValueError(f"boundary_low ({self.boundary_low}) 必须小于 boundary_high ({self.boundary_high})")


@dataclass  
class BoundaryResult:
    """边界样本计算结果"""
    boundary_count: int                  # 边界样本数量
    total_count: int                     # 总样本数量（原始分母）
    boundary_ratio: float                # 边界样本比例
    boundary_similarities: List[float]   # 边界相似度列表
    strategy_used: BoundaryStrategyType  # 使用的策略
    window_size: int                     # 实际窗口大小
    quality_filtered: bool               # 是否进行了质量过滤
    thread_safe: bool                    # 是否线程安全
    
    # 🔧 新增：统一分母计算相关字段
    denominator_mode: DenominatorModeType  # 分母计算模式
    unified_total_count: int             # 统一分母数量
    unified_boundary_ratio: float        # 统一边界比例
    normalization_applied: bool          # 是否应用了归一化
    normalization_factor: float          # 实际归一化因子
    sample_pairs_count: int              # 样本对总数（用于分析）
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'boundary_count': self.boundary_count,
            'total_count': self.total_count,
            'boundary_ratio': self.boundary_ratio,
            'boundary_similarities': self.boundary_similarities,
            'strategy_used': self.strategy_used,
            'window_size': self.window_size,
            'quality_filtered': self.quality_filtered,
            'thread_safe': self.thread_safe,
            # 🔧 新增：统一分母计算字段
            'denominator_mode': self.denominator_mode,
            'unified_total_count': self.unified_total_count,
            'unified_boundary_ratio': self.unified_boundary_ratio,
            'normalization_applied': self.normalization_applied,
            'normalization_factor': self.normalization_factor,
            'sample_pairs_count': self.sample_pairs_count
        }


class BoundaryMinerUtils:
    """
    统一边界样本计算工具类
    
    核心功能：
    - 支持pair_margin和cosine_range两种策略
    - 统一的接口和配置管理
    - 线程安全的状态管理
    - 动态窗口机制
    - 高复用性设计
    """
    
    def __init__(self, config: BoundaryConfig = None):
        """
        初始化边界挖掘工具
        
        Args:
            config: 边界配置，如果为None则使用默认配置（从src.config读取）
        """
        self.config = config or BoundaryConfig()
        
        # 线程安全锁
        self._lock = threading.Lock() if self.config.enable_thread_safety else None
        
        # 初始化PairMarginMiner（用于pair_margin策略）
        self._cosine_dist = CosineSimilarity()
        self._pair_miner = PairMarginMiner(
            pos_margin=self.config.boundary_high,
            neg_margin=self.config.boundary_low,
            distance=self._cosine_dist
        )
        
        # 统计信息
        self._stats = {
            'total_calls': 0,
            'pair_margin_calls': 0,
            'cosine_range_calls': 0,
            'thread_safe_calls': 0,
            'quality_filtered_calls': 0
        }
        
        log_utils.info(f"🔧 BoundaryMinerUtils初始化完成 - 策略支持: [pair_margin, cosine_range]", tag="BOUNDARY_MINER")
        log_utils.info(f"🔧 边界阈值: [{self.config.boundary_low}, {self.config.boundary_high}] (来源: src.config)", tag="BOUNDARY_MINER")
        log_utils.info(f"🔧 分母计算模式: {self.config.denominator_mode}", tag="BOUNDARY_MINER")
    
    def _compute_sample_pairs_count(self, embeddings: torch.Tensor, labels: torch.Tensor, 
                                   historical_embeddings: Optional[List[torch.Tensor]] = None,
                                   historical_labels: Optional[List[torch.Tensor]] = None) -> int:
        """
        计算样本对总数
        
        Args:
            embeddings: 当前特征嵌入
            labels: 当前标签
            historical_embeddings: 历史特征嵌入列表
            historical_labels: 历史标签列表
            
        Returns:
            样本对总数
        """
        try:
            if historical_embeddings is None or historical_labels is None:
                # 计算当前批次内的样本对数量
                unique_labels = torch.unique(labels)
                total_pairs = 0
                
                for label in unique_labels:
                    same_class_count = (labels == label).sum().item()
                    diff_class_count = len(embeddings) - same_class_count
                    total_pairs += same_class_count * diff_class_count
                
                return total_pairs
            else:
                # 计算当前样本与历史样本的样本对数量
                current_count = len(embeddings)
                
                # 获取历史样本数量
                if len(historical_embeddings) > 0:
                    # 使用动态窗口
                    total_historical = len(historical_embeddings)
                    if self.config.enable_dynamic_window:
                        window_size = max(self.config.min_window_size, 
                                        int(self.config.window_ratio * total_historical))
                    else:
                        window_size = self.config.min_window_size
                    
                    recent_embeddings = historical_embeddings[-window_size:]
                    recent_labels = historical_labels[-window_size:]
                    
                    if len(recent_embeddings) > 0:
                        recent_lab_tensor = torch.cat(recent_labels, dim=0)
                        
                        # 计算当前样本与历史不同类别样本的样本对数量
                        total_pairs = 0
                        for i, label in enumerate(labels):
                            current_label = label.item()
                            diff_class_mask = (recent_lab_tensor != current_label)
                            total_pairs += diff_class_mask.sum().item()
                        
                        return total_pairs
                
                return 0
                
        except Exception as e:
            log_utils.error(f"计算样本对数量时出错: {e}", tag="BOUNDARY_MINER")
            return len(embeddings)  # 回退到样本数量
    
    def _compute_unified_denominator(self, embeddings: torch.Tensor, labels: torch.Tensor,
                                   original_total_count: int, sample_pairs_count: int,
                                   strategy: BoundaryStrategyType) -> Tuple[int, float, bool]:
        """
        计算统一分母
        
        Args:
            embeddings: 特征嵌入
            labels: 标签
            original_total_count: 原始分母数量
            sample_pairs_count: 样本对数量
            strategy: 使用的策略
            
        Returns:
            (统一分母数量, 归一化因子, 是否应用归一化)
        """
        if self.config.denominator_mode == "sample_based":
            # 强制使用样本数作为分母
            return len(embeddings), 1.0, False
            
        elif self.config.denominator_mode == "pair_based":
            # 强制使用样本对数作为分母
            return sample_pairs_count, 1.0, False
            
        elif self.config.denominator_mode == "unified":
            # 统一分母计算模式
            if strategy == "pair_margin":
                # 训练阶段：将样本数转换为等效样本对数
                if self.config.enable_normalization:
                    if self.config.normalization_factor is not None:
                        # 使用手动设置的归一化因子
                        normalization_factor = self.config.normalization_factor
                    else:
                        # 🔧 修复：使用实际负样本对统计替代理想化估算
                        unique_labels = torch.unique(labels)
                        total_samples = len(embeddings)
                        
                        # 计算实际负样本对数量
                        actual_neg_pairs = 0
                        for label in unique_labels:
                            same_class_count = (labels == label).sum().item()
                            diff_class_count = total_samples - same_class_count
                            actual_neg_pairs += same_class_count * diff_class_count
                        
                        # 计算平均每个样本的负样本对数量
                        normalization_factor = max(1.0, actual_neg_pairs / max(1, total_samples))
                    
                    unified_total_count = int(original_total_count * normalization_factor)
                    return unified_total_count, normalization_factor, True
                else:
                    return original_total_count, 1.0, False
                    
            elif strategy == "cosine_range":
                # 验证阶段：直接使用样本对数
                return sample_pairs_count, 1.0, False
                
        # 默认回退
        return original_total_count, 1.0, False
    
    def compute_boundary_samples(self, 
                                embeddings: torch.Tensor,
                                labels: torch.Tensor,
                                strategy: BoundaryStrategyType = "cosine_range",
                                historical_embeddings: Optional[List[torch.Tensor]] = None,
                                historical_labels: Optional[List[torch.Tensor]] = None) -> BoundaryResult:
        """
        统一的边界样本计算接口
        
        Args:
            embeddings: 当前特征嵌入 [N, D]
            labels: 当前标签 [N]
            strategy: 计算策略 ("pair_margin" 或 "cosine_range")
            historical_embeddings: 历史特征嵌入列表（用于cosine_range策略）
            historical_labels: 历史标签列表（用于cosine_range策略）
            
        Returns:
            BoundaryResult: 边界样本计算结果
        """
        # 线程安全保护
        if self._lock:
            with self._lock:
                return self._compute_boundary_samples_impl(
                    embeddings, labels, strategy, historical_embeddings, historical_labels
                )
        else:
            return self._compute_boundary_samples_impl(
                embeddings, labels, strategy, historical_embeddings, historical_labels
            )
    
    def compute_unified_boundary_samples(self,
                                        embeddings: torch.Tensor,
                                        labels: torch.Tensor,
                                        mode: RunModeType,
                                        historical_embeddings: Optional[List[torch.Tensor]] = None,
                                        historical_labels: Optional[List[torch.Tensor]] = None) -> BoundaryResult:
        """
        🔧 统一边界挖掘接口 - 策略适配层
        
        根据运行模式自动选择最优策略，确保训练和验证阶段的一致性
        
        Args:
            embeddings: 当前特征嵌入 [N, D]
            labels: 当前标签 [N]
            mode: 运行模式 ("train" 或 "validation")
            historical_embeddings: 历史特征嵌入列表
            historical_labels: 历史标签列表
            
        Returns:
            BoundaryResult: 统一格式的边界样本计算结果
        """
        if not self.config.enable_strategy_adaptation:
            # 策略适配关闭，使用默认策略
            default_strategy = "cosine_range"
            return self.compute_boundary_samples(
                embeddings, labels, default_strategy, historical_embeddings, historical_labels
            )
        
        # 🔧 策略适配逻辑
        if mode == "train":
            # 训练模式策略选择
            strategy = self.config.train_strategy_override or "pair_margin"
            return self._compute_optimized_train_strategy(
                embeddings, labels, strategy, historical_embeddings, historical_labels
            )
        elif mode == "validation":
            # 验证模式策略选择
            strategy = self.config.validation_strategy_override or "cosine_range"
            return self._compute_optimized_validation_strategy(
                embeddings, labels, strategy, historical_embeddings, historical_labels
            )
        else:
            raise ValueError(f"不支持的运行模式: {mode}")
    
    def _compute_optimized_train_strategy(self,
                                         embeddings: torch.Tensor,
                                         labels: torch.Tensor,
                                         strategy: BoundaryStrategyType,
                                         historical_embeddings: Optional[List[torch.Tensor]],
                                         historical_labels: Optional[List[torch.Tensor]]) -> BoundaryResult:
        """训练阶段优化策略"""
        if strategy == "pair_margin":
            # 使用优化的pair_margin策略
            result = self._compute_pair_margin_boundary(embeddings, labels)
            # 🔧 训练阶段特殊处理：增加类别平衡检测
            result = self._enhance_train_result(result, embeddings, labels)
            return result
        elif strategy == "cosine_range":
            # 训练阶段使用cosine_range时的特殊处理
            if historical_embeddings is None:
                # 创建虚拟历史样本
                historical_embeddings = [embeddings.detach().cpu()]
                historical_labels = [labels.detach().cpu()]
            result = self._compute_cosine_range_boundary(
                embeddings, labels, historical_embeddings, historical_labels
            )
            result = self._enhance_train_result(result, embeddings, labels)
            return result
        else:
            raise ValueError(f"训练阶段不支持的策略: {strategy}")
    
    def _compute_optimized_validation_strategy(self,
                                              embeddings: torch.Tensor,
                                              labels: torch.Tensor,
                                              strategy: BoundaryStrategyType,
                                              historical_embeddings: Optional[List[torch.Tensor]],
                                              historical_labels: Optional[List[torch.Tensor]]) -> BoundaryResult:
        """验证阶段优化策略"""
        if strategy == "cosine_range":
            # 使用优化的cosine_range策略
            result = self._compute_cosine_range_boundary(
                embeddings, labels, historical_embeddings, historical_labels
            )
            # 🔧 验证阶段特殊处理：增加时间衰减权重
            result = self._enhance_validation_result(result, embeddings, labels)
            return result
        elif strategy == "pair_margin":
            # 验证阶段使用pair_margin时的特殊处理
            result = self._compute_pair_margin_boundary(embeddings, labels)
            result = self._enhance_validation_result(result, embeddings, labels)
            return result
        else:
            raise ValueError(f"验证阶段不支持的策略: {strategy}")
    
    def _enhance_train_result(self, result: BoundaryResult, embeddings: torch.Tensor, labels: torch.Tensor) -> BoundaryResult:
        """增强训练阶段结果"""
        # 🔧 训练阶段增强：类别平衡检测
        unique_labels = torch.unique(labels)
        class_balance_score = len(unique_labels) / len(embeddings) if len(embeddings) > 0 else 0.0
        
        # 如果类别不平衡，调整边界样本比例
        if class_balance_score < 0.3:  # 类别过于集中
            adjusted_boundary_ratio = result.boundary_ratio * 0.8  # 降低边界比例
            log_utils.debug(f"训练阶段类别不平衡调整: {result.boundary_ratio:.4f} → {adjusted_boundary_ratio:.4f}", tag="BOUNDARY_MINER")
            
            # 创建调整后的结果
            return BoundaryResult(
                boundary_count=int(result.boundary_count * 0.8),
                total_count=result.total_count,
                boundary_ratio=adjusted_boundary_ratio,
                boundary_similarities=result.boundary_similarities[:int(len(result.boundary_similarities) * 0.8)],
                strategy_used=f"{result.strategy_used}_train_enhanced",
                window_size=result.window_size,
                quality_filtered=result.quality_filtered,
                thread_safe=result.thread_safe,
                denominator_mode=result.denominator_mode,
                unified_total_count=result.unified_total_count,
                unified_boundary_ratio=adjusted_boundary_ratio,
                normalization_applied=result.normalization_applied,
                normalization_factor=result.normalization_factor,
                sample_pairs_count=result.sample_pairs_count
            )
        
        # 类别平衡，返回原结果但标记为训练增强
        result.strategy_used = f"{result.strategy_used}_train_enhanced"
        return result
    
    def _enhance_validation_result(self, result: BoundaryResult, embeddings: torch.Tensor, labels: torch.Tensor) -> BoundaryResult:
        """增强验证阶段结果"""
        # 🔧 验证阶段增强：稳定性检测
        if len(result.boundary_similarities) > 1:  # 至少需要2个值才能计算标准差
            # 计算边界相似度的标准差
            similarities_tensor = torch.tensor(result.boundary_similarities)
            
            # 🔧 修复：安全计算标准差，避免自由度不足的警告
            if len(similarities_tensor) > 1:
                # 检查是否所有值都相同
                if torch.all(similarities_tensor == similarities_tensor[0]):
                    boundary_std = 0.0  # 所有值相同，标准差为0
                else:
                    boundary_std = similarities_tensor.std().item()
            else:
                boundary_std = 0.0  # 只有一个值，标准差为0
            
            # 如果边界样本相似度过于分散，进行稳定性调整
            if boundary_std > 0.2:  # 相似度分散度过高
                # 过滤掉极端值
                mean_sim = similarities_tensor.mean()
                stable_mask = torch.abs(similarities_tensor - mean_sim) <= boundary_std
                stable_similarities = similarities_tensor[stable_mask]
                
                adjusted_boundary_count = len(stable_similarities)
                adjusted_boundary_ratio = adjusted_boundary_count / max(result.total_count, 1)
                
                log_utils.debug(f"验证阶段稳定性调整: {result.boundary_count} → {adjusted_boundary_count} 边界样本", tag="BOUNDARY_MINER")
                
                # 创建调整后的结果
                return BoundaryResult(
                    boundary_count=adjusted_boundary_count,
                    total_count=result.total_count,
                    boundary_ratio=adjusted_boundary_ratio,
                    boundary_similarities=stable_similarities.tolist(),
                    strategy_used=f"{result.strategy_used}_val_enhanced",
                    window_size=result.window_size,
                    quality_filtered=result.quality_filtered,
                    thread_safe=result.thread_safe,
                    denominator_mode=result.denominator_mode,
                    unified_total_count=result.unified_total_count,
                    unified_boundary_ratio=adjusted_boundary_ratio,
                    normalization_applied=result.normalization_applied,
                    normalization_factor=result.normalization_factor,
                    sample_pairs_count=result.sample_pairs_count
                )
        
        # 边界样本稳定，返回原结果但标记为验证增强
        result.strategy_used = f"{result.strategy_used}_val_enhanced"
        return result
    
    def _compute_boundary_samples_impl(self,
                                      embeddings: torch.Tensor,
                                      labels: torch.Tensor,
                                      strategy: BoundaryStrategyType,
                                      historical_embeddings: Optional[List[torch.Tensor]],
                                      historical_labels: Optional[List[torch.Tensor]]) -> BoundaryResult:
        """边界样本计算的具体实现"""
        self._stats['total_calls'] += 1
        
        # 🔧 统一分母计算：预先计算样本对数量
        sample_pairs_count = self._compute_sample_pairs_count(
            embeddings, labels, historical_embeddings, historical_labels
        )
        
        if strategy == "pair_margin":
            result = self._compute_pair_margin_boundary(embeddings, labels)
        elif strategy == "cosine_range":
            result = self._compute_cosine_range_boundary(
                embeddings, labels, historical_embeddings, historical_labels
            )
        else:
            raise ValueError(f"不支持的策略: {strategy}")
        
        # 🔧 应用统一分母计算
        unified_total_count, normalization_factor, normalization_applied = self._compute_unified_denominator(
            embeddings, labels, result.total_count, sample_pairs_count, strategy
        )
        
        unified_boundary_ratio = result.boundary_count / max(unified_total_count, 1)
        
        # 🔧 更新结果对象，包含统一分母信息
        return BoundaryResult(
            boundary_count=result.boundary_count,
            total_count=result.total_count,  # 保留原始分母
            boundary_ratio=result.boundary_ratio,  # 保留原始比例
            boundary_similarities=result.boundary_similarities,
            strategy_used=result.strategy_used,
            window_size=result.window_size,
            quality_filtered=result.quality_filtered,
            thread_safe=result.thread_safe,
            # 🔧 新增统一分母计算字段
            denominator_mode=self.config.denominator_mode,
            unified_total_count=unified_total_count,
            unified_boundary_ratio=unified_boundary_ratio,
            normalization_applied=normalization_applied,
            normalization_factor=normalization_factor,
            sample_pairs_count=sample_pairs_count
        )
    
    def _compute_pair_margin_boundary(self, embeddings: torch.Tensor, labels: torch.Tensor) -> BoundaryResult:
        """
        使用PairMarginMiner策略计算边界样本
        
        PairMarginMiner工作原理：
        - 返回格式: (a1, p, a2, n) 其中：
          * a1: 正样本对的anchor索引
          * p: 与a1形成正样本对的positive索引
          * a2: 负样本对的anchor索引  
          * n: 与a2形成负样本对的negative索引
        - 边界样本定义: anchor-negative相似度在[boundary_low, boundary_high]范围内的困难负样本
        - 目标: 识别那些与anchor相似度适中的负样本，用于困难样本挖掘
        """
        self._stats['pair_margin_calls'] += 1
        
        try:
            unique_labels = torch.unique(labels).size(0)
            # 归一化特征
            norm_embeddings = l2_normalize(embeddings, p=2, dim=1)
            
            # 使用PairMarginMiner挖掘
            pairs = self._pair_miner(norm_embeddings, labels)
            log_utils.debug(f"PairMarginMiner返回的4元组格式: {len(pairs)}", tag="BOUNDARY_MINER")
            
            # 🔧 修复：正确处理PairMarginMiner返回的4元组格式: (a1, p, a2, n)
            # a1: anchor索引 (用于正样本对)
            # p: positive索引 (与a1形成正样本对)
            # a2: anchor索引 (用于负样本对) 
            # n: negative索引 (与a2形成负样本对)
            if len(pairs) == 4 and len(pairs[2]) > 0 and len(pairs[3]) > 0:  # 检查负样本对是否存在
                a1, p, a2, n = pairs
                
                # 🔧 修复：计算anchor和negative之间的相似度（边界样本关注困难负样本）
                # a2是负样本对的anchor索引，n是对应的negative索引
                anchor_embeddings = norm_embeddings[a2]  # 负样本对的anchor
                negative_embeddings = norm_embeddings[n]  # 对应的negative样本
                # similarities = torch.cosine_similarity(anchor_embeddings, negative_embeddings, dim=1)

                from src.utils.distance_calculator import get_distance_calculator
                distance_calculator = get_distance_calculator()
                similarities = distance_calculator.cosine_distance(anchor_embeddings, negative_embeddings)
                
                # 动态调整质量过滤阈值
                diversity = unique_labels / len(labels)
                adj_low = max(0.1, self.config.boundary_low * (1.5 - diversity))
                adj_high = min(0.9, self.config.boundary_high * (0.5 + diversity))
                
                # 质量过滤
                boundary_similarities = []
                boundary_count = len(a2)
                
                if self.config.enable_quality_filter:
                    quality_mask = (similarities >= adj_low) & \
                                  (similarities <= adj_high)
                    boundary_count = quality_mask.sum().item()
                    boundary_similarities = similarities[quality_mask].detach().cpu().numpy().tolist()
                    self._stats['quality_filtered_calls'] += 1
                else:
                    boundary_similarities = similarities.detach().cpu().numpy().tolist()
                
                total_count = len(embeddings)
                boundary_ratio = boundary_count / max(total_count, 1)
                
                log_utils.debug(f"边界样本统计: anchor-negative对数={len(a2)}, 过滤后边界样本={boundary_count}, 相似度范围=[{min(similarities):.4f}, {max(similarities):.4f}]", tag="BOUNDARY_MINER")
                
                return BoundaryResult(
                    boundary_count=boundary_count,
                    total_count=total_count,
                    boundary_ratio=boundary_ratio,
                    boundary_similarities=boundary_similarities,
                    strategy_used="pair_margin",
                    window_size=total_count,
                    quality_filtered=self.config.enable_quality_filter,
                    thread_safe=self._lock is not None,
                    # 临时值，将在上层方法中更新
                    denominator_mode="sample_based",
                    unified_total_count=total_count,
                    unified_boundary_ratio=boundary_ratio,
                    normalization_applied=False,
                    normalization_factor=1.0,
                    sample_pairs_count=0
                )
            else:
                # 🔧 改进：记录无负样本对的警告
                if len(pairs) == 4:
                    log_utils.debug(f"PairMarginMiner未产生负样本对，检查类别多样性。正样本对数: {len(pairs[0])}, 负样本对数: {len(pairs[2])}", tag="BOUNDARY_MINER")
                else:
                    log_utils.debug(f"PairMarginMiner返回格式异常: {len(pairs)}", tag="BOUNDARY_MINER")
                
                return BoundaryResult(
                    boundary_count=0,
                    total_count=len(embeddings),
                    boundary_ratio=0.0,
                    boundary_similarities=[],
                    strategy_used="pair_margin",
                    window_size=len(embeddings),
                    quality_filtered=self.config.enable_quality_filter,
                    thread_safe=self._lock is not None,
                    # 临时值，将在上层方法中更新
                    denominator_mode="sample_based",
                    unified_total_count=len(embeddings),
                    unified_boundary_ratio=0.0,
                    normalization_applied=False,
                    normalization_factor=1.0,
                    sample_pairs_count=0
                )
                
        except Exception as e:
            log_utils.error(f"PairMargin边界样本计算失败: {e}", tag="BOUNDARY_MINER")
            return BoundaryResult(
                boundary_count=0,
                total_count=len(embeddings),
                boundary_ratio=0.0,
                boundary_similarities=[],
                strategy_used="pair_margin",
                window_size=len(embeddings),
                quality_filtered=False,
                thread_safe=self._lock is not None,
                # 临时值，将在上层方法中更新
                denominator_mode="sample_based",
                unified_total_count=len(embeddings),
                unified_boundary_ratio=0.0,
                normalization_applied=False,
                normalization_factor=1.0,
                sample_pairs_count=0
            )
    
    def _compute_cosine_range_boundary(self, 
                                      embeddings: torch.Tensor,
                                      labels: torch.Tensor,
                                      historical_embeddings: Optional[List[torch.Tensor]],
                                      historical_labels: Optional[List[torch.Tensor]]) -> BoundaryResult:
        """使用余弦相似度范围策略计算边界样本"""
        self._stats['cosine_range_calls'] += 1
        
        if historical_embeddings is None or historical_labels is None or len(historical_embeddings) < 5:
            # 历史样本不足，返回空结果
            return BoundaryResult(
                boundary_count=0,
                total_count=len(embeddings),
                boundary_ratio=0.0,
                boundary_similarities=[],
                strategy_used="cosine_range",
                window_size=0,
                quality_filtered=False,
                thread_safe=self._lock is not None,
                # 临时值，将在上层方法中更新
                denominator_mode="pair_based",
                unified_total_count=len(embeddings),
                unified_boundary_ratio=0.0,
                normalization_applied=False,
                normalization_factor=1.0,
                sample_pairs_count=0
            )
        
        try:
            # 动态窗口大小计算
            total_historical = len(historical_embeddings)
            if self.config.enable_dynamic_window:
                window_size = max(self.config.min_window_size, 
                                int(self.config.window_ratio * total_historical))
            else:
                window_size = self.config.min_window_size
            
            # 获取最近的历史样本
            recent_embeddings = historical_embeddings[-window_size:]
            recent_labels = historical_labels[-window_size:]
            
            if len(recent_embeddings) < 5:
                return BoundaryResult(
                    boundary_count=0,
                    total_count=len(embeddings),
                    boundary_ratio=0.0,
                    boundary_similarities=[],
                    strategy_used="cosine_range",
                    window_size=len(recent_embeddings),
                    quality_filtered=False,
                    thread_safe=self._lock is not None,
                    # 临时值，将在上层方法中更新
                    denominator_mode="pair_based",
                    unified_total_count=len(embeddings),
                    unified_boundary_ratio=0.0,
                    normalization_applied=False,
                    normalization_factor=1.0,
                    sample_pairs_count=0
                )
            
            # 🔧 修复：安全的tensor拼接处理
            try:
                # 验证所有tensor维度一致性
                valid_embeddings = []
                valid_labels = []
                for emb, lab in zip(recent_embeddings, recent_labels):
                    if emb.dim() == 2 and emb.size(1) == embeddings.size(1) and lab.dim() == 1:
                        valid_embeddings.append(emb)
                        valid_labels.append(lab)
                
                if len(valid_embeddings) == 0:
                    log_utils.warning("历史样本维度不匹配，无法进行边界检测", tag="BOUNDARY_MINER")
                    return BoundaryResult(
                        boundary_count=0,
                        total_count=len(embeddings),
                        boundary_ratio=0.0,
                        boundary_similarities=[],
                        strategy_used="cosine_range",
                        window_size=0,
                        quality_filtered=False,
                        thread_safe=self._lock is not None,
                        denominator_mode="pair_based",
                        unified_total_count=len(embeddings),
                        unified_boundary_ratio=0.0,
                        normalization_applied=False,
                        normalization_factor=1.0,
                        sample_pairs_count=0
                    )
                
                recent_emb_tensor = torch.cat(valid_embeddings, dim=0)
                recent_lab_tensor = torch.cat(valid_labels, dim=0)
                
            except Exception as tensor_error:
                log_utils.error(f"历史样本拼接失败: {tensor_error}", tag="BOUNDARY_MINER")
                return BoundaryResult(
                    boundary_count=0,
                    total_count=len(embeddings),
                    boundary_ratio=0.0,
                    boundary_similarities=[],
                    strategy_used="cosine_range",
                    window_size=0,
                    quality_filtered=False,
                    thread_safe=self._lock is not None,
                    denominator_mode="pair_based",
                    unified_total_count=len(embeddings),
                    unified_boundary_ratio=0.0,
                    normalization_applied=False,
                    normalization_factor=1.0,
                    sample_pairs_count=0
                )
            
            # 处理单样本情况
            boundary_count = 0
            boundary_similarities = []
            total_sample_pairs = 0
            
            # 🔧 修复：确保设备一致性
            current_device = embeddings.device
            
            for i, (embedding, label) in enumerate(zip(embeddings, labels)):
                # 归一化当前样本
                norm_embedding = l2_normalize(embedding.unsqueeze(0), p=2, dim=1)
                
                # 🔧 修复：确保历史样本与当前样本在同一设备上
                recent_emb_tensor_device = recent_emb_tensor.to(current_device)
                recent_lab_tensor_device = recent_lab_tensor.to(current_device)
                
                # 🔧 修复：统一归一化历史样本（在同一设备上）
                norm_recent_embeddings = l2_normalize(recent_emb_tensor_device, p=2, dim=1)
                
                # 计算相似度（现在两个tensor都在同一设备上）
                similarities = torch.mm(norm_embedding, norm_recent_embeddings.t()).squeeze()
                
                # 处理0维张量
                if similarities.dim() == 0:
                    similarities = similarities.unsqueeze(0)
                
                # 找出不同类别的样本
                current_label = label.item()
                diff_class_mask = (recent_lab_tensor_device != current_label)
                
                if diff_class_mask.sum() > 0:
                    diff_class_similarities = similarities[diff_class_mask]
                    total_sample_pairs += diff_class_similarities.size(0)
                    
                    # 检查边界样本
                    boundary_mask = (diff_class_similarities >= self.config.boundary_low) & \
                                   (diff_class_similarities <= self.config.boundary_high)
                    
                    if boundary_mask.sum() > 0:
                        boundary_count += boundary_mask.sum().item()
                        boundary_sims = diff_class_similarities[boundary_mask].detach().cpu().numpy().tolist()
                        boundary_similarities.extend(boundary_sims)
            
            boundary_ratio = boundary_count / max(total_sample_pairs, 1)
            
            return BoundaryResult(
                boundary_count=boundary_count,
                total_count=total_sample_pairs,
                boundary_ratio=boundary_ratio,
                boundary_similarities=boundary_similarities,
                strategy_used="cosine_range",
                window_size=len(valid_embeddings),
                quality_filtered=self.config.enable_quality_filter,
                thread_safe=self._lock is not None,
                # 临时值，将在上层方法中更新
                denominator_mode="pair_based",
                unified_total_count=total_sample_pairs,
                unified_boundary_ratio=boundary_ratio,
                normalization_applied=False,
                normalization_factor=1.0,
                sample_pairs_count=total_sample_pairs
            )
            
        except Exception as e:
            log_utils.error(f"CosineRange边界样本计算失败: {e}", tag="BOUNDARY_MINER")
            return BoundaryResult(
                boundary_count=0,
                total_count=len(embeddings),
                boundary_ratio=0.0,
                boundary_similarities=[],
                strategy_used="cosine_range",
                window_size=0,
                quality_filtered=False,
                thread_safe=self._lock is not None,
                # 临时值，将在上层方法中更新
                denominator_mode="pair_based",
                unified_total_count=len(embeddings),
                unified_boundary_ratio=0.0,
                normalization_applied=False,
                normalization_factor=1.0,
                sample_pairs_count=0
            )
    
    def update_config(self, **kwargs):
        """动态更新配置"""
        if self._lock:
            with self._lock:
                self._update_config_impl(**kwargs)
        else:
            self._update_config_impl(**kwargs)
    
    def _update_config_impl(self, **kwargs):
        """配置更新的具体实现"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                log_utils.debug(f"BoundaryMinerUtils配置更新: {key} = {value}", tag="BOUNDARY_MINER")
        
        # 更新PairMarginMiner参数
        if 'boundary_low' in kwargs or 'boundary_high' in kwargs:
            self._pair_miner = PairMarginMiner(
                pos_margin=self.config.boundary_high,
                neg_margin=self.config.boundary_low,
                distance=self._cosine_dist
            )
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        if self._lock:
            with self._lock:
                return self._stats.copy()
        else:
            return self._stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        if self._lock:
            with self._lock:
                self._reset_stats_impl()
        else:
            self._reset_stats_impl()
    
    def _reset_stats_impl(self):
        """重置统计信息的具体实现"""
        self._stats = {
            'total_calls': 0,
            'pair_margin_calls': 0,
            'cosine_range_calls': 0,
            'thread_safe_calls': 0,
            'quality_filtered_calls': 0
        }
    

