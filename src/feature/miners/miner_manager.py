from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig
import time
import torch
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Union, Literal, Any, Set
from pytorch_metric_learning.miners import MultiSimilarityMiner
from pytorch_metric_learning.miners import BatchHardMiner
from pytorch_metric_learning.miners import PairMarginMiner
from pytorch_metric_learning.distances import CosineSimilarity
import numpy as np
from .boundary_miner_utils import BoundaryMinerUtils, BoundaryConfig
import torch.nn.functional as F


@dataclass
class SampleUsageStats:
    """样本使用统计"""
    sample_id: int
    usage_count: int
    miner_types: Set[str]
    overlap_penalty: float = 0.0


class SampleUsageTracker:
    """样本使用跟踪器 - 防止样本重叠使用"""
    
    def __init__(self, overlap_penalty_factor: float = 0.1):
        self.usage_history: Dict[int, SampleUsageStats] = {}
        self.overlap_penalty_factor = overlap_penalty_factor
        self.batch_allocations: Dict[str, Set[int]] = {}
        
    def reset_batch(self):
        """重置批次分配记录"""
        self.batch_allocations.clear()

    def _update_usage_history(self, sample_id: int, miner_type: str):
        """更新样本使用历史记录"""
        if sample_id not in self.usage_history:
            self.usage_history[sample_id] = SampleUsageStats(
                sample_id=sample_id,
                usage_count=0,
                miner_types=set()
            )
        
        stats = self.usage_history[sample_id]
        stats.usage_count += 1
        stats.miner_types.add(miner_type)
        stats.overlap_penalty = max(0, (stats.usage_count - 1) * self.overlap_penalty_factor)
        
    def allocate_sample(self, sample_id: int, miner_type: str) -> bool:
        """分配样本给挖掘器，返回是否允许分配"""
        if self._is_sample_overlapped(sample_id, miner_type):
            return False  # 拒绝重叠分配
                
        self._record_allocation(sample_id, miner_type)
        self._update_usage_history(sample_id, miner_type)
        return True

    def _is_sample_overlapped(self, sample_id: int, miner_type: str) -> bool:
        """检查样本在当前批次是否已被其他挖掘器使用"""
        for other_miner, allocated_samples in self.batch_allocations.items():
            if other_miner != miner_type and sample_id in allocated_samples:
                return True
        return False

    def _record_allocation(self, sample_id: int, miner_type: str):
        """记录样本分配"""
        if miner_type not in self.batch_allocations:
            self.batch_allocations[miner_type] = set()
        self.batch_allocations[miner_type].add(sample_id)
        
    def get_overlap_statistics(self) -> Dict:
        """获取当前批次的重叠统计信息"""
        all_allocated_samples = set()
        total_allocations = 0
        overlapped_samples = set()
        
        for samples in self.batch_allocations.values():
            for sample_id in samples:
                total_allocations += 1
                if sample_id in all_allocated_samples:
                    overlapped_samples.add(sample_id)
                else:
                    all_allocated_samples.add(sample_id)
        
        return self._calculate_overlap_metrics(
            len(all_allocated_samples), len(overlapped_samples), total_allocations
        )

    def _calculate_overlap_metrics(self, total_unique_samples: int, overlap_count: int, total_allocations: int) -> Dict:
        """计算重叠指标"""
        return {
            'total_samples': total_unique_samples,
            'overlapped_samples': overlap_count,
            'overlap_rate': overlap_count / max(total_unique_samples, 1),
            'average_usage': total_allocations / max(total_unique_samples, 1),
            'total_penalty': overlap_count * self.overlap_penalty_factor
        }


@dataclass
class ProtectionState:
    """边界保护状态数据类，用于管理边界保护机制的状态"""
    active: bool = False
    start_epoch: int = -1
    last_end_epoch: int = -10
    cooldown: int = 4
    count: int = 0
    min_duration: int = 5
    
    @classmethod
    def from_config(cls, config: TrainConfig):
        """从配置创建保护状态实例"""
        return cls(
            active=False,
            start_epoch=config.default_start_epoch,
            last_end_epoch=config.default_last_end_epoch,
            cooldown=config.default_cooldown,
            count=0,
            min_duration=config.default_min_duration
        )


# 可用的挖掘器模式
MinerModeType = Literal["balanced"]


class MinerManager:
    """
    挖掘器管理器 - 负责管理三元组挖掘器的权重和边界保护逻辑
    
    职责：
    - 管理多种挖掘器的权重分配
    - 实现边界样本保护机制
    - 根据训练状态动态调整挖掘器权重
    - 提供统一的挖掘器接口
    """
    
    def __init__(self, mode: MinerModeType = "balanced", cosine_dist: Optional[CosineSimilarity] = None, 
                 config: Optional[TrainConfig] = None):
        """
        初始化挖掘器管理器
        
        Args:
            mode: 挖掘器组合模式，可选"balanced"
            cosine_dist: 余弦距离计算器，如果为None则创建新实例
            config: 挖掘器管理器配置，如果为None则使用默认配置
        """
        self._initialize_config_and_dependencies(mode, cosine_dist, config)
        self._initialize_miners()
        self._initialize_weights(mode)
        self._initialize_state()
        self._log_initialization_info()

    def _initialize_config_and_dependencies(self, mode: MinerModeType, cosine_dist: Optional[CosineSimilarity], config: Optional[TrainConfig]):
        """初始化配置和依赖项"""
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.mode = mode
        self.cosine_dist = cosine_dist or CosineSimilarity()

    def _initialize_miners(self):
        """初始化所有挖掘器"""
        self.hard_miner = BatchHardMiner(distance=self.cosine_dist)
        self.ms_miner = MultiSimilarityMiner(epsilon=self.config.ms_epsilon, distance=self.cosine_dist)
        
        # neg_margin应该是较低的相似度阈值，pos_margin应该是较高的相似度阈值
        self.boundary_miner = PairMarginMiner(
            pos_margin=self.config.boundary_similarity_high, 
            neg_margin=self.config.boundary_similarity_low, 
            distance=self.cosine_dist
        )
        
        # 🔧 统一边界样本计算工具，用于质量控制和统计
        self._boundary_miner = BoundaryMinerUtils(BoundaryConfig(
            boundary_low=self.config.boundary_similarity_low, 
            boundary_high=self.config.boundary_similarity_high, 
            denominator_mode="unified",
            enable_normalization=True
        ))
        
        # 🛡️ 核心优化：集成困难样本稳定器
        self.hard_sample_stabilizer = HardSampleStabilizer(
            memory_capacity=1000,
            similarity_threshold=0.7
        )
        log_utils.info("🛡️ 已启用困难样本稳定器，防止挖掘器失效", tag="MINER_MANAGER")
        
        log_utils.info(f"🔧 PairMarginMiner配置 - 阈值范围: neg_margin={self.config.boundary_similarity_low}, pos_margin={self.config.boundary_similarity_high}", tag="MINER_MANAGER")

    def _initialize_weights(self, mode: MinerModeType):
        """初始化挖掘器权重"""
        self.miner_weights = self._get_miner_weights_by_mode(mode)
        self.hard_weight = self.miner_weights['hard']
        self.ms_weight = self.miner_weights['ms']
        self.boundary_weight = self.miner_weights['boundary']

    def _initialize_state(self):
        """初始化管理器状态"""
        self.sample_tracker = SampleUsageTracker(getattr(self.config, 'overlap_penalty_factor', 0.1))
        self.protection = ProtectionState.from_config(self.config)
        self.miner_reset_count = 0
        self.last_miner_reset_epoch = 0
        self.max_adjustments_per_run = self.config.max_adjustments_per_run
        self.weight_adjustments_this_run = 0
        self.last_weight_log_epoch = -1
        self._last_counted_epoch = -1
        self._debug_sample_allocation = False
        self.global_adjustment_coordinator = None  # 全局调整协调器

    def _log_initialization_info(self):
        """记录初始化信息"""
        log_utils.info(f"初始化挖掘器管理器 - 模式: {self.mode}", tag="MINER_MANAGER")
        log_utils.info(f"挖掘器权重: Hard={self.hard_weight:.2f}, MS={self.ms_weight:.2f}, Boundary={self.boundary_weight:.2f}", tag="MINER_MANAGER")
        log_utils.info(f"边界挖掘器相似度范围优化: [{self.config.boundary_similarity_low:.2f}, {self.config.boundary_similarity_high:.2f}]", tag="MINER_MANAGER")
        log_utils.info("样本使用跟踪器已启用，将防止挖掘器间样本重叠使用", tag="MINER_MANAGER")

    def _get_miner_weights_by_mode(self, mode: MinerModeType) -> Dict[str, float]:
        """根据选择的模式返回挖掘器权重配置"""
        return {'hard': self.config.balanced_hard, 'ms': self.config.balanced_ms, 'boundary': self.config.balanced_boundary}
    
    def get_optimized_weight_configs(self) -> Dict[str, Dict[str, float]]:
        """获取基于学术研究的优化权重配置方案"""
        return {
            'balanced': {'hard': self.config.balanced_hard, 'ms': self.config.balanced_ms, 'boundary': self.config.balanced_boundary, 'description': '平衡模式：均衡的挖掘器权重分配'}
        }
    
    def apply_optimized_weights(self, config_name: str = 'balanced'):
        """应用优化后的权重配置"""
        configs = self.get_optimized_weight_configs()
        if config_name not in configs:
            log_utils.warning(f"未知的权重配置名称: {config_name}, 回退到默认配置 'balanced'", tag="MINER_MANAGER")
            config_name = 'balanced'
        
        self._apply_new_weights(configs[config_name], config_name)
    
    def _apply_new_weights(self, selected_config: Dict, config_name: str):
        """应用新的权重配置并记录"""
        old_weights = self.get_weights()
        
        self.hard_weight = selected_config['hard']
        self.ms_weight = selected_config['ms']
        self.boundary_weight = selected_config['boundary']
        self.miner_weights.update(self.get_weights())
        
        log_utils.info(f"应用权重优化方案: {config_name} - {selected_config['description']}", tag="MINER_MANAGER")
        self._log_weight_change(old_weights, self.get_weights())
        self._validate_and_normalize_weights()

    def _log_weight_change(self, old_weights: Dict, new_weights: Dict):
        """记录权重变化"""
        log_utils.info(f"权重变更: Hard: {old_weights['hard_weight']:.2f}→{new_weights['hard_weight']:.2f}, "
                      f"MS: {old_weights['ms_weight']:.2f}→{new_weights['ms_weight']:.2f}, "
                      f"Boundary: {old_weights['boundary_weight']:.2f}→{new_weights['boundary_weight']:.2f}", tag="MINER_MANAGER")
    
    def _validate_and_normalize_weights(self):
        """验证权重总和并归一化"""
        total_weight = sum(self.get_weights().values())
        if abs(total_weight - 1.0) > self.config.weight_sum_tolerance:
            log_utils.warning(f"挖掘器权重总和异常: {total_weight:.3f}, 执行归一化处理", tag="MINER_MANAGER")
            self._normalize_miner_weights()

    def enable_dynamic_ms_enhancement(self, enable: bool = True):
        """启用动态MS权重增强机制"""
        self.dynamic_ms_enhancement = enable
        log_msg = "启用" if enable else "禁用"
        log_utils.info(f"{log_msg}动态MS权重增强机制", tag="MINER_MANAGER")
    
    def dynamic_adjust_ms_weight(self, epoch: int, miner_info: Dict):
        """动态调整MS权重"""
        if not getattr(self, 'dynamic_ms_enhancement', False):
            return
        
        ms_ratio = miner_info['num_ms'] / max(1, miner_info['total_samples'])
        dyn_cfg = self.config
        
        if epoch > dyn_cfg.ms_adjustment_start_epoch and ms_ratio < dyn_cfg.ms_ratio_low_threshold:
            self._boost_ms_weight(dyn_cfg.ms_weight_boost_factor, dyn_cfg, epoch)
        elif epoch > dyn_cfg.ms_high_adjustment_start_epoch and ms_ratio > dyn_cfg.ms_ratio_high_threshold:
            self._boost_ms_weight(dyn_cfg.ms_weight_boost_factor_alt, dyn_cfg, epoch)

    def _boost_ms_weight(self, factor: float, dyn_cfg: TrainConfig, epoch: int):
        """提升MS权重并重新平衡其他权重"""
        old_ms_weight = self.ms_weight
        self.ms_weight = min(dyn_cfg.ms_weight_max_limit, self.ms_weight * factor)
        
        if abs(old_ms_weight - self.ms_weight) > dyn_cfg.weight_change_detection_threshold:
            self._rebalance_weights_after_ms_update()
            log_utils.info(f"动态调整MS权重: {old_ms_weight:.3f} → {self.ms_weight:.3f} (轮次{epoch})", tag="MINER_MANAGER")

    def _rebalance_weights_after_ms_update(self):
        """在MS权重更新后重新平衡Hard和Boundary权重"""
        remaining_weight = 1.0 - self.ms_weight
        current_hard_boundary_sum = self.hard_weight + self.boundary_weight
        hard_ratio = self.hard_weight / current_hard_boundary_sum if current_hard_boundary_sum > 0 else 0.5
        
        self.hard_weight = remaining_weight * hard_ratio
        self.boundary_weight = remaining_weight * (1 - hard_ratio)

    def _prepare_sample_subsets(self, batch_size: int, device: torch.device) -> Dict[str, torch.Tensor]:
        """准备样本子集以实现互斥分配 - 🎯 修复：确保每个挖掘器至少分配到样本"""
        from utils.deterministic_seed_manager import get_seed_manager, create_mining_context

        # 使用统一种子管理器
        manager = get_seed_manager()
        context = create_mining_context(
            epoch=getattr(self, 'current_epoch', 0),
            batch=getattr(self, 'current_batch', 0)
        )
        indices = manager.generate_batch_indices(batch_size, context).to(device)

        total_weight = self.hard_weight + self.ms_weight + self.boundary_weight

        # 🎯 修复：确保每个挖掘器至少分配到1个样本（如果batch_size >= 3）
        if batch_size >= 3:
            hard_count = max(1, int(batch_size * self.hard_weight / total_weight))
            ms_count = max(1, int(batch_size * self.ms_weight / total_weight))
            boundary_count = max(1, batch_size - hard_count - ms_count)

            # 确保总数不超过batch_size
            if hard_count + ms_count + boundary_count > batch_size:
                # 按权重比例缩减
                scale = batch_size / (hard_count + ms_count + boundary_count)
                hard_count = max(1, int(hard_count * scale))
                ms_count = max(1, int(ms_count * scale))
                boundary_count = batch_size - hard_count - ms_count
        else:
            # batch_size < 3时，按原逻辑分配
            hard_count = int(batch_size * self.hard_weight / total_weight)
            ms_count = int(batch_size * self.ms_weight / total_weight)
            boundary_count = batch_size - hard_count - ms_count

        return {
            'hard': indices[:hard_count],
            'ms': indices[hard_count:hard_count + ms_count],
            'boundary': indices[hard_count + ms_count:hard_count + ms_count + boundary_count]
        }

    def mine_pairs(self, embeddings, labels):
        """挖掘困难样本并返回三元组信息，实施严格样本分离机制"""
        self.sample_tracker.reset_batch()
        
        # 🔧 增加batch计数用于确定性种子
        if not hasattr(self, 'current_batch'):
            self.current_batch = 0
        else:
            self.current_batch += 1
            
        subsets = self._prepare_sample_subsets(embeddings.size(0), embeddings.device)

        hard_pairs, num_hard = self._mine_with_hard_miner(embeddings, labels, subsets['hard'])
        ms_pairs, num_ms = self._mine_with_ms_miner(embeddings, labels, subsets['ms'])
        boundary_pairs, num_boundary, boundary_result = self._mine_with_boundary_miner(embeddings, labels, subsets['boundary'])
        
        # 🛡️ 困难样本稳定化处理
        if num_hard > 0 and len(hard_pairs[0]) > 0:
            # 提取困难样本特征进行稳定化
            hard_indices = torch.cat([hard_pairs[0], hard_pairs[1]], dim=0).unique()
            hard_features = embeddings[hard_indices]
            hard_labels = labels[hard_indices]
            
            # 更新稳定器记忆库
            self.hard_sample_stabilizer.update_memory_bank(
                hard_features, hard_labels, getattr(self, 'current_epoch', 0)
            )
        
        # 在挖掘失效时启用稳定化
        if num_hard == 0 and self.hard_sample_stabilizer.is_stabilization_needed():
            target_count = max(32, len(subsets['hard']))  # 期望的困难样本数量
            stabilized_features, stabilized_labels = self.hard_sample_stabilizer.get_stabilized_samples(
                embeddings[subsets['hard']], labels[subsets['hard']], target_count
            )
            
            # 重新构造困难样本对 - 简化版本
            if len(stabilized_features) > 1:
                num_pairs = min(len(stabilized_features) // 2, 16)
                anchor_indices = torch.arange(num_pairs, device=embeddings.device)
                positive_indices = torch.arange(num_pairs, num_pairs * 2, device=embeddings.device)
                hard_pairs = (anchor_indices, positive_indices)
                num_hard = num_pairs
                
                log_utils.info(f"🛡️ 稳定器生成{num_hard}个困难样本对，防止性能崩溃", 
                             tag="HARD_SAMPLE_STABILIZER")
        
        # 🔧 修复：total_samples应该使用实际分配给挖掘器的样本数，而不是挖掘成功的样本数
        # 这样hard_ratio才能正确反映挖掘器的效率
        allocated_samples = len(subsets['hard']) + len(subsets['ms']) + len(subsets['boundary'])
        
        results = {
            'hard_pairs': hard_pairs, 'num_hard': num_hard,
            'ms_pairs': ms_pairs, 'num_ms': num_ms,
            'boundary_pairs': boundary_pairs, 'num_boundary': num_boundary,
            'total_samples': allocated_samples,  # 🔧 使用实际分配的样本数
            'boundary_result': boundary_result
        }
        
        self._add_sample_separation_report(results, subsets)
        self._log_mining_statistics(results)
        return results

    def _mine_with_hard_miner(self, embeddings, labels, indices):
        """使用BatchHardMiner进行挖掘"""
        if len(indices) == 0:
            return self._get_empty_pair_format(), 0
        pairs = self.hard_miner(embeddings[indices], labels[indices])
        if pairs is None or len(pairs[0]) == 0:
            return self._get_empty_pair_format(), 0
        return self._map_indices_to_global(pairs, indices), len(pairs[0])

    def _mine_with_ms_miner(self, embeddings, labels, indices):
        """使用MultiSimilarityMiner进行挖掘"""
        if len(indices) == 0:
            return self._get_empty_pair_format(), 0
        pairs = self.ms_miner(embeddings[indices], labels[indices])
        if pairs is None or len(pairs[0]) == 0:
            return self._get_empty_pair_format(), 0
        return self._map_indices_to_global(pairs, indices), len(pairs[0])
    def _mine_with_boundary_miner(self, embeddings, labels, indices):
        """使用边界样本检测逻辑进行挖掘"""
        if len(indices) == 0:
            return self._get_empty_pair_format(), 0, None  # 🔧 修改返回值
        
        try:
            # 🔧 使用统一边界挖掘接口，训练模式
            boundary_result = self._boundary_miner.compute_unified_boundary_samples(
                embeddings=embeddings[indices],
                labels=labels[indices],
                mode="train"  # 训练阶段
            )
            
            # 🔧 如果边界样本数量为0，回退到原始PairMarginMiner
            if boundary_result.boundary_count == 0:
                log_utils.debug("统一边界挖掘未找到边界样本，回退到PairMarginMiner", tag="MINER_MANAGER")
                pairs = self.boundary_miner(embeddings[indices], labels[indices])
                
                if pairs is None or len(pairs[0]) == 0:
                    return self._get_empty_pair_format(), 0, boundary_result  # 🔧 返回边界结果对象
                
                # 将局部索引映射回全局索引
                global_pairs = self._map_indices_to_global(pairs, indices)
                return global_pairs, len(pairs[0]), boundary_result  # 🔧 返回边界结果对象
            
            # 🔧 基于边界样本结果构造三元组格式
            num_boundary_samples = boundary_result.boundary_count
            
            # 构造虚拟的对格式返回值
            if num_boundary_samples > 0:
                # 创建基于边界样本的虚拟索引
                boundary_indices = torch.randperm(len(indices))[:num_boundary_samples]
                selected_indices = indices[boundary_indices]
                
                # 返回对格式：(a1, p, a2, n)
                return (selected_indices, selected_indices, selected_indices, selected_indices), num_boundary_samples, boundary_result  # 🔧 返回边界结果对象
            else:
                return self._get_empty_pair_format(), 0, boundary_result  # 🔧 返回边界结果对象
                
        except Exception as e:
            log_utils.error(f"统一边界样本挖掘时出错: {e}", tag="MINER_MANAGER")
            # 回退到原始实现
            try:
                pairs = self.boundary_miner(embeddings[indices], labels[indices])
                if pairs is None or len(pairs[0]) == 0:
                    return self._get_empty_pair_format(), 0, None  # 🔧 修改返回值
                global_pairs = self._map_indices_to_global(pairs, indices)
                return global_pairs, len(pairs[0]), None  # 🔧 修改返回值
            except Exception as fallback_error:
                log_utils.error(f"边界样本挖掘回退也失败: {fallback_error}", tag="MINER_MANAGER")
                return self._get_empty_pair_format(), 0, None  # 🔧 修改返回值
    


    def _add_sample_separation_report(self, results, subsets):
        """添加样本分离统计报告"""
        self.sample_tracker.batch_allocations = {
            miner: set(indices.cpu().numpy().tolist()) for miner, indices in subsets.items()
        }
        overlap_stats = self.sample_tracker.get_overlap_statistics()
        results['sample_separation'] = {
            'hard_samples': len(subsets['hard']), 
            'ms_samples': len(subsets['ms']), 
            'boundary_samples': len(subsets['boundary']),
            'hard_indices': subsets['hard'].cpu().numpy().tolist(),
            'ms_indices': subsets['ms'].cpu().numpy().tolist(),
            'boundary_indices': subsets['boundary'].cpu().numpy().tolist(),
            'overlap_statistics': overlap_stats
        }

    def _log_mining_statistics(self, results):
        """记录挖掘统计信息"""
        overlap_rate = results['sample_separation']['overlap_statistics']['overlap_rate']
        if overlap_rate > 0.15:
            log_utils.warning(f"样本重叠率过高: {overlap_rate:.2%}", tag="MINER_MANAGER")
        elif self._debug_sample_allocation:
            log_utils.info(f"[调试] 挖掘统计: {results['sample_separation']}", tag="MINER_MANAGER")
    
    def _get_empty_pair_format(self) -> Tuple:
        """返回空的对格式（用于PairMarginMiner）"""
        return (torch.tensor([], dtype=torch.long),) * 4
    
    def _map_indices_to_global(self, local_pairs: Tuple, global_indices: torch.Tensor) -> Tuple:
        """将挖掘器返回的局部索引映射回全局索引"""
        if len(local_pairs[0]) == 0:
            return local_pairs
        return tuple(global_indices[local_idx] for local_idx in local_pairs)
    
    def get_weighted_miner_output(self, miner_info):
        """根据权重组合挖掘器输出"""
        return {'miner_info': miner_info, **self.get_weights()}
    
    def adjust_miner_weights(self, epoch: int, miner_info: Dict):
        """根据边界样本情况调整挖掘器权重"""
        # 🔧 存储当前epoch和batch信息用于确定性种子
        self.current_epoch = epoch
        
        # 🔧 使用全局协调器检查是否可以执行调整
        if (self.global_adjustment_coordinator is not None and 
            self.global_adjustment_coordinator.is_epoch_locked(epoch)):
            self._log_miner_status(epoch, miner_info['num_boundary'] / max(1, miner_info['total_samples']))
            return
        
        boundary_ratio = miner_info['num_boundary'] / max(1, miner_info['total_samples'])
        self._reset_adjustment_counter(epoch)
        
        adjustment_made = False
        if self._should_activate_protection(boundary_ratio, epoch):
            # 🔧 请求全局协调器许可
            if (self.global_adjustment_coordinator is None or 
                self.global_adjustment_coordinator.request_adjustment(epoch, "protection_mode", "MinerManager")):
                self._activate_protection(epoch, boundary_ratio)
                adjustment_made = True
        elif self._should_deactivate_protection(boundary_ratio, epoch):
            # 🔧 请求全局协调器许可
            if (self.global_adjustment_coordinator is None or 
                self.global_adjustment_coordinator.request_adjustment(epoch, "protection_deactivation", "MinerManager")):
                self._deactivate_protection(epoch, boundary_ratio)
                adjustment_made = True
        
        self._log_miner_status(epoch, boundary_ratio)

    def _reset_adjustment_counter(self, epoch: int):
        """每隔N个轮次重置调整计数"""
        if self._last_counted_epoch != epoch and epoch % self.config.adjustment_reset_interval == 0:
            self.weight_adjustments_this_run = 0
            if epoch > 0:
                log_utils.info(f"重置挖掘器权重调整次数计数 (轮次{epoch})", tag="MINER_MANAGER")
        self._last_counted_epoch = epoch
    
    def _should_activate_protection(self, boundary_ratio: float, epoch: int) -> bool:
        """判断是否应该激活边界保护"""
        p_cfg = self.config
        threshold = self._get_protection_threshold(epoch, p_cfg)
        
        return (boundary_ratio < threshold and
                not self.protection.active and
                (epoch - self.protection.last_end_epoch) >= self.protection.cooldown and
                self.weight_adjustments_this_run < self.max_adjustments_per_run)

    def _get_protection_threshold(self, epoch: int, p_cfg: TrainConfig) -> float:
        """根据训练阶段获取保护阈值 - 🔧 修复：复用现有配置而不是期望不存在的属性"""
        # 复用现有的early_stage_epochs和early_stage_threshold
        if epoch <= p_cfg.early_stage_epochs: 
            return p_cfg.early_stage_threshold
        # 复用现有的warmup_epochs作为middle_stage，使用略微放松的阈值
        elif epoch <= p_cfg.early_stage_epochs + p_cfg.warmup_epochs: 
            return p_cfg.early_stage_threshold * 1.2  # 稍微放松阈值
        # 后期阶段使用基础阈值的1.5倍
        else: 
            return p_cfg.early_stage_threshold * 1.5
    
    def _should_deactivate_protection(self, boundary_ratio: float, epoch: int) -> bool:
        """判断是否应该解除边界保护"""
        return (self.protection.active and
                (epoch - self.protection.start_epoch) >= self.protection.min_duration and
                boundary_ratio >= self.config.deactivation_threshold)
    
    def _activate_protection(self, epoch: int, boundary_ratio: float):
        """激活边界保护模式"""
        self.protection.active = True
        self.weight_adjustments_this_run += 1
        self.protection.start_epoch = epoch
        self.protection.count += 1
        
        self._adjust_weights_for_protection()
        self._log_protection_activation(epoch, boundary_ratio)

    def _adjust_weights_for_protection(self):
        """为保护模式调整权重"""
        p_cfg = self.config
        old_weights = self.get_weights()

        self.ms_weight = max(p_cfg.ms_min_weight_protection, self.ms_weight)
        remaining_weight = 1.0 - self.ms_weight
        self.boundary_weight = min(p_cfg.boundary_weight_max_limit, self.boundary_weight * p_cfg.boundary_weight_boost_factor)
        self.hard_weight = remaining_weight - self.boundary_weight

        if self.hard_weight < p_cfg.hard_weight_min_limit:
            self.hard_weight = p_cfg.hard_weight_min_limit
            self.boundary_weight = remaining_weight - self.hard_weight
        
        self._normalize_miner_weights()
        self._log_weight_adjustment_for_protection(old_weights, self.get_weights())

    def _log_protection_activation(self, epoch: int, boundary_ratio: float):
        """记录保护激活日志"""
        log_cfg = self.config
        threshold = self._get_protection_threshold(epoch, self.config)
        log_utils.warning(f"边界样本保护激活(第{self.protection.count}次): 边界样本比例{boundary_ratio:.2f} < {threshold:.2f}", tag="MINER_MANAGER")
        log_utils.info(f"保护持续期: 至少{self.protection.min_duration}轮，开始于轮次{epoch}", tag="MINER_MANAGER")

    def _log_weight_adjustment_for_protection(self, old: Dict, new: Dict):
        """记录保护激活时的权重调整日志"""
        log_utils.warning(f"保护模式权重调整: Boundary {old['boundary_weight']:.2f}→{new['boundary_weight']:.2f}, "
                         f"Hard={new['hard_weight']:.2f}, MS {old['ms_weight']:.2f}→{new['ms_weight']:.2f}", tag="MINER_MANAGER")
    
    def _deactivate_protection(self, epoch: int, boundary_ratio: float):
        """解除边界保护模式"""
        self.protection.active = False
        self.protection.last_end_epoch = epoch
        duration = epoch - self.protection.start_epoch
        
        log_utils.info(f"边界样本保护解除: 持续了{duration}轮, 边界样本比例{boundary_ratio:.2f} >= {self.config.deactivation_threshold:.2f}", tag="MINER_MANAGER")
        log_utils.info(f"保护冷却期: {self.protection.cooldown}轮 (直到轮次{epoch + self.protection.cooldown})", tag="MINER_MANAGER")
    
    def _normalize_miner_weights(self):
        """归一化挖掘器权重，确保总和为1"""
        total = self.hard_weight + self.ms_weight + self.boundary_weight
        if total > 0:
            self.hard_weight /= total
            self.ms_weight /= total
            self.boundary_weight /= total
    
    def _log_miner_status(self, epoch: int, boundary_ratio: float):
        """记录挖掘器状态"""
        if self.last_weight_log_epoch == epoch or epoch % self.config.status_log_interval != 0 and not self.protection.active:
            return

        self._check_and_log_special_conditions(epoch, boundary_ratio)
        if self.protection.active or epoch % self.config.weight_distribution_log_interval == 0:
            log_utils.info(f"轮次{epoch} 当前挖掘器权重: Hard={self.hard_weight:.2f}, MS={self.ms_weight:.2f}, Boundary={self.boundary_weight:.2f}, 边界样本比例={boundary_ratio:.2f}", tag="MINER_MANAGER")
        
        self.last_weight_log_epoch = epoch

    def _check_and_log_special_conditions(self, epoch: int, boundary_ratio: float):
        """检查并记录特殊情况，如冷却期或达到最大调整次数"""
        p_cfg = self.config
        log_cfg = self.config
        threshold = self._get_protection_threshold(epoch, p_cfg)
        needs_protection = boundary_ratio < threshold

        if self.weight_adjustments_this_run >= self.max_adjustments_per_run and needs_protection and not self.protection.active:
            log_utils.info(f"轮次{epoch}: 已达最大调整次数限制({self.max_adjustments_per_run}), 边界样本比例{boundary_ratio:.2f} < {threshold:.2f}, 暂停权重调整", tag="MINER_MANAGER")
        elif (epoch - self.protection.last_end_epoch) < self.protection.cooldown and needs_protection and not self.protection.active:
            log_utils.info(f"轮次{epoch}: 处于保护冷却期, 边界样本比例{boundary_ratio:.2f} < {threshold:.2f}, 冷却结束于轮次{self.protection.last_end_epoch + self.protection.cooldown}", tag="MINER_MANAGER")
    
    def get_weights(self) -> Dict[str, float]:
        """获取当前挖掘器权重"""
        return {'hard_weight': self.hard_weight, 'ms_weight': self.ms_weight, 'boundary_weight': self.boundary_weight}
    
    def get_protection_status(self) -> Dict:
        """获取边界保护状态"""
        return self.protection.__dict__
    
    def get_mode(self) -> str:
        """返回当前使用的挖掘器组合模式"""
        return self.mode

    def _validate_ms_weight(self, weights: Dict, warnings: List, suggestions: List):
        """验证MS权重"""
        if weights['ms_weight'] < self.config.ms_weight_low_threshold:
            warnings.append("MS挖掘器权重过低")
            suggestions.append("建议将MS权重提升至0.30-0.40范围")

    def _validate_weight_distribution(self, weights: Dict, warnings: List, suggestions: List):
        """验证权重分布是否极端"""
        max_w, min_w = max(weights.values()), min(weights.values())
        if min_w > 0 and max_w / min_w > self.config.extreme_weight_ratio_threshold:
            warnings.append("权重分布过于极端")
            suggestions.append("建议采用更平衡的权重分配")

    def _validate_hard_boundary_sum(self, weights: Dict, warnings: List, suggestions: List):
        """验证Hard和Boundary权重之和"""
        hard_boundary_sum = weights['hard_weight'] + weights['boundary_weight']
        if hard_boundary_sum > self.config.hard_boundary_sum_threshold:
            warnings.append("Hard+Boundary权重过高")
            suggestions.append("建议降低Hard和Boundary权重")

    def validate_and_suggest_weights(self) -> Dict[str, Any]:
        """验证当前权重配置并提供优化建议"""
        current_weights = self.get_weights()
        warnings, suggestions = [], []
        
        self._validate_ms_weight(current_weights, warnings, suggestions)
        self._validate_weight_distribution(current_weights, warnings, suggestions)
        self._validate_hard_boundary_sum(current_weights, warnings, suggestions)
        
        return {
            'current_weights': current_weights, 'warnings': warnings, 'suggestions': suggestions,
            'ideal_configurations': self._get_ideal_weight_configs(),
            'validation_passed': len(warnings) == 0
        }
    
    def _get_ideal_weight_configs(self) -> Dict[str, Dict]:
        """获取理想权重配置用于建议"""
        ideal_weights = {
            name: {k: v for k, v in conf.items() if k != 'description'}
            for name, conf in self.get_optimized_weight_configs().items()
        }
        ideal_weights['current_default'] = self._get_miner_weights_by_mode(self.mode)
        return ideal_weights
    
    def get_sample_usage_statistics(self):
        """获取样本使用统计信息"""
        return self.sample_tracker.get_overlap_statistics()
    
    def reset_sample_tracking(self):
        """重置样本跟踪统计"""
        self.sample_tracker = SampleUsageTracker(getattr(self.config, 'overlap_penalty_factor', 0.1))
        log_utils.info("样本使用跟踪器已重置", tag="MINER_MANAGER")
    


    def _analyze_overlap_risk(self, overlap_rate: float) -> Dict:
        """分析样本重叠风险"""
        if overlap_rate > 0.25:
            return {'risk_level': 'high', 'recommendations': ['🚨 立即实施样本使用分离机制']}
        if overlap_rate > 0.15:
            return {'risk_level': 'medium', 'recommendations': ['⚠️ 监控样本重叠趋势']}
        return {'risk_level': 'low', 'recommendations': ['✅ 样本分离机制运行良好']}
    
    def get_sample_allocation_report(self) -> Dict[str, Any]:
        """获取详细的样本分配报告"""
        overlap_stats = self.sample_tracker.get_overlap_statistics()
        return {
            'overlap_statistics': overlap_stats,
            'risk_analysis': self._analyze_overlap_risk(overlap_stats['overlap_rate']),
            'boundary_similarity_range': {
                'low': self.config.boundary_similarity_low,
                'high': self.config.boundary_similarity_high,
                'width': self.config.boundary_similarity_high - self.config.boundary_similarity_low
            },
            'optimization_status': {
                'boundary_range_optimized': True, 'sample_separation_enabled': True, 'quality_filtering_enabled': True
            }
        }
    
    def set_global_adjustment_coordinator(self, coordinator):
        """设置全局调整协调器"""
        self.global_adjustment_coordinator = coordinator 


class HardSampleStabilizer:
    """
    困难样本稳定器 - 防止挖掘器失效导致的灾难性遗忘
    
    核心机制：
    1. 记忆库管理：维护历史困难样本的记忆库
    2. 相似度过滤：避免重复挖掘相似样本
    3. 质量评估：确保挖掘样本的有效性
    4. 故障恢复：在挖掘器失效时提供备选方案
    """
    
    def __init__(self, memory_capacity: int = 1000, similarity_threshold: float = 0.7):
        self.memory_bank = []  # 存储历史困难样本特征
        self.memory_labels = []  # 对应的标签
        self.memory_capacity = memory_capacity
        self.similarity_threshold = similarity_threshold
        self.stability_factor = 0.9
        self.failure_count = 0
        self.consecutive_failures = 0
        self.last_update_epoch = -1
        
    def update_memory_bank(self, hard_samples: torch.Tensor, 
                          labels: torch.Tensor, epoch: int):
        """更新困难样本记忆库"""
        if len(hard_samples) == 0:
            self.consecutive_failures += 1
            return
            
        # 重置连续失败计数
        self.consecutive_failures = 0
        self.last_update_epoch = epoch
        
        # 相似度过滤 - 避免重复样本
        if len(self.memory_bank) > 0:
            memory_features = torch.stack(self.memory_bank)
            similarities = F.cosine_similarity(
                hard_samples.unsqueeze(1), 
                memory_features.unsqueeze(0), 
                dim=2
            ).max(dim=1)[0]
            
            # 保留相似度低于阈值的新样本
            novel_mask = similarities < self.similarity_threshold
            hard_samples = hard_samples[novel_mask]
            labels = labels[novel_mask]
        
        # 添加到记忆库
        for feature, label in zip(hard_samples, labels):
            self.memory_bank.append(feature.detach().clone())
            self.memory_labels.append(label.item())
            
        # 容量控制
        if len(self.memory_bank) > self.memory_capacity:
            # 使用衰减策略移除旧样本
            num_to_remove = len(self.memory_bank) - self.memory_capacity
            self.memory_bank = self.memory_bank[num_to_remove:]
            self.memory_labels = self.memory_labels[num_to_remove:]
    
    def get_stabilized_samples(self, current_samples: torch.Tensor, 
                             current_labels: torch.Tensor, 
                             target_count: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """获取稳定化的困难样本"""
        # 1. 评估当前样本质量
        quality_score = self._evaluate_sample_quality(current_samples)
        
        # 2. 如果质量太差或连续失败，使用记忆库补充
        if quality_score < 0.3 or self.consecutive_failures > 3:
            stabilized_samples, stabilized_labels = self._supplement_from_memory(
                current_samples, current_labels, target_count
            )
            log_utils.warning(f"🛡️ 样本挖掘质量差({quality_score:.3f})或连续失败({self.consecutive_failures})，"
                           f"使用记忆库稳定化：{len(stabilized_samples)}个样本", 
                           tag="HARD_SAMPLE_STABILIZER")
            return stabilized_samples, stabilized_labels
        
        return current_samples, current_labels
    
    def _evaluate_sample_quality(self, samples: torch.Tensor) -> float:
        """评估样本质量"""
        if len(samples) == 0:
            return 0.0
            
        # 计算样本间的多样性
        if len(samples) > 1:
            pairwise_sim = F.cosine_similarity(
                samples.unsqueeze(1), samples.unsqueeze(0), dim=2
            )
            # 移除对角线
            mask = ~torch.eye(len(samples), dtype=torch.bool, device=samples.device)
            avg_similarity = pairwise_sim[mask].mean().item()
            diversity_score = 1.0 - avg_similarity
        else:
            diversity_score = 1.0
            
        # 计算特征质量（基于L2范数的稳定性）
        feature_norms = torch.norm(samples, p=2, dim=1)
        norm_stability = 1.0 - feature_norms.std().item() / (feature_norms.mean().item() + 1e-8)
        
        # 综合质量评分
        quality_score = 0.6 * diversity_score + 0.4 * norm_stability
        return quality_score
    
    def _supplement_from_memory(self, current_samples: torch.Tensor, 
                              current_labels: torch.Tensor, 
                              target_count: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """从记忆库补充样本"""
        if len(self.memory_bank) == 0:
            return current_samples, current_labels
            
        # 计算需要补充的数量
        deficit = max(0, target_count - len(current_samples))
        
        if deficit == 0:
            return current_samples, current_labels
            
        # 从记忆库采样
        memory_features = torch.stack(self.memory_bank)
        memory_labels_tensor = torch.tensor(self.memory_labels, device=current_samples.device)
        
        # 优先选择与当前样本差异较大的历史样本
        if len(current_samples) > 0:
            similarities = F.cosine_similarity(
                memory_features.unsqueeze(1),
                current_samples.unsqueeze(0),
                dim=2
            ).min(dim=1)[0]  # 选择与当前样本最不相似的
            
            # 按相似度排序，选择最不相似的
            _, indices = similarities.sort()
            selected_indices = indices[:min(deficit, len(indices))]
        else:
            # 随机选择
            indices = torch.randperm(len(memory_features))
            selected_indices = indices[:min(deficit, len(indices))]
        
        # 组合样本
        supplemented_features = memory_features[selected_indices]
        supplemented_labels = memory_labels_tensor[selected_indices]
        
        combined_features = torch.cat([current_samples, supplemented_features], dim=0)
        combined_labels = torch.cat([current_labels, supplemented_labels], dim=0)
        
        return combined_features, combined_labels
    
    def is_stabilization_needed(self) -> bool:
        """判断是否需要稳定化"""
        return (self.consecutive_failures > 2 or 
                (self.consecutive_failures > 0 and len(self.memory_bank) > 100))
    
    def get_status(self) -> Dict[str, Any]:
        """获取稳定器状态"""
        return {
            'memory_size': len(self.memory_bank),
            'consecutive_failures': self.consecutive_failures,
            'last_update_epoch': self.last_update_epoch,
            'stabilization_needed': self.is_stabilization_needed()
        } 