#!/usr/bin/env python3
"""
课程学习配置热更新工具

用于在训练过程中动态调整恢复参数，无需重启训练。

职责：
- 热更新恢复配置
- 验证配置有效性
- 备份和恢复配置
- 配置版本管理

使用方法:
python config_updater.py --key detection.zero_recognition_tolerance --value 1
python config_updater.py --preset aggressive
python config_updater.py --backup
python config_updater.py --restore backup_20250117.json

Dependencies:
- argparse
- json
- datetime
- .recovery_config
"""

import argparse
import json
import os
import shutil
from datetime import datetime
from typing import Dict, Any, Optional
from .recovery_config import (
    EmergencyRecoveryConfig, DEFAULT_RECOVERY_CONFIG,
    CrashDetectionConfig, RecoveryStrategyConfig,
    RecoverySamplingConfig, RecoveryMonitoringConfig
)
from utils import log_utils


class ConfigUpdater:
    """配置更新器"""
    
    def __init__(self, config_path: str = "recovery_config.json"):
        self.config_path = config_path
        self.backup_dir = "config_backups"
        self.current_config = DEFAULT_RECOVERY_CONFIG
        
        # 确保备份目录存在
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # 加载现有配置
        self.load_config()
    
    def load_config(self) -> EmergencyRecoveryConfig:
        """加载配置文件"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_dict = json.load(f)
                
                # 重建配置对象（简化版）
                self.current_config = self._dict_to_config(config_dict)
                log_utils.info(f"✅ 加载配置文件: {self.config_path}", tag="CONFIG_UPDATER")
                
            except Exception as e:
                log_utils.warning(f"⚠️ 配置文件加载失败，使用默认配置: {e}", tag="CONFIG_UPDATER")
                self.current_config = DEFAULT_RECOVERY_CONFIG
        else:
            log_utils.info("📝 使用默认配置", tag="CONFIG_UPDATER")
            self.current_config = DEFAULT_RECOVERY_CONFIG
        
        return self.current_config
    
    def save_config(self):
        """保存配置文件"""
        config_dict = self._config_to_dict(self.current_config)
        
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        log_utils.info(f"💾 保存配置文件: {self.config_path}", tag="CONFIG_UPDATER")
    
    def backup_config(self) -> str:
        """备份当前配置"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(self.backup_dir, f"backup_{timestamp}.json")
        
        if os.path.exists(self.config_path):
            shutil.copy2(self.config_path, backup_path)
            log_utils.info(f"📦 配置已备份到: {backup_path}", tag="CONFIG_UPDATER")
        else:
            # 备份默认配置
            config_dict = self._config_to_dict(self.current_config)
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            log_utils.info(f"📦 默认配置已备份到: {backup_path}", tag="CONFIG_UPDATER")
        
        return backup_path
    
    def restore_config(self, backup_path: str):
        """恢复配置"""
        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"备份文件不存在: {backup_path}")
        
        shutil.copy2(backup_path, self.config_path)
        self.load_config()
        
        log_utils.info(f"🔄 配置已从备份恢复: {backup_path}", tag="CONFIG_UPDATER")
    
    def update_config_value(self, key_path: str, value: Any):
        """更新配置值"""
        # 解析键路径 (例如: "detection.zero_recognition_tolerance")
        keys = key_path.split('.')
        
        # 转换配置为字典进行修改
        config_dict = self._config_to_dict(self.current_config)
        
        # 导航到目标位置
        current = config_dict
        for key in keys[:-1]:
            if key not in current:
                raise KeyError(f"配置路径不存在: {'.'.join(keys[:keys.index(key)+1])}")
            current = current[key]
        
        # 更新值
        final_key = keys[-1]
        if final_key not in current:
            raise KeyError(f"配置键不存在: {key_path}")
        
        old_value = current[final_key]
        current[final_key] = value
        
        # 重建配置对象
        self.current_config = self._dict_to_config(config_dict)
        
        log_utils.info(f"🔧 更新配置: {key_path} = {old_value} → {value}", tag="CONFIG_UPDATER")
    
    def apply_preset(self, preset_name: str):
        """应用预设配置"""
        presets = {
            'conservative': {
                'detection.zero_recognition_tolerance': 3,
                'detection.performance_drop_threshold': 0.7,
                'strategy.moderate_recovery.easy_ratio_adjustment': 0.15,
                'strategy.moderate_recovery.mining_weight_reduction': 0.8
            },
            'aggressive': {
                'detection.zero_recognition_tolerance': 1,
                'detection.performance_drop_threshold': 0.4,
                'strategy.moderate_recovery.easy_ratio_adjustment': 0.25,
                'strategy.moderate_recovery.mining_weight_reduction': 0.6
            },
            'balanced': {
                'detection.zero_recognition_tolerance': 2,
                'detection.performance_drop_threshold': 0.5,
                'strategy.moderate_recovery.easy_ratio_adjustment': 0.18,
                'strategy.moderate_recovery.mining_weight_reduction': 0.7
            }
        }
        
        if preset_name not in presets:
            raise ValueError(f"未知预设: {preset_name}. 可用预设: {list(presets.keys())}")
        
        preset_config = presets[preset_name]
        
        log_utils.info(f"🎛️ 应用预设配置: {preset_name}", tag="CONFIG_UPDATER")
        
        for key_path, value in preset_config.items():
            self.update_config_value(key_path, value)
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 基本范围检查
            detection = self.current_config.detection
            
            if not (0 < detection.zero_recognition_tolerance <= 5):
                raise ValueError("zero_recognition_tolerance 应该在 (0, 5] 范围内")
            
            if not (0.1 <= detection.performance_drop_threshold <= 1.0):
                raise ValueError("performance_drop_threshold 应该在 [0.1, 1.0] 范围内")
            
            if not (1.0 <= detection.loss_explosion_threshold <= 10.0):
                raise ValueError("loss_explosion_threshold 应该在 [1.0, 10.0] 范围内")
            
            # 策略参数检查
            for strategy_name in ['aggressive_recovery', 'moderate_recovery', 'gentle_recovery']:
                strategy = getattr(self.current_config.strategy, strategy_name)
                
                if not (0.05 <= strategy['easy_ratio_adjustment'] <= 0.5):
                    raise ValueError(f"{strategy_name}.easy_ratio_adjustment 应该在 [0.05, 0.5] 范围内")
                
                if not (0.3 <= strategy['mining_weight_reduction'] <= 1.0):
                    raise ValueError(f"{strategy_name}.mining_weight_reduction 应该在 [0.3, 1.0] 范围内")
            
            log_utils.info("✅ 配置验证通过", tag="CONFIG_UPDATER")
            return True
            
        except Exception as e:
            log_utils.error(f"❌ 配置验证失败: {e}", tag="CONFIG_UPDATER")
            return False
    
    def _config_to_dict(self, config: EmergencyRecoveryConfig) -> Dict[str, Any]:
        """配置对象转字典（简化版）"""
        return {
            'detection': {
                'zero_recognition_tolerance': config.detection.zero_recognition_tolerance,
                'performance_drop_threshold': config.detection.performance_drop_threshold,
                'loss_explosion_threshold': config.detection.loss_explosion_threshold,
                'boundary_sample_threshold': config.detection.boundary_sample_threshold,
                'feature_quality_threshold': config.detection.feature_quality_threshold
            },
            'strategy': {
                'aggressive_recovery': config.strategy.aggressive_recovery,
                'moderate_recovery': config.strategy.moderate_recovery,
                'gentle_recovery': config.strategy.gentle_recovery
            },
            'sampling': {
                'enable_special_sampling': config.sampling.enable_special_sampling,
                'recovery_sample_distribution': config.sampling.recovery_sample_distribution,
                'essential_sample_size': config.sampling.essential_sample_size
            },
            'monitoring': {
                'monitoring_interval': config.monitoring.monitoring_interval,
                'detailed_logging': config.monitoring.detailed_logging
            },
            'enable_emergency_recovery': config.enable_emergency_recovery,
            'debug_mode': config.debug_mode
        }
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> EmergencyRecoveryConfig:
        """字典转配置对象（简化版）"""
        # 这里应该完整重建配置对象，简化版本直接返回默认配置
        # 在实际使用中需要完整实现
        return DEFAULT_RECOVERY_CONFIG


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description="课程学习恢复配置更新工具")
    
    parser.add_argument('--key', type=str, help='配置键路径 (例如: detection.zero_recognition_tolerance)')
    parser.add_argument('--value', type=str, help='配置值')
    parser.add_argument('--preset', type=str, choices=['conservative', 'aggressive', 'balanced'], 
                       help='应用预设配置')
    parser.add_argument('--backup', action='store_true', help='备份当前配置')
    parser.add_argument('--restore', type=str, help='从备份恢复配置')
    parser.add_argument('--validate', action='store_true', help='验证配置有效性')
    parser.add_argument('--config-path', type=str, default='recovery_config.json', 
                       help='配置文件路径')
    
    args = parser.parse_args()
    
    updater = ConfigUpdater(args.config_path)
    
    try:
        if args.backup:
            backup_path = updater.backup_config()
            print(f"✅ 配置已备份到: {backup_path}")
        
        elif args.restore:
            updater.restore_config(args.restore)
            print(f"✅ 配置已从 {args.restore} 恢复")
        
        elif args.preset:
            updater.backup_config()  # 自动备份
            updater.apply_preset(args.preset)
            updater.save_config()
            print(f"✅ 已应用预设配置: {args.preset}")
        
        elif args.key and args.value:
            updater.backup_config()  # 自动备份
            
            # 类型转换
            value = args.value
            try:
                if '.' in value:
                    value = float(value)
                elif value.isdigit():
                    value = int(value)
                elif value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
            except:
                pass  # 保持字符串类型
            
            updater.update_config_value(args.key, value)
            updater.save_config()
            print(f"✅ 已更新配置: {args.key} = {value}")
        
        elif args.validate:
            if updater.validate_config():
                print("✅ 配置验证通过")
            else:
                print("❌ 配置验证失败")
                exit(1)
        
        else:
            parser.print_help()
    
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        exit(1)


if __name__ == "__main__":
    main()
