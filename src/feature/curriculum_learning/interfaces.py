"""
课程学习回调接口模块

定义课程学习系统的回调接口，提供扩展点。

职责：
- 定义回调接口规范
- 提供事件触发点
- 支持自定义回调实现

Dependencies:
- abc (标准库)
- typing (标准库)
"""

from abc import ABC, abstractmethod
from typing import Dict, Any


class CurriculumLearningCallback(ABC):
    """课程学习回调接口"""
    
    @abstractmethod
    def on_curriculum_start(self, scheduler: 'CurriculumScheduler', epoch: int):
        """课程学习开始时的回调"""
        pass
    
    @abstractmethod
    def on_difficulty_update(self, scheduler: 'CurriculumScheduler', 
                           difficulty_stats: Dict[str, float]):
        """难度更新时的回调"""
        pass
    
    @abstractmethod
    def on_curriculum_progress(self, scheduler: 'CurriculumScheduler', 
                             stage: int, easy_ratio: float):
        """课程进展时的回调"""
        pass
    
    @abstractmethod
    def on_stage_transition(self, scheduler: 'CurriculumScheduler', 
                          old_stage: int, new_stage: int):
        """阶段转换时的回调"""
        pass
    
    @abstractmethod
    def on_stagnation_detected(self, scheduler: 'CurriculumScheduler', 
                             health_status: Dict[str, float]):
        """停滞检测时的回调"""
        pass
    
    @abstractmethod
    def on_performance_feedback(self, scheduler: 'CurriculumScheduler', 
                              metrics: Dict[str, float], adjustment: str):
        """性能反馈时的回调"""
        pass
    
    @abstractmethod
    def on_emergency_recovery(self, scheduler: 'CurriculumScheduler', 
                            strategy: Dict[str, Any], new_stage: int, new_easy_ratio: float):
        """紧急恢复时的回调"""
        pass 