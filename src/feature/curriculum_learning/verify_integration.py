#!/usr/bin/env python3
"""
课程学习优化集成验证脚本

用于验证我们的优化是否正确集成到训练流程中。

职责：
- 验证工厂函数集成
- 检查恢复配置传递
- 模拟崩溃检测
- 确认日志输出

Dependencies:
- .factory
- .scheduler
- .recovery_config
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

from factory import create_curriculum_learning_system
from recovery_config import DEFAULT_RECOVERY_CONFIG
from config import DEFAULT_TRAIN_CONFIG
from utils import log_utils


def test_factory_integration():
    """测试工厂函数集成"""
    print("🧪 测试工厂函数集成...")
    
    try:
        # 创建课程学习系统（应该包含我们的优化）
        scheduler, sampler, monitor = create_curriculum_learning_system(
            config=DEFAULT_TRAIN_CONFIG,
            enable_monitoring=True,
            recovery_config=DEFAULT_RECOVERY_CONFIG
        )
        
        # 验证调度器包含崩溃检测器
        assert hasattr(scheduler, 'crash_detector'), "调度器缺少崩溃检测器"
        assert hasattr(scheduler.crash_detector, 'recovery_config'), "崩溃检测器缺少恢复配置"
        
        # 验证恢复配置
        recovery_config = scheduler.crash_detector.recovery_config
        assert recovery_config.detection.zero_recognition_tolerance == 1, \
            f"零识别容忍度应为1，实际为{recovery_config.detection.zero_recognition_tolerance}"
        
        assert recovery_config.sampling.enable_special_sampling == True, \
            "特殊采样应该启用"
        
        print("✅ 工厂函数集成测试通过")
        return scheduler, sampler, monitor
        
    except Exception as e:
        print(f"❌ 工厂函数集成测试失败: {e}")
        raise


def test_crash_detection_integration():
    """测试崩溃检测集成"""
    print("🧪 测试崩溃检测集成...")
    
    scheduler, _, _ = test_factory_integration()
    
    try:
        # 模拟正常性能数据
        for epoch in range(3):
            performance_metrics = {
                'val_acc': 0.8,
                'boundary_samples': 15,
                'feature_quality_score': 0.75
            }
            scheduler.update_curriculum(
                epoch=epoch,
                performance_metrics=performance_metrics,
                recognition_count=20,
                total_loss=1.5
            )
            
            state = scheduler.get_current_curriculum_state()
            assert state.get('recovery_state', 'normal') == 'normal', \
                f"正常情况下恢复状态应为normal，实际为{state.get('recovery_state')}"
        
        # 模拟崩溃情况
        crash_epoch = 3
        performance_metrics = {
            'val_acc': 0.4,
            'boundary_samples': 2,
            'feature_quality_score': 0.3
        }
        scheduler.update_curriculum(
            epoch=crash_epoch,
            performance_metrics=performance_metrics,
            recognition_count=0,  # 零识别
            total_loss=3.0
        )
        
        state = scheduler.get_current_curriculum_state()
        recovery_state = state.get('recovery_state', 'normal')
        
        # 验证崩溃检测
        if recovery_state in ['crashed', 'recovering']:
            print(f"✅ 崩溃检测成功触发: {recovery_state}")
        else:
            print(f"⚠️ 崩溃检测未触发，当前状态: {recovery_state}")
        
        # 验证简单样本比例调整
        easy_ratio = state.get('easy_ratio', 0.0)
        if easy_ratio > 0.8:  # 应该提升到较高水平
            print(f"✅ 简单样本比例已调整: {easy_ratio:.3f}")
        else:
            print(f"⚠️ 简单样本比例调整不明显: {easy_ratio:.3f}")
        
        print("✅ 崩溃检测集成测试完成")
        
    except Exception as e:
        print(f"❌ 崩溃检测集成测试失败: {e}")
        raise


def test_recovery_config_effectiveness():
    """测试恢复配置有效性"""
    print("🧪 测试恢复配置有效性...")
    
    try:
        # 测试默认配置
        default_scheduler, _, _ = create_curriculum_learning_system()
        
        # 测试自定义配置
        from feature.curriculum_learning.recovery_config import EmergencyRecoveryConfig, CrashDetectionConfig
        
        custom_config = EmergencyRecoveryConfig(
            detection=CrashDetectionConfig(
                zero_recognition_tolerance=2,  # 更保守的设置
                performance_drop_threshold=0.7
            )
        )
        
        custom_scheduler, _, _ = create_curriculum_learning_system(
            recovery_config=custom_config
        )
        
        # 验证配置差异
        default_tolerance = default_scheduler.crash_detector.crash_thresholds['zero_recognition_tolerance']
        custom_tolerance = custom_scheduler.crash_detector.crash_thresholds['zero_recognition_tolerance']
        
        assert default_tolerance == 1, f"默认配置容忍度应为1，实际为{default_tolerance}"
        assert custom_tolerance == 2, f"自定义配置容忍度应为2，实际为{custom_tolerance}"
        
        print(f"✅ 配置有效性验证通过: 默认={default_tolerance}, 自定义={custom_tolerance}")
        
    except Exception as e:
        print(f"❌ 恢复配置有效性测试失败: {e}")
        raise


def test_training_integration():
    """测试训练集成"""
    print("🧪 测试训练集成...")
    
    try:
        # 模拟训练管理器的使用方式
        from train import CurriculumLearningManager
        
        manager = CurriculumLearningManager()
        manager.initialize_curriculum_system()
        
        # 验证管理器包含优化的调度器
        assert hasattr(manager.curriculum_scheduler, 'crash_detector'), \
            "训练管理器的调度器缺少崩溃检测器"
        
        # 模拟训练循环中的调用
        for epoch in range(1, 6):
            if manager.should_update_curriculum(epoch):
                performance_metrics = {
                    'val_acc': 0.7 if epoch < 4 else 0.3,  # 第4轮开始性能下降
                    'boundary_samples': 15 if epoch < 4 else 2,
                    'feature_quality_score': 0.75 if epoch < 4 else 0.3,
                    'total_loss': 1.5 if epoch < 4 else 3.0,
                    'target_recognition_count': 20 if epoch < 4 else 0
                }
                
                manager.update_curriculum(epoch, performance_metrics)
                
                state = manager.get_current_curriculum_state()
                recovery_state = state.get('recovery_state', 'normal')
                
                print(f"Epoch {epoch}: 恢复状态={recovery_state}, "
                      f"简单比例={state.get('easy_ratio', 0):.3f}")
        
        print("✅ 训练集成测试完成")
        
    except Exception as e:
        print(f"❌ 训练集成测试失败: {e}")
        raise


def main():
    """主测试函数"""
    print("🚀 开始课程学习优化集成验证...")
    
    try:
        test_factory_integration()
        test_crash_detection_integration()
        test_recovery_config_effectiveness()
        test_training_integration()
        
        print("\n🎉 所有集成验证测试通过！")
        print("✅ 您的优化已成功集成到训练流程中")
        print("✅ 紧急恢复系统已激活")
        print("✅ 崩溃检测阈值已优化")
        print("✅ 特殊采样策略已启用")
        
        # 输出关键配置信息
        print("\n📋 当前恢复配置:")
        config = DEFAULT_RECOVERY_CONFIG
        print(f"  - 零识别容忍度: {config.detection.zero_recognition_tolerance}")
        print(f"  - 性能下降阈值: {config.detection.performance_drop_threshold}")
        print(f"  - 边界样本阈值: {config.detection.boundary_sample_threshold}")
        print(f"  - 特征质量阈值: {config.detection.feature_quality_threshold}")
        print(f"  - 特殊采样启用: {config.sampling.enable_special_sampling}")
        
        print("\n💡 建议:")
        print("  1. 运行训练时关注 'CRASH_DETECTOR' 和 'EMERGENCY_RECOVERY' 标签的日志")
        print("  2. 如需调整参数，使用 config_updater.py 工具")
        print("  3. 监控 boundary_samples 和 feature_quality_score 指标")
        
    except Exception as e:
        print(f"\n❌ 集成验证失败: {e}")
        print("请检查代码集成是否正确")
        sys.exit(1)


if __name__ == "__main__":
    main()
