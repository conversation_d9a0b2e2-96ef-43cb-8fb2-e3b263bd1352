"""
课程感知采样器模块

负责批次采样和类别平衡处理。

职责：
- 批次采样逻辑
- 难度缓存管理
- 类别平衡处理
- 缓存优化

Dependencies:
- .scheduler (本模块)
- numpy
- random
- collections
- deterministic_seed_manager
"""

import random
import numpy as np
from collections import defaultdict
from typing import Dict, List, Optional, Tuple
from .scheduler import CurriculumScheduler
from utils.deterministic_seed_manager import get_seed_manager, create_meta_learning_context


class CurriculumAwareSampler:
    """课程感知采样器"""
    
    def __init__(self, 
                 scheduler: CurriculumScheduler,
                 difficulty_cache_size: int = 1000,
                 cache_update_frequency: int = 10,
                 maintain_class_balance: bool = True,
                 min_samples_per_class: int = 2):
        self.scheduler = scheduler
        self.difficulty_cache_size = difficulty_cache_size
        self.cache_update_frequency = cache_update_frequency
        self.maintain_class_balance = maintain_class_balance
        self.min_samples_per_class = min_samples_per_class
        self.difficulty_cache: Dict[int, float] = {}
        self.cache_update_counter = 0
    
    def sample_batch(self, available_samples: List[int], batch_size: int, 
                    class_labels: Optional[Dict[int, int]] = None) -> Tuple[List[int], np.ndarray]:
        """采样批次"""
        self._maybe_update_difficulty_cache(available_samples)
        
        difficulties = self._get_cached_difficulties(available_samples)
        
        selected_samples = self.scheduler.select_samples_by_curriculum(available_samples, difficulties)
        selected_samples = selected_samples[:min(len(selected_samples), batch_size)]
        
        selected_difficulties = self._get_cached_difficulties(selected_samples)
        sample_weights = self.scheduler.get_sample_weights(selected_samples, selected_difficulties)
        
        if self.maintain_class_balance and class_labels:
            return self._balance_classes(selected_samples, sample_weights, class_labels)
        
        return selected_samples, sample_weights
    
    def _maybe_update_difficulty_cache(self, sample_ids: List[int]):
        """可能更新难度缓存"""
        if self.cache_update_counter % self.cache_update_frequency == 0:
            self._update_difficulty_cache(sample_ids)
        self.cache_update_counter += 1

    def _update_difficulty_cache(self, sample_ids: List[int]):
        """更新难度缓存"""
        difficulties = self.scheduler.difficulty_estimator.estimate_difficulty(sample_ids)
        for i, sample_id in enumerate(sample_ids):
            self.difficulty_cache[sample_id] = difficulties[i]
        self._evict_old_cache_entries()

    def _evict_old_cache_entries(self):
        """清理旧的缓存条目"""
        if len(self.difficulty_cache) > self.difficulty_cache_size:
            num_to_evict = len(self.difficulty_cache) - self.difficulty_cache_size
            keys_to_evict = list(self.difficulty_cache.keys())[:num_to_evict]
            for key in keys_to_evict:
                del self.difficulty_cache[key]
    
    def _get_cached_difficulties(self, sample_ids: List[int]) -> np.ndarray:
        """获取缓存的难度"""
        return np.array([self.difficulty_cache.get(sid, 0.5) for sid in sample_ids])
    
    def _balance_classes(self, sample_ids: List[int], weights: np.ndarray, 
                        class_labels: Dict[int, int]) -> Tuple[List[int], np.ndarray]:
        """平衡类别"""
        class_samples, class_weights = self._group_by_class(sample_ids, weights, class_labels)

        balanced_samples, balanced_weights = [], []
        for class_id, samples in class_samples.items():
            if not samples: 
                continue
            
            num_to_select = min(len(samples), self.min_samples_per_class * 2)
            if len(samples) >= self.min_samples_per_class:
                # 使用确定性种子管理器
                seed_manager = get_seed_manager()
                context = create_meta_learning_context(epoch=0)  # 可以传入实际epoch
                shuffled_indices = seed_manager.shuffle_list(list(range(len(samples))), context)
                indices = shuffled_indices[:num_to_select]
                balanced_samples.extend([samples[i] for i in indices])
                balanced_weights.extend([class_weights[class_id][i] for i in indices])
            else: # 不足时全部采用
                balanced_samples.extend(samples)
                balanced_weights.extend(class_weights[class_id])
                
        return balanced_samples, np.array(balanced_weights) if balanced_weights else np.array([])

    def _group_by_class(self, s_ids: List[int], s_weights: np.ndarray, c_labels: Dict[int, int]) -> Tuple[Dict, Dict]:
        """按类别分组"""
        class_samples: Dict[int, List[int]] = defaultdict(list)
        class_weights: Dict[int, List[float]] = defaultdict(list)
        for i, sid in enumerate(s_ids):
            cid = c_labels.get(sid)
            if cid is not None:
                class_samples[cid].append(sid)
                class_weights[cid].append(s_weights[i])
        return class_samples, class_weights 