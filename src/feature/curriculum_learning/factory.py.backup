"""
课程学习系统工厂模块

负责创建完整的课程学习系统，包含所有组件的组装。

职责：
- 系统组装
- 配置管理
- 组件集成

Dependencies:
- .scheduler (本模块)
- .sampler (本模块)
- .callbacks (本模块)
- src.config
"""

from typing import Optional, Tuple
from .scheduler import CurriculumScheduler
from .sampler import CurriculumAwareSampler
from .callbacks import CurriculumMonitorCallback
from .recovery_config import EmergencyRecoveryConfig, DEFAULT_RECOVERY_CONFIG
from config import TrainConfig, DEFAULT_TRAIN_CONFIG


def create_curriculum_learning_system(config: Optional[TrainConfig] = None,
                                    difficulty_cache_size: int = 1000,
                                    cache_update_frequency: int = 10,
                                    maintain_class_balance: bool = True,
                                    min_samples_per_class: int = 2,
                                    enable_monitoring: bool = True,
                                    recovery_config: Optional[EmergencyRecoveryConfig] = None
                                    ) -> <PERSON><PERSON>[CurriculumScheduler, CurriculumAwareSampler, Optional[CurriculumMonitorCallback]]:
    """创建完整的课程学习系统 - 集成优化的恢复机制"""
    callbacks = []
    monitor_callback = None
    if enable_monitoring:
        monitor_callback = CurriculumMonitorCallback()
        callbacks.append(monitor_callback)

    # 🚨 关键修复：传递恢复配置，激活优化的紧急恢复系统
    effective_recovery_config = recovery_config or DEFAULT_RECOVERY_CONFIG
    scheduler = CurriculumScheduler(
        config=config,
        callbacks=callbacks,
        recovery_config=effective_recovery_config
    )

    sampler = CurriculumAwareSampler(
        scheduler=scheduler,
        difficulty_cache_size=difficulty_cache_size,
        cache_update_frequency=cache_update_frequency,
        maintain_class_balance=maintain_class_balance,
        min_samples_per_class=min_samples_per_class
    )

    # 记录恢复系统状态
    from utils import log_utils
    log_utils.info(f"🚨 课程学习系统已启用优化恢复机制: "
                   f"检测阈值={effective_recovery_config.detection.zero_recognition_tolerance}, "
                   f"性能下降阈值={effective_recovery_config.detection.performance_drop_threshold}",
                   tag="CURRICULUM_FACTORY")

    return scheduler, sampler, monitor_callback