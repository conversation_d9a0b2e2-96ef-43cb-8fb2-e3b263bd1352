"""
课程进展策略模块

专门处理各种课程进展算法。

职责：
- 线性进展策略
- 指数进展策略
- 阶梯式进展策略
- 自适应进展策略

Dependencies:
- numpy
"""

import numpy as np
from typing import Dict, List


class CurriculumProgressStrategies:
    """课程进展策略集合"""
    
    @staticmethod
    def linear_progress(current_epoch: int, config) -> float:
        """线性课程进展"""
        progress = min(1.0, (current_epoch - config.curriculum_start_epoch) * config.curriculum_speed)
        return np.interp(progress, [0, 1], [config.initial_easy_ratio, config.final_easy_ratio])

    @staticmethod
    def exponential_progress(current_epoch: int, config) -> float:
        """指数课程进展"""
        decay_rate = config.curriculum_speed
        progress = 1.0 - np.exp(-decay_rate * (current_epoch - config.curriculum_start_epoch))
        return np.interp(progress, [0, 1], [config.initial_easy_ratio, config.final_easy_ratio])
    
    @staticmethod
    def step_progress(current_epoch: int, current_stage: int, config) -> float:
        """阶梯式课程进展"""
        if config.enable_multi_stage and current_stage < len(config.stage_difficulties):
            return 1.0 - config.stage_difficulties[current_stage]
        else:
            step = (current_epoch - config.curriculum_start_epoch) // 10
            step_size = (config.initial_easy_ratio - config.final_easy_ratio) / 4.0
            return config.initial_easy_ratio - step * step_size
    
    @staticmethod
    def adaptive_progress(current_easy_ratio: float, performance_history: List[Dict], config) -> float:
        """自适应课程进展"""
        if len(performance_history) < 2:
            return CurriculumProgressStrategies.linear_progress(0, config)

        perf_change = CurriculumProgressStrategies._calculate_performance_change(list(performance_history)[-2:])
        adjustment = 0.0
        if perf_change > config.performance_threshold:
            adjustment = config.adaptation_rate
        elif perf_change < -config.performance_decline_threshold:
            adjustment = -config.adaptation_rate * 0.5
        
        return current_easy_ratio - adjustment

    @staticmethod
    def _calculate_performance_change(recent_perf: List[Dict[str, float]]) -> float:
        """计算性能变化"""
        main_metrics = ['val_acc', 'separation_ratio', 'feature_quality_score']
        changes = []
        for metric in main_metrics:
            if metric in recent_perf[0] and metric in recent_perf[1]:
                changes.append(recent_perf[1][metric] - recent_perf[0][metric])
        return np.mean(changes) if changes else 0.0 