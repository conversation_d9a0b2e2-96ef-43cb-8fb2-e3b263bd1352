"""
恢复系统测试脚本

用于测试和验证紧急恢复机制的有效性。

职责：
- 模拟崩溃场景
- 测试恢复策略
- 验证采样器功能
- 性能基准测试

Dependencies:
- numpy
- typing
- .scheduler
- .recovery_config
- .recovery_sampler
"""

import numpy as np
from typing import Dict, List, Any
from .scheduler import CurriculumScheduler, PerformanceCrashDetector
from .recovery_config import EmergencyRecoveryConfig, DEFAULT_RECOVERY_CONFIG
from .recovery_sampler import RecoverySampler
from config import DEFAULT_TRAIN_CONFIG
from utils import log_utils


class RecoverySystemTester:
    """恢复系统测试器"""
    
    def __init__(self):
        self.recovery_config = DEFAULT_RECOVERY_CONFIG
        self.scheduler = CurriculumScheduler(
            config=DEFAULT_TRAIN_CONFIG,
            recovery_config=self.recovery_config
        )
        self.recovery_sampler = RecoverySampler(self.recovery_config)
        
    def test_crash_detection(self):
        """测试崩溃检测功能"""
        print("🧪 测试崩溃检测功能...")
        
        detector = PerformanceCrashDetector(recovery_config=self.recovery_config)
        
        # 模拟正常性能数据
        for epoch in range(5):
            detector.update_metrics(
                epoch=epoch,
                recognition_count=20 + np.random.randint(-3, 4),
                total_loss=1.5 + np.random.normal(0, 0.1),
                performance_metrics={
                    'val_acc': 0.8 + np.random.normal(0, 0.02),
                    'boundary_samples': 15 + np.random.randint(-2, 3),
                    'feature_quality_score': 0.75 + np.random.normal(0, 0.05)
                }
            )
            
            crash_info = detector.detect_crash(epoch)
            assert not crash_info['crash_detected'], f"误报崩溃在epoch {epoch}"
        
        # 模拟零识别崩溃
        for epoch in range(5, 8):
            detector.update_metrics(
                epoch=epoch,
                recognition_count=0,  # 连续零识别
                total_loss=2.0 + np.random.normal(0, 0.2),
                performance_metrics={
                    'val_acc': 0.6 + np.random.normal(0, 0.05),
                    'boundary_samples': 5,
                    'feature_quality_score': 0.4
                }
            )
            
            crash_info = detector.detect_crash(epoch)
            if epoch >= 6:  # 应该在第二次零识别时触发
                assert crash_info['crash_detected'], f"未检测到崩溃在epoch {epoch}"
                assert 'zero_recognition' in crash_info['crash_types']
                print(f"✅ 成功检测到零识别崩溃: epoch={epoch}, 严重程度={crash_info['severity']:.2f}")
                break
        
        print("✅ 崩溃检测测试通过")
    
    def test_recovery_strategies(self):
        """测试恢复策略"""
        print("🧪 测试恢复策略...")
        
        # 测试不同严重程度的恢复策略
        severities = [0.3, 0.6, 0.9]
        expected_types = ['gentle_recovery', 'moderate_recovery', 'aggressive_recovery']
        
        for severity, expected_type in zip(severities, expected_types):
            strategy = self.scheduler.crash_detector._get_recovery_strategy(severity)
            assert strategy['type'] == expected_type, \
                f"严重程度{severity}应该触发{expected_type}，实际触发{strategy['type']}"
            
            print(f"✅ 严重程度{severity:.1f} -> {strategy['type']}")
            print(f"   调整幅度: {strategy['easy_ratio_adjustment']:.2f}")
            print(f"   挖掘权重: {strategy['mining_weight_reduction']:.2f}")
            print(f"   持续时间: {strategy['duration']}轮")
        
        print("✅ 恢复策略测试通过")
    
    def test_recovery_sampler(self):
        """测试恢复期采样器"""
        print("🧪 测试恢复期采样器...")
        
        # 准备测试数据
        sample_pool = list(range(100))
        sample_difficulties = np.random.random(100)
        
        # 激活恢复采样
        self.recovery_sampler.activate_recovery_sampling('moderate_recovery', sample_pool)
        
        # 测试采样
        batch_size = 32
        selected_samples, weights = self.recovery_sampler.sample_for_recovery(
            sample_pool, sample_difficulties, batch_size
        )
        
        assert len(selected_samples) == batch_size, f"采样数量不正确: {len(selected_samples)} != {batch_size}"
        assert len(weights) == batch_size, f"权重数量不正确: {len(weights)} != {batch_size}"
        assert all(w > 0 for w in weights), "权重应该都为正数"
        
        # 检查采样分布
        stats = self.recovery_sampler.get_sampling_stats()
        print(f"✅ 采样统计: {stats}")
        
        # 停用恢复采样
        self.recovery_sampler.deactivate_recovery_sampling()
        
        print("✅ 恢复期采样器测试通过")
    
    def test_integrated_recovery_flow(self):
        """测试完整恢复流程"""
        print("🧪 测试完整恢复流程...")
        
        # 模拟训练过程
        for epoch in range(20):
            # 模拟性能指标
            if 8 <= epoch <= 10:  # 模拟崩溃期
                recognition_count = 0
                performance_metrics = {
                    'val_acc': 0.5,
                    'boundary_samples': 3,
                    'feature_quality_score': 0.3
                }
                total_loss = 3.0
            elif 11 <= epoch <= 13:  # 模拟恢复期
                recognition_count = 10 + (epoch - 11) * 5
                performance_metrics = {
                    'val_acc': 0.6 + (epoch - 11) * 0.05,
                    'boundary_samples': 8 + (epoch - 11) * 2,
                    'feature_quality_score': 0.5 + (epoch - 11) * 0.05
                }
                total_loss = 2.0 - (epoch - 11) * 0.2
            else:  # 正常期
                recognition_count = 20 + np.random.randint(-2, 3)
                performance_metrics = {
                    'val_acc': 0.8 + np.random.normal(0, 0.02),
                    'boundary_samples': 15 + np.random.randint(-2, 3),
                    'feature_quality_score': 0.75 + np.random.normal(0, 0.05)
                }
                total_loss = 1.5 + np.random.normal(0, 0.1)
            
            # 更新课程
            self.scheduler.update_curriculum(
                epoch=epoch,
                performance_metrics=performance_metrics,
                recognition_count=recognition_count,
                total_loss=total_loss
            )
            
            # 检查状态
            state = self.scheduler.get_current_curriculum_state()
            recovery_state = state.get('recovery_state', 'normal')
            
            print(f"Epoch {epoch:2d}: 识别={recognition_count:2d}, "
                  f"简单比例={state['easy_ratio']:.3f}, "
                  f"恢复状态={recovery_state}")
            
            # 验证恢复逻辑
            if epoch == 9:  # 应该触发恢复
                assert recovery_state in ['crashed', 'recovering'], \
                    f"Epoch {epoch}应该触发恢复，当前状态: {recovery_state}"
            elif epoch == 14:  # 应该恢复成功
                assert recovery_state == 'normal', \
                    f"Epoch {epoch}应该恢复成功，当前状态: {recovery_state}"
        
        print("✅ 完整恢复流程测试通过")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始恢复系统测试...")
        
        try:
            self.test_crash_detection()
            self.test_recovery_strategies()
            self.test_recovery_sampler()
            self.test_integrated_recovery_flow()
            
            print("🎉 所有测试通过！恢复系统工作正常")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            raise


def run_recovery_benchmark():
    """运行恢复系统基准测试"""
    print("📊 运行恢复系统基准测试...")
    
    # 测试不同配置的性能
    configs = [
        ("默认配置", DEFAULT_RECOVERY_CONFIG),
        ("激进配置", EmergencyRecoveryConfig(
            detection=DEFAULT_RECOVERY_CONFIG.detection,
            strategy=DEFAULT_RECOVERY_CONFIG.strategy
        )),
    ]
    
    for config_name, config in configs:
        print(f"\n测试配置: {config_name}")
        
        scheduler = CurriculumScheduler(
            config=DEFAULT_TRAIN_CONFIG,
            recovery_config=config
        )
        
        # 模拟100轮训练
        recovery_triggered = 0
        recovery_success = 0
        
        for epoch in range(100):
            # 随机模拟崩溃
            if np.random.random() < 0.05:  # 5%概率崩溃
                recognition_count = 0
                performance_metrics = {'val_acc': 0.4, 'boundary_samples': 2}
                total_loss = 4.0
            else:
                recognition_count = 15 + np.random.randint(-5, 6)
                performance_metrics = {'val_acc': 0.75 + np.random.normal(0, 0.1)}
                total_loss = 1.8 + np.random.normal(0, 0.3)
            
            scheduler.update_curriculum(epoch, performance_metrics, recognition_count, total_loss)
            
            state = scheduler.get_current_curriculum_state()
            if state.get('recovery_state') == 'crashed':
                recovery_triggered += 1
            elif state.get('recovery_state') == 'normal' and recovery_triggered > recovery_success:
                recovery_success += 1
        
        success_rate = recovery_success / max(1, recovery_triggered)
        print(f"  恢复触发次数: {recovery_triggered}")
        print(f"  恢复成功次数: {recovery_success}")
        print(f"  成功率: {success_rate:.2%}")


if __name__ == "__main__":
    # 运行测试
    tester = RecoverySystemTester()
    tester.run_all_tests()
    
    # 运行基准测试
    run_recovery_benchmark()
