"""
课程学习模块

实现基于难度的自适应样本选择和课程调度，支持：
1. 多种难度评估策略（基于损失、距离、置信度）
2. 自适应课程进展控制
3. 多阶段课程学习
4. 性能反馈机制
5. 样本重权重策略

设计原则：
- 与现有AdaptiveBatchSampler无缝集成
- 支持多种课程学习策略
- 提供完整的监控和回调机制
"""

from .interfaces import CurriculumLearningCallback
from .callbacks import CurriculumMonitorCallback
from .estimator import DifficultyEstimator
from .scheduler import CurriculumScheduler
from .sampler import CurriculumAwareSampler
from .factory import create_curriculum_learning_system
from .difficulty_metrics import DifficultyMetrics
from .progress_strategies import CurriculumProgressStrategies
from .selection_strategies import SampleSelectionStrategies
from .health_monitor import CurriculumHealthMonitor

__all__ = [
    # 接口
    'CurriculumLearningCallback',
    
    # 回调实现
    'CurriculumMonitorCallback',
    
    # 核心组件
    'DifficultyEstimator',
    'CurriculumScheduler', 
    'CurriculumAwareSampler',
    
    # 策略组件
    'DifficultyMetrics',
    'CurriculumProgressStrategies',
    'SampleSelectionStrategies',
    'CurriculumHealthMonitor',
    
    # 工厂函数
    'create_curriculum_learning_system'
]

__version__ = '1.0' 