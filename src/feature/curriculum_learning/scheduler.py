"""
课程调度器模块

负责课程学习的核心调度逻辑，包含进展控制和样本选择。

职责：
- 课程进展控制
- 样本选择策略
- 性能反馈处理
- 回调事件管理

Dependencies:
- .interfaces (本模块)
- .estimator (本模块)
- .progress_strategies (本模块)
- .selection_strategies (本模块)
- .strategies (本模块)
- .health_monitor (本模块)
- utils (本模块)
- config (本模块)
"""

import copy
import numpy as np
from collections import deque
from typing import Dict, Any, Optional, List, Tuple, Deque
from .interfaces import CurriculumLearningCallback
from .estimator import DifficultyEstimator
from .progress_strategies import CurriculumProgressStrategies
from .selection_strategies import SampleSelectionStrategies
from .strategies import PerformanceFeedbackHandler, LossFeedbackHandler
from .health_monitor import CurriculumHealthMonitor
from .recovery_config import (
    EmergencyRecoveryConfig, DEFAULT_RECOVERY_CONFIG,
    get_recovery_strategy, get_detection_thresholds
)
from utils import log_utils
from config import TrainConfig, DEFAULT_TRAIN_CONFIG


class PerformanceCrashDetector:
    """
    性能崩溃检测器 - 基于贝叶斯分析的崩溃预警和恢复
    
    核心机制：
    1. 多指标监控：识别率、损失震荡、梯度异常
    2. 崩溃预测：基于历史模式预测崩溃概率
    3. 热切换：在检测到崩溃时快速调整课程参数
    4. 自适应恢复：根据崩溃严重程度选择恢复策略
    """
    
    def __init__(self, window_size: int = 5, recovery_config: EmergencyRecoveryConfig = None):
        self.window_size = window_size
        self.performance_window = deque(maxlen=window_size)
        self.loss_window = deque(maxlen=window_size)
        self.crash_history = []
        self.last_crash_epoch = -1
        self.recovery_state = "normal"  # normal, warning, crashed, recovering

        # 使用配置化的恢复系统
        self.recovery_config = recovery_config or DEFAULT_RECOVERY_CONFIG
        self.crash_thresholds = get_detection_thresholds(self.recovery_config)

        # 恢复状态跟踪
        self.recovery_attempts = 0
        self.recovery_start_epoch = -1
        self.recovery_step = 0
        self.success_confirmation_count = 0
        
    def update_metrics(self, epoch: int, recognition_count: int, 
                      total_loss: float, performance_metrics: Dict[str, float]):
        """更新性能指标"""
        self.performance_window.append({
            'epoch': epoch,
            'recognition_count': recognition_count,
            'total_loss': total_loss,
            'metrics': performance_metrics.copy()
        })
        
        self.loss_window.append(total_loss)
        
    def detect_crash(self, current_epoch: int) -> Dict[str, Any]:
        """检测性能崩溃"""
        if len(self.performance_window) < 3:
            return {'crash_detected': False, 'crash_type': None}
            
        # 1. 零识别检测
        zero_recognition_crash = self._detect_zero_recognition_crash()
        
        # 2. 性能暴跌检测
        performance_drop_crash = self._detect_performance_drop_crash()
        
        # 3. 损失爆炸检测  
        loss_explosion_crash = self._detect_loss_explosion_crash()
        
        # 综合判断
        crash_types = []
        if zero_recognition_crash:
            crash_types.append('zero_recognition')
        if performance_drop_crash:
            crash_types.append('performance_drop')
        if loss_explosion_crash:
            crash_types.append('loss_explosion')
            
        if crash_types:
            crash_severity = self._calculate_crash_severity(crash_types)
            self.crash_history.append({
                'epoch': current_epoch,
                'types': crash_types,
                'severity': crash_severity
            })
            self.last_crash_epoch = current_epoch
            self.recovery_state = "crashed"
            
            return {
                'crash_detected': True,
                'crash_types': crash_types,
                'severity': crash_severity,
                'recovery_strategy': self._get_recovery_strategy(crash_severity)
            }
            
        return {'crash_detected': False, 'crash_type': None}
    
    def _detect_zero_recognition_crash(self) -> bool:
        """检测零识别崩溃 - 增强版：多维度检测"""
        if len(self.performance_window) < 2:
            return False

        recent_data = list(self.performance_window)[-3:]
        recent_recognitions = [p['recognition_count'] for p in recent_data]
        zero_count = sum(1 for r in recent_recognitions if r == 0)

        # 主要检测：连续零识别
        if zero_count >= self.crash_thresholds['zero_recognition_tolerance']:
            return True

        # 辅助检测：边界样本数量骤降
        if len(recent_data) >= 2:
            recent_boundary = [p.get('metrics', {}).get('boundary_samples', 0) for p in recent_data[-2:]]
            if all(b < self.crash_thresholds['boundary_sample_threshold'] for b in recent_boundary):
                return True

        # 辅助检测：特征质量严重下降
        if len(recent_data) >= 2:
            recent_quality = [p.get('metrics', {}).get('feature_quality_score', 1.0) for p in recent_data[-2:]]
            if all(q < self.crash_thresholds['feature_quality_threshold'] for q in recent_quality):
                return True

        return False
    
    def _detect_performance_drop_crash(self) -> bool:
        """检测性能暴跌崩溃"""
        if len(self.performance_window) < 4:
            return False
            
        recent_recognitions = [p['recognition_count'] for p in self.performance_window]
        
        # 计算最大值到最小值的下降比例
        max_recognition = max(recent_recognitions[:-1])  # 排除最新的，防止噪声
        min_recent = min(recent_recognitions[-2:])       # 最近2个值的最小值
        
        if max_recognition > 10:  # 只有在之前有良好表现时才检测暴跌
            drop_ratio = 1.0 - (min_recent / max_recognition)
            return drop_ratio > self.crash_thresholds['performance_drop_threshold']
            
        return False
    
    def _detect_loss_explosion_crash(self) -> bool:
        """检测损失爆炸崩溃"""
        if len(self.loss_window) < 3:
            return False
            
        recent_losses = list(self.loss_window)[-3:]
        baseline_loss = np.mean(list(self.loss_window)[:-2]) if len(self.loss_window) > 2 else recent_losses[0]
        
        # 检测损失是否突然增大
        for loss in recent_losses:
            if loss > baseline_loss * self.crash_thresholds['loss_explosion_threshold']:
                return True
                
        return False
    
    def _calculate_crash_severity(self, crash_types: List[str]) -> float:
        """计算崩溃严重程度"""
        severity_weights = {
            'zero_recognition': 0.8,
            'performance_drop': 0.6,
            'loss_explosion': 0.7
        }
        
        total_severity = sum(severity_weights.get(ct, 0.5) for ct in crash_types)
        return min(1.0, total_severity / len(crash_types))
    
    def _get_recovery_strategy(self, severity: float) -> Dict[str, Any]:
        """获取恢复策略 - 使用配置化系统"""
        return get_recovery_strategy(severity, self.recovery_config)
    
    def check_recovery_success(self, current_performance: int,
                              boundary_samples: int = 0,
                              feature_quality: float = 0.0) -> bool:
        """检查恢复是否成功 - 增强版：多维度评估"""
        if self.recovery_state != "recovering":
            return False

        # 主要指标：识别数量恢复
        performance_recovered = current_performance >= self.crash_thresholds['recovery_success_threshold']

        # 辅助指标：边界样本数量恢复
        boundary_recovered = boundary_samples >= self.crash_thresholds['boundary_sample_threshold']

        # 辅助指标：特征质量恢复
        quality_recovered = feature_quality >= self.crash_thresholds['feature_quality_threshold']

        # 综合判断：主要指标必须满足，辅助指标至少满足一个
        if performance_recovered and (boundary_recovered or quality_recovered):
            self.recovery_state = "normal"
            return True

        return False


class CurriculumScheduler:
    """课程学习调度器"""
    
    def __init__(self,
                 config: Optional[TrainConfig] = None,
                 callbacks: Optional[List[CurriculumLearningCallback]] = None,
                 recovery_config: Optional[EmergencyRecoveryConfig] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.callbacks = callbacks or []
        self.difficulty_estimator = DifficultyEstimator(self.config)
        self.health_monitor = CurriculumHealthMonitor()

        # 🚨 核心优化：集成性能崩溃检测器（使用配置化系统）
        self.crash_detector = PerformanceCrashDetector(
            window_size=5,
            recovery_config=recovery_config or DEFAULT_RECOVERY_CONFIG
        )
        log_utils.info("🚨 已启用增强型性能崩溃检测和热切换机制", tag="CURRICULUM_SCHEDULER")
        
        self.current_epoch = 0
        self.current_stage = 0
        self.current_easy_ratio = self.config.curriculum_initial_easy_ratio
        self.temperature = self.config.curriculum_temperature
        
        self.performance_history: Deque[Dict] = deque(maxlen=self.config.curriculum_feedback_window)
        self.loss_feedback_history: List[Dict] = []
        
        log_utils.info("CurriculumScheduler 初始化完成", tag="CURRICULUM_SCHEDULER")
    
    def manage_callbacks(self, action: str, callback: CurriculumLearningCallback):
        """管理回调函数"""
        if action == "add":
            self.callbacks.append(callback)
        elif action == "remove" and callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def should_update_curriculum(self, epoch: int) -> bool:
        """判断是否应该更新课程"""
        return (self.config.curriculum_enable_curriculum_learning and
                epoch >= self.config.curriculum_start_epoch and
                (epoch - self.config.curriculum_start_epoch) % self.config.curriculum_update_interval == 0)
    
    def update_curriculum(self, epoch: int, performance_metrics: Dict[str, float], 
                         recognition_count: int = 0, total_loss: float = 0.0):
        """更新课程状态 - 集成崩溃检测和热切换"""
        self.current_epoch = epoch
        self._trigger_callbacks("curriculum_start", epoch=epoch)
        
        # 🚨 崩溃检测和热切换机制
        self.crash_detector.update_metrics(
            epoch, recognition_count, total_loss, performance_metrics
        )
        
        crash_info = self.crash_detector.detect_crash(epoch)
        if crash_info['crash_detected']:
            log_utils.warning(f"🚨 检测到性能崩溃: {crash_info['crash_types']}, "
                            f"严重程度: {crash_info['severity']:.2f}", 
                            tag="CRASH_DETECTOR")
            
            # 执行热切换恢复
            self._execute_emergency_recovery(crash_info['recovery_strategy'])
            self.crash_detector.recovery_state = "recovering"
            
        # 检查恢复状态 - 增强版
        elif self.crash_detector.recovery_state == "recovering":
            boundary_samples = performance_metrics.get('boundary_samples', 0)
            feature_quality = performance_metrics.get('feature_quality_score', 0.0)

            if self.crash_detector.check_recovery_success(
                recognition_count, boundary_samples, feature_quality):
                log_utils.info(f"🎉 性能恢复成功 - 识别数: {recognition_count}, "
                             f"边界样本: {boundary_samples}, 特征质量: {feature_quality:.3f}",
                             tag="CRASH_DETECTOR")

                # 触发恢复成功回调
                self._trigger_callbacks("recovery_success",
                                       recognition_count=recognition_count,
                                       boundary_samples=boundary_samples,
                                       feature_quality=feature_quality)
        
        # 使用 health_monitor 管理性能历史和停滞检测
        self.performance_history.append(copy.deepcopy(performance_metrics))
        is_stagnating = self.health_monitor.check_stagnation(performance_metrics)
        
        # 如果检测到停滞，触发回调
        if is_stagnating:
            self._trigger_callbacks("stagnation_detected", 
                                   health_status=self.health_monitor.get_health_status())
        
        difficulty_stats = self.difficulty_estimator.get_difficulty_stats()
        self._trigger_callbacks("difficulty_update", difficulty_stats=difficulty_stats)
        
        if self.config.curriculum_enable_multi_stage: 
            self._check_stage_transition()
        self._update_curriculum_progress()
        if self.config.curriculum_enable_feedback: 
            self._apply_performance_feedback()
        
        self.temperature = max(0.1, self.temperature * self.config.curriculum_temperature_decay)
        self._trigger_callbacks("curriculum_progress", stage=self.current_stage, easy_ratio=self.current_easy_ratio)
    
    def select_samples_by_curriculum(self, sample_ids: List[int], 
                                   sample_difficulties: Optional[np.ndarray] = None) -> List[int]:
        """根据课程选择样本"""
        if sample_difficulties is None:
            sample_difficulties = self.difficulty_estimator.estimate_difficulty(sample_ids)
        
        selection_map = {
            "threshold": lambda ids, diff: SampleSelectionStrategies.threshold_selection(ids, diff, self.current_easy_ratio),
            "ranking": lambda ids, diff: SampleSelectionStrategies.ranking_selection(ids, diff, self.current_easy_ratio),
            "probabilistic": lambda ids, diff: SampleSelectionStrategies.probabilistic_selection(ids, diff, self.temperature),
        }
        return selection_map.get(self.config.curriculum_selection_method, selection_map["probabilistic"])(sample_ids, sample_difficulties)
    
    def get_sample_weights(self, sample_ids: List[int], 
                          sample_difficulties: Optional[np.ndarray] = None) -> np.ndarray:
        """获取样本权重"""
        if not self.config.curriculum_enable_reweighting: 
            return np.ones(len(sample_ids))
        
        difficulties = sample_difficulties if sample_difficulties is not None else self.difficulty_estimator.estimate_difficulty(sample_ids)
        
        weights = np.ones(len(sample_ids))
        threshold = 1.0 - self.current_easy_ratio
        weights[difficulties <= threshold] = self.config.curriculum_easy_sample_weight
        weights[difficulties > threshold] = self.config.curriculum_hard_sample_weight
        return weights
    
    def get_state_and_update_difficulties(self, sample_ids: Optional[List[int]] = None, **kwargs) -> Dict[str, Any]:
        """获取当前状态并可选地更新样本难度"""
        if sample_ids is not None:
            self.difficulty_estimator.update_sample_difficulty(sample_ids, **kwargs)
        
        return {
            'epoch': self.current_epoch, 'stage': self.current_stage,
            'easy_ratio': self.current_easy_ratio, 'temperature': self.temperature,
            'difficulty_stats': self.difficulty_estimator.get_difficulty_stats(),
            'health_status': self.health_monitor.get_health_status()
        }
    
    def _trigger_callbacks(self, event: str, *args, **kwargs):
        """触发回调事件"""
        for cb in self.callbacks:
            try:
                getattr(cb, f"on_{event}")(self, *args, **kwargs)
            except Exception as e:
                log_utils.error(f"课程学习回调 {event} 执行失败: {e}", tag="CURRICULUM_SCHEDULER")
    
    def _check_stage_transition(self):
        """检查阶段转换"""
        stage_epochs = self.config.curriculum_stage_epochs
        if self.current_stage < len(stage_epochs) - 1 and self.current_epoch >= stage_epochs[self.current_stage + 1]:
            old_stage = self.current_stage
            self.current_stage += 1
            self._trigger_callbacks("stage_transition", old_stage=old_stage, new_stage=self.current_stage)
    
    def _update_curriculum_progress(self):
        """更新课程进展"""
        strategy_map = {
            "linear": lambda: CurriculumProgressStrategies.linear_progress(self.current_epoch, self.config),
            "exponential": lambda: CurriculumProgressStrategies.exponential_progress(self.current_epoch, self.config),
            "step": lambda: CurriculumProgressStrategies.step_progress(self.current_epoch, self.current_stage, self.config),
            "adaptive": lambda: CurriculumProgressStrategies.adaptive_progress(self.current_easy_ratio, list(self.performance_history), self.config),
        }
        self.current_easy_ratio = strategy_map.get(self.config.curriculum_strategy, strategy_map["adaptive"])()
        self.current_easy_ratio = np.clip(self.current_easy_ratio, self.config.curriculum_final_easy_ratio, self.config.curriculum_initial_easy_ratio)

    def _apply_performance_feedback(self):
        """应用性能反馈"""
        self.current_easy_ratio = PerformanceFeedbackHandler.apply_performance_feedback(
            self.current_easy_ratio, list(self.performance_history), self.config
        )
        self._trigger_callbacks("performance_feedback", metrics=self.performance_history[-1], adjustment="applied")

    def receive_loss_feedback(self, loss_metrics: Dict[str, float], epoch: int):
        """接收损失反馈"""
        adjustments = {}
        
        # 慢收敛调整
        slow_conv_result = LossFeedbackHandler.adjust_for_slow_convergence(
            loss_metrics, epoch, self.current_stage, self.current_easy_ratio, self.config
        )
        if slow_conv_result:
            if slow_conv_result.get("stage_changed"):
                self.current_stage = slow_conv_result["new_stage"]
                self.current_easy_ratio = slow_conv_result["new_ratio"]
                self._trigger_callbacks("stage_transition", old_stage=self.current_stage-1, new_stage=self.current_stage)
            adjustments.update(slow_conv_result)
        
        # 损失震荡调整
        osc_result = LossFeedbackHandler.adjust_for_loss_oscillation(
            list(self.performance_history), self.current_stage, self.current_easy_ratio
        )
        if osc_result:
            if osc_result.get("stage_changed"):
                self.current_stage = osc_result["new_stage"]
                self.current_easy_ratio = osc_result["new_ratio"]
                self._trigger_callbacks("stage_transition", old_stage=self.current_stage+1, new_stage=self.current_stage)
            adjustments.update(osc_result)
        
        # 损失比例调整
        ratio_result = LossFeedbackHandler.adjust_for_loss_ratio(loss_metrics, self.current_easy_ratio)
        if ratio_result:
            if ratio_result.get("easy_ratio_changed"):
                self.current_easy_ratio = ratio_result["new_ratio"]
            adjustments.update(ratio_result)
        
        # 记录反馈历史
        self.loss_feedback_history.append({
            'epoch': epoch, 'loss_metrics': loss_metrics.copy(),
            'adjustments_made': adjustments
        })

    def get_loss_feedback_history(self) -> List[Dict]: 
        return self.loss_feedback_history
    
    def _execute_emergency_recovery(self, recovery_strategy: Dict[str, Any]):
        """执行紧急恢复策略 - 增强版：渐进式恢复"""
        strategy_type = recovery_strategy['type']

        log_utils.warning(f"🚨 执行紧急恢复策略: {strategy_type}", tag="EMERGENCY_RECOVERY")

        # 保存恢复前状态
        self._old_easy_ratio = self.current_easy_ratio
        self._old_stage = self.current_stage

        # 1. 渐进式调整课程难度
        if recovery_strategy.get('progressive_steps'):
            # 使用渐进式调整
            progressive_steps = recovery_strategy['progressive_steps']
            current_step = min(len(progressive_steps) - 1,
                             getattr(self, 'recovery_step', 0))
            easy_ratio_adjustment = progressive_steps[current_step]
            self.recovery_step = getattr(self, 'recovery_step', 0) + 1
        else:
            # 使用一次性调整
            easy_ratio_adjustment = recovery_strategy.get('easy_ratio_adjustment', 0.0)

        if easy_ratio_adjustment > 0:
            old_ratio = self.current_easy_ratio
            self.current_easy_ratio = min(0.95, self.current_easy_ratio + easy_ratio_adjustment)
            log_utils.info(f"📚 紧急调整简单样本比例: {old_ratio:.3f} → {self.current_easy_ratio:.3f}",
                         tag="EMERGENCY_RECOVERY")

        # 2. 调整挖掘器权重
        mining_weight_reduction = recovery_strategy.get('mining_weight_reduction', 1.0)
        if mining_weight_reduction < 1.0:
            log_utils.info(f"⚖️ 降低困难样本挖掘权重: 因子={mining_weight_reduction:.2f}",
                         tag="EMERGENCY_RECOVERY")

        # 3. 设置恢复期参数
        self.recovery_duration = recovery_strategy.get('duration', 3)
        self.recovery_start_epoch = self.current_epoch
        self.recovery_attempts = getattr(self, 'recovery_attempts', 0) + 1

        # 4. 启用特殊采样策略
        if recovery_strategy.get('special_sampling', False):
            log_utils.info("🎯 启用恢复期特殊采样策略", tag="EMERGENCY_RECOVERY")

        # 5. 触发回调通知其他组件
        self._trigger_callbacks("emergency_recovery",
                               strategy=recovery_strategy,
                               new_stage=self.current_stage,
                               new_easy_ratio=self.current_easy_ratio,
                               mining_weight_reduction=mining_weight_reduction,
                               recovery_attempt=self.recovery_attempts)
    
    # 向后兼容方法
    def get_current_curriculum_state(self) -> Dict[str, Any]:
        """获取当前课程状态 - 包含崩溃检测信息"""
        base_state = {
            'epoch': self.current_epoch, 
            'stage': self.current_stage,
            'easy_ratio': self.current_easy_ratio, 
            'temperature': self.temperature,
            'difficulty_stats': self.difficulty_estimator.get_difficulty_stats(),
            'health_status': self.health_monitor.get_health_status()
        }
        
        # 添加崩溃检测状态
        if hasattr(self, 'crash_detector'):
            base_state.update({
                'recovery_state': self.crash_detector.recovery_state,
                'last_crash_epoch': self.crash_detector.last_crash_epoch,
                'crash_history_count': len(self.crash_detector.crash_history)
            })
            
        return base_state
    
    def update_sample_difficulties(self, sample_ids: List[int], **kwargs):
        """更新样本难度（向后兼容）"""
        self.get_state_and_update_difficulties(sample_ids, **kwargs)
    
    def add_callback(self, cb: CurriculumLearningCallback):
        """添加回调（向后兼容）"""
        self.manage_callbacks("add", cb)
    
    def remove_callback(self, cb: CurriculumLearningCallback):
        """移除回调（向后兼容）"""
        self.manage_callbacks("remove", cb) 
