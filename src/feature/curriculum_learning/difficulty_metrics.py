"""
难度度量计算模块

专门处理各种难度度量的计算逻辑。

职责：
- 基于损失的难度计算
- 基于距离的难度计算
- 基于置信度的难度计算
- 混合度量计算

Dependencies:
- numpy
- torch
"""

import numpy as np
import torch
from typing import Dict, List, Optional


class DifficultyMetrics:
    """难度度量计算器"""
    
    @staticmethod
    def loss_based_difficulty(sample_ids: List[int], loss_history: Dict) -> np.ndarray:
        """基于损失的难度计算"""
        difficulties = []
        for sample_id in sample_ids:
            if sample_id in loss_history:
                loss_value = loss_history[sample_id]
                # 处理两种数据格式：集合或单个值
                if hasattr(loss_value, '__iter__') and not isinstance(loss_value, (str, bytes)):
                    avg_loss = np.mean(list(loss_value))
                else:
                    avg_loss = float(loss_value)
                difficulties.append(avg_loss)
            else:
                difficulties.append(0.5)  # 默认中等难度
        return np.array(difficulties)
    
    @staticmethod
    def distance_based_difficulty(sample_ids: List[int], 
                                distance_cache: Dict,
                                global_stats: Dict) -> np.ndarray:
        """基于距离的难度计算"""
        difficulties = []
        for sample_id in sample_ids:
            if sample_id in distance_cache:
                distance_value = distance_cache[sample_id]
                # 处理两种数据格式：集合或单个值
                if hasattr(distance_value, '__iter__') and not isinstance(distance_value, (str, bytes)):
                    distance = np.mean(list(distance_value))
                else:
                    distance = float(distance_value)
                
                # 归一化距离作为难度
                if global_stats.get('max_distance', 0) > 0:
                    normalized_distance = distance / global_stats['max_distance']
                    difficulties.append(1.0 - normalized_distance)  # 距离越远难度越低
                else:
                    difficulties.append(0.5)
            else:
                difficulties.append(0.5)
        return np.array(difficulties)
    
    @staticmethod
    def confidence_based_difficulty(sample_ids: List[int],
                                  confidence_scores: Dict) -> np.ndarray:
        """基于置信度的难度计算"""
        difficulties = []
        for sample_id in sample_ids:
            if sample_id in confidence_scores:
                confidence_value = confidence_scores[sample_id]
                # 处理两种数据格式：集合或单个值
                if hasattr(confidence_value, '__iter__') and not isinstance(confidence_value, (str, bytes)):
                    confidence = np.mean(list(confidence_value))
                else:
                    confidence = float(confidence_value)
                
                # 置信度越低难度越高
                difficulty = 1.0 - confidence
                difficulties.append(difficulty)
            else:
                difficulties.append(0.5)
        return np.array(difficulties)
    
    @staticmethod
    def mixed_difficulty(sample_ids: List[int],
                        loss_history: Dict,
                        distance_cache: Dict,
                        confidence_scores: Dict,
                        global_stats: Dict,
                        weights: Optional[Dict[str, float]] = None) -> np.ndarray:
        """混合难度计算"""
        if weights is None:
            weights = {'loss': 0.4, 'distance': 0.3, 'confidence': 0.3}
        
        loss_diff = DifficultyMetrics.loss_based_difficulty(sample_ids, loss_history)
        dist_diff = DifficultyMetrics.distance_based_difficulty(sample_ids, distance_cache, global_stats)
        conf_diff = DifficultyMetrics.confidence_based_difficulty(sample_ids, confidence_scores)
        
        # 加权平均
        mixed_diff = (weights['loss'] * loss_diff + 
                     weights['distance'] * dist_diff + 
                     weights['confidence'] * conf_diff)
        
        return mixed_diff 