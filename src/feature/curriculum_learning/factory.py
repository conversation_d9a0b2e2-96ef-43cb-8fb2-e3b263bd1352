"""
课程学习系统工厂模块

负责创建完整的课程学习系统，包含所有组件的组装。

职责：
- 系统组装
- 配置管理
- 组件集成

Dependencies:
- .scheduler (本模块)
- .sampler (本模块)
- .callbacks (本模块)
- src.config
"""

from typing import Optional, Tuple
from .scheduler import CurriculumScheduler
from .sampler import CurriculumAwareSampler
from .callbacks import CurriculumMonitorCallback
from config import TrainConfig, DEFAULT_TRAIN_CONFIG


def create_curriculum_learning_system(config: Optional[TrainConfig] = None,
                                    difficulty_cache_size: int = 1000,
                                    cache_update_frequency: int = 10,
                                    maintain_class_balance: bool = True,
                                    min_samples_per_class: int = 2,
                                    enable_monitoring: bool = True
                                    ) -> <PERSON><PERSON>[CurriculumScheduler, CurriculumAwareSampler, Optional[CurriculumMonitorCallback]]:
    """创建完整的课程学习系统"""
    callbacks = []
    monitor_callback = None
    if enable_monitoring:
        monitor_callback = CurriculumMonitorCallback()
        callbacks.append(monitor_callback)
    
    scheduler = CurriculumScheduler(config=config, callbacks=callbacks)
    sampler = CurriculumAwareSampler(
        scheduler=scheduler,
        difficulty_cache_size=difficulty_cache_size,
        cache_update_frequency=cache_update_frequency,
        maintain_class_balance=maintain_class_balance,
        min_samples_per_class=min_samples_per_class
    )
    
    return scheduler, sampler, monitor_callback 