"""
课程学习健康监控模块

专门处理课程学习系统的健康检查和停滞检测。

职责：
- 停滞检测
- 性能监控
- 健康状态评估

Dependencies:
- numpy
- collections
"""

import numpy as np
from collections import deque
from typing import Dict, List, Deque


class CurriculumHealthMonitor:
    """课程学习健康监控器"""
    
    def __init__(self, patience: int = 5, min_improvement: float = 0.001):
        self.patience = patience
        self.min_improvement = min_improvement
        self.performance_history: Deque = deque(maxlen=patience * 2)
        self.stagnation_counter = 0
        
    def check_stagnation(self, current_performance: Dict[str, float]) -> bool:
        """检查是否停滞"""
        self.performance_history.append(current_performance)
        
        if len(self.performance_history) < self.patience:
            return False
            
        recent_perfs = list(self.performance_history)[-self.patience:]
        
        # 检查主要指标是否改善
        main_metrics = ['val_acc', 'separation_ratio', 'feature_quality_score']
        improvements = []
        
        for metric in main_metrics:
            if metric in recent_perfs[0] and metric in recent_perfs[-1]:
                improvement = recent_perfs[-1][metric] - recent_perfs[0][metric]
                improvements.append(improvement)
        
        avg_improvement = np.mean(improvements) if improvements else 0.0
        
        if avg_improvement < self.min_improvement:
            self.stagnation_counter += 1
            return self.stagnation_counter >= self.patience
        else:
            self.stagnation_counter = 0
            return False
    
    def get_health_status(self) -> Dict[str, float]:
        """获取健康状态 - 集成统一健康度评估模块"""
        if len(self.performance_history) < 2:
            return {"health_score": 1.0, "stagnation_risk": 0.0}
        
        from utils.feature_space_health import comprehensive_health_evaluation
        
        # 获取最新性能数据
        latest_perf = self.performance_history[-1]
        
        # 使用统一健康度评估
        health_metrics = comprehensive_health_evaluation(
            inter_distance=latest_perf.get('avg_inter_dist'),
            intra_distance=latest_perf.get('avg_intra_dist'),
            fisher_score=latest_perf.get('fisher_score'),
            boundary_ratio=latest_perf.get('boundary_ratio')
        )
        
        # 计算本地趋势和停滞风险
        recent_trend = self._calculate_trend()
        stagnation_risk = self.stagnation_counter / self.patience
        
        # 综合健康度得分 (结合统一评估和本地监控)
        unified_score = health_metrics.overall_level.value[1] / 4.0  # 归一化到0-1
        local_score = max(0.0, 1.0 - stagnation_risk + recent_trend * 0.5)
        
        # 加权平均
        combined_health_score = unified_score * 0.7 + local_score * 0.3
        
        return {
            "health_score": combined_health_score,
            "unified_health_score": unified_score,
            "local_health_score": local_score,
            "stagnation_risk": stagnation_risk,
            "recent_trend": recent_trend,
            "unified_level": health_metrics.overall_level.value[0]
        }
    
    def _calculate_trend(self) -> float:
        """计算最近趋势"""
        if len(self.performance_history) < 3:
            return 0.0
            
        recent = list(self.performance_history)[-3:]
        trends = []
        
        for i in range(1, len(recent)):
            for metric in ['val_acc', 'separation_ratio']:
                if metric in recent[i] and metric in recent[i-1]:
                    trend = recent[i][metric] - recent[i-1][metric]
                    trends.append(trend)
        
        return np.mean(trends) if trends else 0.0 