"""
课程学习恢复配置模块

专门处理紧急恢复的配置参数和策略定义。

职责：
- 恢复策略配置
- 检测阈值配置
- 恢复期采样策略
- 监控指标配置

Dependencies:
- dataclasses
- typing
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional


@dataclass
class CrashDetectionConfig:
    """崩溃检测配置"""
    # 基础检测阈值
    zero_recognition_tolerance: int = 1  # 连续零识别容忍度（降低到1次）
    performance_drop_threshold: float = 0.5  # 性能下降阈值（更敏感）
    loss_explosion_threshold: float = 2.5  # 损失爆炸阈值（更早发现）
    
    # 新增检测指标
    boundary_sample_threshold: int = 8  # 边界样本数量阈值
    feature_quality_threshold: float = 0.6  # 特征质量阈值
    separation_ratio_threshold: float = 0.4  # 分离度阈值
    
    # 检测窗口配置
    detection_window_size: int = 5  # 检测窗口大小
    min_samples_for_detection: int = 2  # 最少样本数量
    
    # 复合检测权重
    crash_type_weights: Dict[str, float] = field(default_factory=lambda: {
        'zero_recognition': 0.8,
        'performance_drop': 0.6,
        'loss_explosion': 0.7,
        'boundary_collapse': 0.65,  # 新增：边界样本崩溃
        'feature_degradation': 0.55  # 新增：特征质量下降
    })


@dataclass
class RecoveryStrategyConfig:
    """恢复策略配置"""
    # 严重崩溃恢复策略
    aggressive_recovery: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'aggressive_recovery',
        'easy_ratio_adjustment': 0.22,
        'stage_rollback': 1,
        'learning_rate_reduction': 0.65,
        'mining_weight_reduction': 0.5,
        'duration': 4,
        'progressive_steps': [0.15, 0.20, 0.22],
        'special_sampling': True,
        'cool_down_epochs': 6
    })
    
    # 中等崩溃恢复策略
    moderate_recovery: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'moderate_recovery',
        'easy_ratio_adjustment': 0.18,
        'stage_rollback': 0,
        'learning_rate_reduction': 0.75,
        'mining_weight_reduction': 0.65,
        'duration': 3,
        'progressive_steps': [0.12, 0.15, 0.18],
        'special_sampling': True,
        'cool_down_epochs': 4
    })
    
    # 轻微崩溃恢复策略
    gentle_recovery: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'gentle_recovery',
        'easy_ratio_adjustment': 0.12,
        'stage_rollback': 0,
        'learning_rate_reduction': 0.85,
        'mining_weight_reduction': 0.8,
        'duration': 2,
        'progressive_steps': [0.08, 0.12],
        'special_sampling': False,
        'cool_down_epochs': 2
    })
    
    # 恢复成功判定标准
    recovery_success_criteria: Dict[str, Any] = field(default_factory=lambda: {
        'recognition_count_threshold': 15,
        'boundary_samples_threshold': 8,
        'feature_quality_threshold': 0.6,
        'patience_epochs': 2,  # 连续满足条件的轮次
        'require_all_metrics': False  # 是否需要所有指标都满足
    })





@dataclass
class RecoveryMonitoringConfig:
    """恢复期监控配置"""
    # 监控指标
    monitor_metrics: List[str] = field(default_factory=lambda: [
        'recognition_count',
        'boundary_samples',
        'feature_quality_score',
        'separation_ratio',
        'loss_stability',
        'gradient_norm'
    ])
    
    # 监控频率
    monitoring_interval: int = 1  # 每轮都监控
    detailed_logging: bool = True
    
    # 预警阈值
    warning_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'recognition_decline_rate': 0.3,
        'boundary_sample_decline_rate': 0.4,
        'feature_quality_decline_rate': 0.2,
        'loss_increase_rate': 0.5
    })
    
    # 恢复期退出条件
    exit_conditions: Dict[str, Any] = field(default_factory=lambda: {
        'max_recovery_duration': 8,  # 最大恢复期长度
        'max_recovery_attempts': 3,  # 最大恢复尝试次数
        'force_exit_threshold': 0.1, # 强制退出阈值
        'success_confirmation_epochs': 2  # 成功确认轮次
    })


@dataclass
class EmergencyRecoveryConfig:
    """紧急恢复总配置"""
    detection: CrashDetectionConfig = field(default_factory=CrashDetectionConfig)
    strategy: RecoveryStrategyConfig = field(default_factory=RecoveryStrategyConfig)
    monitoring: RecoveryMonitoringConfig = field(default_factory=RecoveryMonitoringConfig)
    
    # 全局开关
    enable_emergency_recovery: bool = True
    enable_progressive_recovery: bool = True
    enable_adaptive_thresholds: bool = True
    
    # 调试模式
    debug_mode: bool = False
    verbose_logging: bool = True


# 默认配置实例
DEFAULT_RECOVERY_CONFIG = EmergencyRecoveryConfig()


def get_recovery_strategy(severity: float, config: EmergencyRecoveryConfig = None) -> Dict[str, Any]:
    """根据严重程度获取恢复策略"""
    if config is None:
        config = DEFAULT_RECOVERY_CONFIG
    
    if severity > 0.8:
        return config.strategy.aggressive_recovery
    elif severity > 0.5:
        return config.strategy.moderate_recovery
    else:
        return config.strategy.gentle_recovery


def get_detection_thresholds(config: EmergencyRecoveryConfig = None) -> Dict[str, float]:
    """获取检测阈值"""
    if config is None:
        config = DEFAULT_RECOVERY_CONFIG
    
    return {
        'zero_recognition_tolerance': config.detection.zero_recognition_tolerance,
        'performance_drop_threshold': config.detection.performance_drop_threshold,
        'loss_explosion_threshold': config.detection.loss_explosion_threshold,
        'boundary_sample_threshold': config.detection.boundary_sample_threshold,
        'feature_quality_threshold': config.detection.feature_quality_threshold,
        'separation_ratio_threshold': config.detection.separation_ratio_threshold
    }



