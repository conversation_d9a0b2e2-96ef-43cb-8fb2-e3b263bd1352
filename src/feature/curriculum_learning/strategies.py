"""
反馈处理策略模块

专门处理性能反馈和损失反馈策略。

职责：
- 性能反馈处理
- 损失反馈处理
- 健康检查机制

Dependencies:
- numpy
- src.utils.log_utils
"""

import numpy as np
from typing import Dict, List
from utils import log_utils


class PerformanceFeedbackHandler:
    """性能反馈处理器"""
    
    @staticmethod
    def apply_performance_feedback(current_easy_ratio: float, performance_history: List[Dict], config) -> float:
        """应用性能反馈"""
        if len(performance_history) < config.feedback_window: 
            return current_easy_ratio

        performance_trend = PerformanceFeedbackHandler._calculate_performance_trend(list(performance_history))
        
        if performance_trend < -config.performance_decline_threshold:
            current_easy_ratio += config.adaptation_rate
        elif performance_trend > config.performance_threshold:
            current_easy_ratio -= config.adaptation_rate
        
        return np.clip(current_easy_ratio, config.final_easy_ratio, config.initial_easy_ratio)
    
    @staticmethod
    def _calculate_performance_trend(performances: List[Dict[str, float]]) -> float:
        """计算性能趋势"""
        main_metrics = ['val_acc', 'separation_ratio', 'feature_quality_score']
        trends = []
        for metric in main_metrics:
            metric_values = [p[metric] for p in performances if metric in p]
            if len(metric_values) >= 2:
                trends.append(np.polyfit(np.arange(len(metric_values)), metric_values, 1)[0])
        return np.mean(trends) if trends else 0.0


class LossFeedbackHandler:
    """损失反馈处理器"""
    
    @staticmethod
    def adjust_for_slow_convergence(metrics: Dict[str, float], epoch: int, current_stage: int, current_easy_ratio: float, config) -> Dict:
        """慢收敛调整"""
        rate = metrics.get('convergence_rate', 0.0)
        max_stages = getattr(config, 'max_stages', 4)
        if rate < 0.01 and epoch > 10 and current_stage < max_stages - 1:
            new_stage = current_stage + 1
            new_ratio = max(config.final_easy_ratio, current_easy_ratio - 0.05)
            log_utils.info(f"🚀 损失收敛缓慢，加速课程进展: Stage {current_stage}→{new_stage}, EasyRatio {current_easy_ratio:.2f}→{new_ratio:.2f}", tag="CURRICULUM_STRATEGY")
            return {"stage_changed": True, "reason": "slow_convergence", "new_stage": new_stage, "new_ratio": new_ratio}
        return {}

    @staticmethod
    def adjust_for_loss_oscillation(performance_history: List[Dict], current_stage: int, current_easy_ratio: float) -> Dict:
        """损失震荡调整"""
        if len(performance_history) >= 3:
            recent_losses = [p.get('total_loss', 0.0) for p in list(performance_history)[-3:]]
            if all(l > 0 for l in recent_losses) and np.var(recent_losses) > 0.1 and current_stage > 0:
                new_stage = current_stage - 1
                new_ratio = min(1.0, current_easy_ratio + 0.1)
                log_utils.warning(f"⚠️ 损失震荡严重，降低课程阶段: Stage {current_stage}→{new_stage}, EasyRatio {current_easy_ratio:.2f}→{new_ratio:.2f}", tag="CURRICULUM_STRATEGY")
                return {"stage_changed": True, "reason": "loss_oscillation", "new_stage": new_stage, "new_ratio": new_ratio}
        return {}
    
    @staticmethod
    def adjust_for_loss_ratio(metrics: Dict[str, float], current_easy_ratio: float) -> Dict:
        """损失比例调整"""
        circle_loss = metrics.get('circle_loss', 0.0)
        arcface_loss = metrics.get('arcface_loss', 0.0)
        if circle_loss <= 0 or arcface_loss <= 0: 
            return {}
        
        ratio = circle_loss / (arcface_loss + 1e-8)
        old_ratio = current_easy_ratio
        adjustment_made = False
        new_ratio = current_easy_ratio
        
        if ratio > 2.0 and current_easy_ratio < 0.8:
            new_ratio = min(0.8, current_easy_ratio + 0.05)
            log_utils.info(f"🎯 Circle损失过高，增加简单样本: {old_ratio:.3f} → {new_ratio:.3f}", tag="CURRICULUM_STRATEGY")
            adjustment_made = True
        elif ratio < 0.5 and current_easy_ratio > 0.2:
            new_ratio = max(0.2, current_easy_ratio - 0.05)
            log_utils.info(f"🎯 Center损失过高，减少简单样本: {old_ratio:.3f} → {new_ratio:.3f}", tag="CURRICULUM_STRATEGY")
            adjustment_made = True
        
        return {"easy_ratio_changed": adjustment_made, "reason": "loss_ratio_imbalance", "new_ratio": new_ratio} if adjustment_made else {} 