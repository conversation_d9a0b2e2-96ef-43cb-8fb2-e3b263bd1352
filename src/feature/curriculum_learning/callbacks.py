"""
课程学习回调实现模块

提供课程学习系统的具体回调实现。

职责：
- 监控课程学习进展
- 记录历史数据
- 日志输出管理

Dependencies:
- interfaces.py (本模块)
- src.utils.log_utils
- src.config
"""

import copy
from typing import Dict, List, Optional, Any
from .interfaces import CurriculumLearningCallback
from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class CurriculumMonitorCallback(CurriculumLearningCallback):
    """课程学习监控回调"""
    
    def __init__(self, log_config: Optional[TrainConfig] = None):
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        self.difficulty_history: List[Dict] = []
        self.progress_history: List[Dict] = []
        self.stage_transitions: List[Dict] = []
        self.feedback_history: List[Dict] = []
    
    def on_curriculum_start(self, scheduler: 'CurriculumScheduler', epoch: int):
        log_utils.info(f"课程学习开始: 第{epoch}轮开始课程学习", tag="CURRICULUM_CALLBACK")
    
    def on_difficulty_update(self, scheduler: 'CurriculumScheduler', 
                           difficulty_stats: Dict[str, float]):
        avg_difficulty = difficulty_stats.get('avg_difficulty', 0.0)
        log_utils.info(f"难度更新: 平均难度={avg_difficulty:.4f}", tag="CURRICULUM_CALLBACK")
        self.difficulty_history.append({
            'epoch': scheduler.current_epoch,
            'stats': copy.deepcopy(difficulty_stats)
        })
    
    def on_curriculum_progress(self, scheduler: 'CurriculumScheduler', 
                             stage: int, easy_ratio: float):
        log_utils.info(f"课程进展: 阶段={stage}, 简单样本比例={easy_ratio:.3f}", tag="CURRICULUM_CALLBACK")
        self.progress_history.append({
            'epoch': scheduler.current_epoch,
            'stage': stage,
            'easy_ratio': easy_ratio
        })
    
    def on_stage_transition(self, scheduler: 'CurriculumScheduler', 
                          old_stage: int, new_stage: int):
        log_utils.info(f"阶段转换: {old_stage} → {new_stage}", tag="CURRICULUM_CALLBACK")
        self.stage_transitions.append({
            'epoch': scheduler.current_epoch,
            'old_stage': old_stage,
            'new_stage': new_stage
        })
    
    def on_performance_feedback(self, scheduler: 'CurriculumScheduler', 
                              metrics: Dict[str, float], adjustment: str):
        main_metric_val = metrics.get('val_acc', metrics.get('separation_ratio', 0.0))
        log_utils.info(f"性能反馈: val_acc={main_metric_val:.4f}, 调整={adjustment}", tag="CURRICULUM_CALLBACK")
        self.feedback_history.append({
            'epoch': scheduler.current_epoch,
            'metrics': copy.deepcopy(metrics),
            'adjustment': adjustment
        })
    
    def on_emergency_recovery(self, scheduler: 'CurriculumScheduler', 
                            strategy: Dict[str, Any], new_stage: int, new_easy_ratio: float):
        """紧急恢复时的回调"""
        log_utils.warning(f"🚨 触发紧急恢复: 策略={strategy.get('type', 'unknown')}, "
                        f"新阶段={new_stage}, 新比例={new_easy_ratio:.3f}", 
                        tag="CURRICULUM_CALLBACK")
        
        # 记录紧急恢复事件
        recovery_record = {
            'epoch': scheduler.current_epoch,
            'strategy': strategy.copy(),
            'new_stage': new_stage,
            'new_easy_ratio': new_easy_ratio,
            'old_stage': getattr(scheduler, '_old_stage', new_stage),
            'old_easy_ratio': getattr(scheduler, '_old_easy_ratio', new_easy_ratio)
        }
        
        # 添加到阶段转换历史
        self.stage_transitions.append(recovery_record)
    
    def on_stagnation_detected(self, scheduler: 'CurriculumScheduler', 
                             health_status: Dict[str, float]):
        """停滞检测时的回调"""
        stagnation_record = {
            'epoch': scheduler.current_epoch,
            'health_status': health_status.copy(),
            'easy_ratio': scheduler.current_easy_ratio,
            'stage': scheduler.current_stage
        }
        
        # 记录到进展历史中
        self.progress_history.append(stagnation_record)
        
        log_utils.warning(f"检测到课程学习停滞: 健康分数={health_status.get('health_score', 0):.3f}, "
                        f"停滞风险={health_status.get('stagnation_risk', 0):.3f}", tag="CURRICULUM_CALLBACK")
    
    def get_difficulty_history(self) -> List[Dict]:
        return self.difficulty_history.copy()
    
    def get_progress_history(self) -> List[Dict]:
        return self.progress_history.copy()
    
    def get_stage_transitions(self) -> List[Dict]:
        return self.stage_transitions.copy()
    
    def get_feedback_history(self) -> List[Dict]:
        return self.feedback_history.copy() 