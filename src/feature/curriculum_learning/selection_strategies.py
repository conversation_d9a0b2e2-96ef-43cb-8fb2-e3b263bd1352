"""
样本选择策略模块

专门处理各种样本选择算法。

职责：
- 阈值选择策略
- 排序选择策略
- 概率选择策略

Dependencies:
- numpy
- random
- deterministic_seed_manager
"""

import random
import numpy as np
from typing import List
from utils.deterministic_seed_manager import get_seed_manager, create_meta_learning_context


class SampleSelectionStrategies:
    """样本选择策略集合"""
    
    @staticmethod
    def threshold_selection(sample_ids: List[int], difficulties: np.ndarray, easy_ratio: float, epoch: int = 0) -> List[int]:
        """阈值选择策略"""
        threshold = 1.0 - easy_ratio
        easy_indices = np.where(difficulties <= threshold)[0]
        hard_indices = np.where(difficulties > threshold)[0]
        
        num_easy = int(len(sample_ids) * easy_ratio)
        num_hard = len(sample_ids) - num_easy

        # 使用确定性种子管理器
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        
        selected_easy_indices = seed_manager.sample_without_replacement(
            len(easy_indices), min(num_easy, len(easy_indices)), context
        ) if len(easy_indices) > 0 else []
        
        selected_hard_indices = seed_manager.sample_without_replacement(
            len(hard_indices), min(num_hard, len(hard_indices)), context
        ) if len(hard_indices) > 0 else []

        # 补充不足的样本
        final_indices = list(selected_easy_indices) + list(selected_hard_indices)
        if len(final_indices) < len(sample_ids):
            remaining_indices = list(set(range(len(sample_ids))) - set(final_indices))
            needed = len(sample_ids) - len(final_indices)
            if remaining_indices:
                additional_indices = seed_manager.sample_without_replacement(
                    len(remaining_indices), needed, context
                )
                final_indices.extend(additional_indices)

        return [sample_ids[i] for i in final_indices]
    
    @staticmethod
    def ranking_selection(sample_ids: List[int], difficulties: np.ndarray, easy_ratio: float, epoch: int = 0) -> List[int]:
        """排序选择策略"""
        sorted_indices = np.argsort(difficulties)
        num_easy = int(len(sample_ids) * easy_ratio)
        
        selected_indices = list(sorted_indices[:num_easy])
        remaining_indices = list(sorted_indices[num_easy:])
        
        if (num_additional := len(sample_ids) - num_easy) > 0:
            if len(remaining_indices) >= num_additional:
                # 使用确定性种子管理器
                seed_manager = get_seed_manager()
                context = create_meta_learning_context(epoch=epoch)
                shuffled_remaining = seed_manager.shuffle_list(remaining_indices, context)
                selected_indices.extend(shuffled_remaining[:num_additional])
            else:
                selected_indices.extend(remaining_indices)
        
        return [sample_ids[i] for i in selected_indices]
    
    @staticmethod
    def probabilistic_selection(sample_ids: List[int], difficulties: np.ndarray, temperature: float, epoch: int = 0) -> List[int]:
        """概率选择策略"""
        easy_bias = 2.0 - difficulties
        probabilities = np.exp(easy_bias / temperature)
        probabilities /= probabilities.sum()
        
        # 使用确定性种子管理器设置numpy随机状态
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        
        from utils.deterministic_seed_manager import SeedScope
        with seed_manager.deterministic_context(context, SeedScope.TRIAL):
            selected_indices = np.random.choice(len(sample_ids), size=len(sample_ids), replace=False, p=probabilities)
        
        return [sample_ids[i] for i in selected_indices] 