"""
恢复期特殊采样器模块

专门处理紧急恢复期间的样本选择和权重调整。

职责：
- 恢复期样本选择
- 核心样本池管理
- 动态权重调整
- 采样策略切换

Dependencies:
- numpy
- typing
- .recovery_config
"""

import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
from .recovery_config import EmergencyRecoveryConfig, create_recovery_sampler_config
from utils import log_utils


class RecoverySampler:
    """恢复期特殊采样器"""
    
    def __init__(self, recovery_config: EmergencyRecoveryConfig):
        self.recovery_config = recovery_config
        self.essential_samples_pool: Dict[int, Dict] = {}  # 核心样本池
        self.sample_performance_history: Dict[int, List[float]] = defaultdict(list)
        self.current_strategy_type = None
        self.is_active = False
        
        # 采样统计
        self.sampling_stats = {
            'total_samples': 0,
            'easy_samples': 0,
            'essential_samples': 0,
            'new_samples': 0,
            'success_rate': 0.0
        }
        
    def activate_recovery_sampling(self, strategy_type: str, sample_pool: List[int]):
        """激活恢复期采样"""
        self.is_active = True
        self.current_strategy_type = strategy_type
        
        # 构建核心样本池
        self._build_essential_samples_pool(sample_pool)
        
        log_utils.info(f"🎯 激活恢复期采样: 策略={strategy_type}, "
                     f"核心样本池大小={len(self.essential_samples_pool)}", 
                     tag="RECOVERY_SAMPLER")
    
    def deactivate_recovery_sampling(self):
        """停用恢复期采样"""
        self.is_active = False
        self.current_strategy_type = None
        
        log_utils.info("🎯 停用恢复期采样，恢复正常采样模式", tag="RECOVERY_SAMPLER")
    
    def sample_for_recovery(self, 
                          available_samples: List[int], 
                          sample_difficulties: np.ndarray,
                          batch_size: int) -> Tuple[List[int], np.ndarray]:
        """恢复期采样"""
        if not self.is_active:
            return available_samples[:batch_size], np.ones(min(batch_size, len(available_samples)))
        
        # 获取当前策略配置
        sampler_config = create_recovery_sampler_config(
            self.current_strategy_type, self.recovery_config
        )
        
        # 分层采样
        selected_samples = []
        sample_weights = []
        
        # 1. 简单样本采样
        easy_count = int(batch_size * sampler_config['sample_distribution']['easy_samples'])
        easy_samples, easy_weights = self._sample_easy_samples(
            available_samples, sample_difficulties, easy_count
        )
        selected_samples.extend(easy_samples)
        sample_weights.extend(easy_weights)
        
        # 2. 核心样本采样
        essential_count = int(batch_size * sampler_config['sample_distribution']['essential_samples'])
        essential_samples, essential_weights = self._sample_essential_samples(essential_count)
        selected_samples.extend(essential_samples)
        sample_weights.extend(essential_weights)
        
        # 3. 新样本采样（填充剩余）
        remaining_count = batch_size - len(selected_samples)
        if remaining_count > 0:
            new_samples, new_weights = self._sample_new_samples(
                available_samples, selected_samples, remaining_count
            )
            selected_samples.extend(new_samples)
            sample_weights.extend(new_weights)
        
        # 更新统计信息
        self._update_sampling_stats(len(easy_samples), len(essential_samples), 
                                  len(selected_samples) - len(easy_samples) - len(essential_samples))
        
        return selected_samples[:batch_size], np.array(sample_weights[:batch_size])
    
    def _build_essential_samples_pool(self, sample_pool: List[int]):
        """构建核心样本池"""
        # 基于历史性能选择核心样本
        sample_scores = {}
        
        for sample_id in sample_pool:
            # 计算样本重要性得分
            history = self.sample_performance_history.get(sample_id, [])
            if history:
                # 高置信度 + 稳定性
                confidence = np.mean(history)
                stability = 1.0 / (np.std(history) + 1e-6)
                score = confidence * 0.7 + stability * 0.3
            else:
                # 新样本给予中等分数
                score = 0.5
            
            sample_scores[sample_id] = score
        
        # 选择top-k作为核心样本
        essential_size = self.recovery_config.sampling.essential_sample_size
        sorted_samples = sorted(sample_scores.items(), key=lambda x: x[1], reverse=True)
        
        self.essential_samples_pool = {}
        for sample_id, score in sorted_samples[:essential_size]:
            self.essential_samples_pool[sample_id] = {
                'score': score,
                'usage_count': 0,
                'last_used_epoch': -1
            }
    
    def _sample_easy_samples(self, 
                           available_samples: List[int], 
                           sample_difficulties: np.ndarray, 
                           count: int) -> Tuple[List[int], List[float]]:
        """采样简单样本"""
        if count <= 0:
            return [], []
        
        # 选择难度最低的样本
        difficulty_indices = np.argsort(sample_difficulties)
        easy_indices = difficulty_indices[:min(count * 2, len(difficulty_indices))]  # 扩大候选池
        
        # 随机选择以增加多样性
        selected_indices = np.random.choice(easy_indices, 
                                          size=min(count, len(easy_indices)), 
                                          replace=False)
        
        selected_samples = [available_samples[i] for i in selected_indices]
        
        # 简单样本权重稍高
        weights = [1.2] * len(selected_samples)
        
        return selected_samples, weights
    
    def _sample_essential_samples(self, count: int) -> Tuple[List[int], List[float]]:
        """采样核心样本"""
        if count <= 0 or not self.essential_samples_pool:
            return [], []
        
        # 基于使用频率和重要性选择
        available_essential = []
        for sample_id, info in self.essential_samples_pool.items():
            # 避免过度使用同一样本
            usage_penalty = info['usage_count'] * 0.1
            adjusted_score = info['score'] - usage_penalty
            available_essential.append((sample_id, adjusted_score))
        
        # 按调整后得分排序
        available_essential.sort(key=lambda x: x[1], reverse=True)
        
        # 选择top样本
        selected_count = min(count, len(available_essential))
        selected_samples = []
        weights = []
        
        for i in range(selected_count):
            sample_id, score = available_essential[i]
            selected_samples.append(sample_id)
            weights.append(1.5)  # 核心样本权重更高
            
            # 更新使用统计
            self.essential_samples_pool[sample_id]['usage_count'] += 1
        
        return selected_samples, weights
    
    def _sample_new_samples(self, 
                          available_samples: List[int], 
                          already_selected: List[int], 
                          count: int) -> Tuple[List[int], List[float]]:
        """采样新样本"""
        if count <= 0:
            return [], []
        
        # 排除已选择的样本
        remaining_samples = [s for s in available_samples if s not in already_selected]
        
        if not remaining_samples:
            return [], []
        
        # 随机选择
        selected_count = min(count, len(remaining_samples))
        selected_samples = np.random.choice(remaining_samples, 
                                          size=selected_count, 
                                          replace=False).tolist()
        
        # 新样本权重正常
        weights = [1.0] * len(selected_samples)
        
        return selected_samples, weights
    
    def _update_sampling_stats(self, easy_count: int, essential_count: int, new_count: int):
        """更新采样统计"""
        total = easy_count + essential_count + new_count
        
        self.sampling_stats.update({
            'total_samples': self.sampling_stats['total_samples'] + total,
            'easy_samples': self.sampling_stats['easy_samples'] + easy_count,
            'essential_samples': self.sampling_stats['essential_samples'] + essential_count,
            'new_samples': self.sampling_stats['new_samples'] + new_count
        })
        
        # 计算成功率（简化版）
        if self.sampling_stats['total_samples'] > 0:
            self.sampling_stats['success_rate'] = (
                self.sampling_stats['easy_samples'] + self.sampling_stats['essential_samples']
            ) / self.sampling_stats['total_samples']
    
    def update_sample_performance(self, sample_id: int, performance: float):
        """更新样本性能历史"""
        self.sample_performance_history[sample_id].append(performance)
        
        # 保持历史长度
        max_history = 10
        if len(self.sample_performance_history[sample_id]) > max_history:
            self.sample_performance_history[sample_id] = \
                self.sample_performance_history[sample_id][-max_history:]
    
    def get_sampling_stats(self) -> Dict[str, Any]:
        """获取采样统计信息"""
        return self.sampling_stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.sampling_stats = {
            'total_samples': 0,
            'easy_samples': 0,
            'essential_samples': 0,
            'new_samples': 0,
            'success_rate': 0.0
        }
