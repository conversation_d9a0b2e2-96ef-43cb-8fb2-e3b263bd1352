# 课程学习紧急恢复系统优化指南

## 🚨 系统概述

基于您提供的日志分析，我们已经成功实施了一套增强型紧急恢复系统，专门针对第35轮检测到的性能崩溃进行优化。

### 核心改进

1. **更敏感的崩溃检测**：零识别容忍度从2次降低到1次
2. **多维度检测指标**：新增边界样本和特征质量检测
3. **渐进式恢复策略**：避免过度调整，平滑恢复过程
4. **特殊采样机制**：恢复期使用核心样本池
5. **配置化管理**：支持热更新和预设配置

## 📊 当前日志分析

根据您的日志：
```
[WARNING] [CRASH_DETECTOR] 🚨 检测到性能崩溃: ['zero_recognition'], 严重程度: 0.80
[WARNING] [EMERGENCY_RECOVERY] 🚨 执行紧急恢复策略: moderate_recovery
[INFO] [EMERGENCY_RECOVERY] 📚 紧急调整简单样本比例: 0.700 → 0.900
```

系统正确识别并响应了崩溃，现在的优化将进一步提升恢复效果。

## 🛠 优化配置

### 1. 检测阈值优化

```python
# 新的检测配置
crash_thresholds = {
    'zero_recognition_tolerance': 1,    # 🎯 1次零识别即触发
    'performance_drop_threshold': 0.5,  # 🎯 50%性能下降触发
    'boundary_sample_threshold': 8,     # 🎯 边界样本<8触发
    'feature_quality_threshold': 0.6    # 🎯 特征质量<0.6触发
}
```

### 2. 恢复策略优化

```python
# 中等崩溃恢复策略（针对您的0.8严重程度）
moderate_recovery = {
    'easy_ratio_adjustment': 0.18,      # 🎯 更平滑的18%调整
    'mining_weight_reduction': 0.65,    # 🎯 大幅降低困难样本权重
    'progressive_steps': [0.12, 0.15, 0.18],  # 🎯 渐进式调整
    'special_sampling': True,           # 🎯 启用特殊采样
    'duration': 3                       # 🎯 持续3轮
}
```

## 🚀 使用方法

### 1. 基本集成

```python
from src.feature.curriculum_learning.scheduler import CurriculumScheduler
from src.feature.curriculum_learning.recovery_config import DEFAULT_RECOVERY_CONFIG

# 创建增强型调度器
scheduler = CurriculumScheduler(
    config=train_config,
    recovery_config=DEFAULT_RECOVERY_CONFIG  # 使用优化配置
)

# 在训练循环中使用
for epoch in range(num_epochs):
    # ... 训练代码 ...
    
    # 更新课程（包含崩溃检测）
    scheduler.update_curriculum(
        epoch=epoch,
        performance_metrics={
            'val_acc': val_accuracy,
            'boundary_samples': boundary_count,
            'feature_quality_score': feature_quality
        },
        recognition_count=target_recognition_count,
        total_loss=total_loss
    )
```

### 2. 热配置更新

```bash
# 立即生效的配置调整
python -m src.feature.curriculum_learning.config_updater \
    --key detection.zero_recognition_tolerance --value 1

# 应用激进恢复预设
python -m src.feature.curriculum_learning.config_updater \
    --preset aggressive

# 备份当前配置
python -m src.feature.curriculum_learning.config_updater --backup
```

### 3. 恢复期特殊采样

```python
from src.feature.curriculum_learning.recovery_sampler import RecoverySampler

# 创建恢复采样器
recovery_sampler = RecoverySampler(recovery_config)

# 在检测到崩溃时激活
if crash_detected:
    recovery_sampler.activate_recovery_sampling('moderate_recovery', sample_pool)

# 恢复期采样
selected_samples, weights = recovery_sampler.sample_for_recovery(
    available_samples, sample_difficulties, batch_size
)
```

## 📈 预期效果

### 1. 更早介入
- **原系统**：2-3次零识别后触发
- **优化后**：1次零识别即触发，减少损失

### 2. 更精准恢复
- **原系统**：固定20%调整
- **优化后**：渐进式12%→15%→18%调整

### 3. 更稳定采样
- **原系统**：随机采样
- **优化后**：70%简单+20%核心+10%新样本

## 🔧 故障排除

### 常见问题

1. **恢复触发过于频繁**
   ```bash
   # 提高检测阈值
   python config_updater.py --key detection.zero_recognition_tolerance --value 2
   ```

2. **恢复效果不明显**
   ```bash
   # 使用激进策略
   python config_updater.py --preset aggressive
   ```

3. **恢复后性能不稳定**
   ```bash
   # 延长恢复期
   python config_updater.py --key strategy.moderate_recovery.duration --value 5
   ```

## 📊 监控指标

### 关键监控点

```python
# 获取恢复状态
state = scheduler.get_current_curriculum_state()
recovery_info = {
    'recovery_state': state.get('recovery_state'),
    'crash_history_count': state.get('crash_history_count'),
    'last_crash_epoch': state.get('last_crash_epoch')
}

# 采样统计
if recovery_sampler.is_active:
    sampling_stats = recovery_sampler.get_sampling_stats()
    print(f"恢复期采样统计: {sampling_stats}")
```

### 日志监控

```bash
# 关键日志标签
grep "CRASH_DETECTOR" training.log
grep "EMERGENCY_RECOVERY" training.log
grep "RECOVERY_SAMPLER" training.log
```

## 🧪 测试验证

```python
# 运行完整测试套件
from src.feature.curriculum_learning.recovery_test import RecoverySystemTester

tester = RecoverySystemTester()
tester.run_all_tests()  # 验证所有功能正常
```

## 📋 配置参考

### 预设配置

| 预设 | 检测敏感度 | 恢复强度 | 适用场景 |
|------|------------|----------|----------|
| conservative | 低 | 温和 | 稳定训练环境 |
| balanced | 中 | 适中 | 一般训练场景 |
| aggressive | 高 | 强力 | 不稳定/崩溃频繁 |

### 自定义配置

```python
custom_config = EmergencyRecoveryConfig(
    detection=CrashDetectionConfig(
        zero_recognition_tolerance=1,
        performance_drop_threshold=0.4
    ),
    strategy=RecoveryStrategyConfig(
        moderate_recovery={
            'easy_ratio_adjustment': 0.20,
            'mining_weight_reduction': 0.6,
            'duration': 4
        }
    )
)
```

## 🎯 针对您当前情况的建议

基于日志显示的0.8严重程度崩溃：

1. **立即应用**：
   ```bash
   python config_updater.py --preset balanced
   ```

2. **监控指标**：重点关注`boundary_samples`和`feature_quality_score`

3. **调整策略**：如果恢复效果不佳，可切换到`aggressive`预设

4. **长期优化**：收集更多崩溃模式数据，定制专属恢复策略

---

💡 **提示**：所有配置更改都会自动备份，可随时回滚到之前的工作状态。
