"""
样本难度估计器模块

负责评估样本的学习难度，支持多种评估策略。

职责：
- 多种难度评估策略（基于损失、距离、置信度）
- 样本历史记录管理
- 全局统计信息维护

Dependencies:
- numpy
- torch
- collections
- src.config
"""

import numpy as np
import torch
from collections import defaultdict, deque
from typing import Dict, List, Optional, Deque
from .difficulty_metrics import DifficultyMetrics
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class DifficultyEstimator:
    """样本难度估计器"""
    
    def __init__(self, config: Optional[TrainConfig] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.metric = self.config.difficulty_metric
        self.window_size = self.config.difficulty_window
        self.smoothing = self.config.difficulty_smoothing
        
        self.loss_history: Dict[int, Deque] = defaultdict(lambda: deque(maxlen=self.window_size))
        self.distance_history: Dict[int, Deque] = defaultdict(lambda: deque(maxlen=self.window_size))
        self.confidence_history: Dict[int, Deque] = defaultdict(lambda: deque(maxlen=self.window_size))
        
        self.global_stats = {
            'loss_mean': 0.0, 'loss_std': 1.0,
            'distance_mean': 0.0, 'distance_std': 1.0,
            'confidence_mean': 0.5, 'confidence_std': 0.2
        }

    def update_sample_difficulty(self, sample_ids: List[int], **kwargs):
        """更新样本难度信息"""
        data_map = {
            'losses': self.loss_history,
            'distances': self.distance_history,
            'confidences': self.confidence_history
        }
        for name, history_dict in data_map.items():
            if (values := kwargs.get(name)) is not None:
                # 处理不同类型的输入数据
                if hasattr(values, 'detach'):  # PyTorch tensor
                    np_values = values.detach().cpu().numpy()
                elif isinstance(values, (list, tuple)):  # Python list/tuple
                    np_values = np.array(values)
                else:  # NumPy array
                    np_values = np.asarray(values)
                
                for i, sample_id in enumerate(sample_ids):
                    if i < len(np_values):
                        history_dict[sample_id].append(np_values[i])
    
    def estimate_difficulty(self, sample_ids: List[int]) -> np.ndarray:
        """估计样本难度"""
        # 使用 DifficultyMetrics 进行批量计算，更高效
        if self.metric == "loss_based":
            return DifficultyMetrics.loss_based_difficulty(sample_ids, self._get_average_history(self.loss_history))
        elif self.metric == "distance_based":
            return DifficultyMetrics.distance_based_difficulty(sample_ids, self._get_average_history(self.distance_history), self.global_stats)
        elif self.metric == "confidence_based":
            return DifficultyMetrics.confidence_based_difficulty(sample_ids, self._get_average_history(self.confidence_history))
        else:  # combined
            return DifficultyMetrics.mixed_difficulty(
                sample_ids,
                self._get_average_history(self.loss_history),
                self._get_average_history(self.distance_history),
                self._get_average_history(self.confidence_history),
                self.global_stats
            )
    
    def _get_average_history(self, history: Dict[int, Deque]) -> Dict[int, float]:
        """获取历史数据的平均值，用于与 DifficultyMetrics 接口兼容"""
        avg_history = {}
        for sample_id, values in history.items():
            if values:
                if len(list(values)) == 1:
                    avg_history[sample_id] = list(values)[0]
                else:
                    # 使用平滑权重计算加权平均
                    values_list = list(values)
                    weights = np.array([self.smoothing ** (len(values_list) - 1 - i) for i in range(len(values_list))])
                    avg_history[sample_id] = np.average(values_list, weights=weights / weights.sum())
        return avg_history
    
    def update_global_stats(self, **kwargs):
        """更新全局统计信息"""
        stats_map = {
            'all_losses': ('loss_mean', 'loss_std'),
            'all_distances': ('distance_mean', 'distance_std'),
            'all_confidences': ('confidence_mean', 'confidence_std')
        }
        for key, (mean_key, std_key) in stats_map.items():
            if (values := kwargs.get(key)) is not None:
                self.global_stats[mean_key] = np.mean(values)
                self.global_stats[std_key] = np.std(values) + 1e-8
    
    def get_difficulty_stats(self) -> Dict[str, float]:
        """获取难度统计信息"""
        all_difficulties = self._collect_all_difficulties()
        if not all_difficulties:
            return {
                'avg_difficulty': 0.5, 'std_difficulty': 0.0, 
                'min_difficulty': 0.0, 'max_difficulty': 1.0, 
                'num_samples': 0
            }
        
        np_difficulties = np.array(all_difficulties)
        return {
            'avg_difficulty': np.mean(np_difficulties), 
            'std_difficulty': np.std(np_difficulties),
            'min_difficulty': np.min(np_difficulties), 
            'max_difficulty': np.max(np_difficulties),
            'num_samples': len(np_difficulties)
        }

    def _collect_all_difficulties(self) -> List[float]:
        """收集所有样本的难度"""
        all_sample_ids = set(self.loss_history.keys()) | set(self.distance_history.keys()) | set(self.confidence_history.keys())
        if not all_sample_ids:
            return []
        # 批量计算所有样本的难度，更高效
        return self.estimate_difficulty(list(all_sample_ids)).tolist() 