import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from utils import log_utils
from utils.feature_norm import create_normalizer_from_config
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


# ==================== 回调机制 ====================

class FusionCallback(ABC):
    """多尺度融合回调接口"""
    
    @abstractmethod
    def on_fusion_start(self, fusion: 'OptimizedMultiScaleFusion', input_shape: Tuple[int, ...]):
        """融合开始时的回调"""
        pass
    
    @abstractmethod
    def on_branch_features_extracted(self, fusion: 'OptimizedMultiScaleFusion', 
                                   sem_feat: torch.Tensor, loc_feat: torch.Tensor, glb_feat: torch.Tensor):
        """分支特征提取完成时的回调"""
        pass
    
    @abstractmethod
    def on_attention_fusion_applied(self, fusion: 'OptimizedMultiScaleFusion', 
                                  enhanced_features: Tu<PERSON>[torch.Tensor, torch.Tensor, torch.Tensor]):
        """注意力融合应用后的回调"""
        pass
    
    @abstractmethod
    def on_adaptive_weights_computed(self, fusion: 'OptimizedMultiScaleFusion', weights: torch.Tensor):
        """自适应权重计算完成时的回调"""
        pass
    
    @abstractmethod
    def on_fusion_complete(self, fusion: 'OptimizedMultiScaleFusion', output: torch.Tensor):
        """融合完成时的回调"""
        pass


class FusionMonitorCallback(FusionCallback):
    """融合过程监控回调"""
    
    def __init__(self, log_level: str = None, enable_analysis: bool = None, 
                 config: TrainConfig = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.log_level = log_level if log_level is not None else "debug"
        self.enable_analysis = enable_analysis if enable_analysis is not None else True
        self.fusion_stats = {}
        self.current_epoch = -1
        self.epoch_logged = False
    
    def _should_log_debug(self) -> bool:
        """判断是否应该输出调试日志（每个epoch最多一次）"""
        if self.log_level != "debug" or self.epoch_logged:
            return False
        self.epoch_logged = True
        return True
    
    def on_fusion_start(self, fusion: 'OptimizedMultiScaleFusion', input_shape: Tuple[int, ...]):
        if self._should_log_debug():
            log_utils.debug(f"Epoch {self.current_epoch} - MultiScaleFusion - 开始融合，输入形状: {input_shape}", tag="MULTI_SCALE_FUSION")
    
    def _get_tensor_stats(self, tensor: torch.Tensor) -> Dict[str, float]:
        """获取张量的均值和标准差"""
        return {'mean': tensor.mean().item(), 'std': tensor.std().item()}

    def _store_branch_stats(self, sem_feat, loc_feat, glb_feat):
        """存储分支特征的统计数据"""
        self.fusion_stats['branch_stats'] = {
            'semantics': self._get_tensor_stats(sem_feat),
            'local': self._get_tensor_stats(loc_feat),
            'global': self._get_tensor_stats(glb_feat)
        }

    def on_branch_features_extracted(self, fusion: 'OptimizedMultiScaleFusion', sem_feat: torch.Tensor, loc_feat: torch.Tensor, glb_feat: torch.Tensor):
        if self.enable_analysis:
            self.fusion_stats['branch_shapes'] = {'semantics': sem_feat.shape, 'local': loc_feat.shape, 'global': glb_feat.shape}
            self._store_branch_stats(sem_feat, loc_feat, glb_feat)
        if self._should_log_debug():
            log_utils.debug(f"Epoch {self.current_epoch} - MultiScaleFusion - 分支特征提取完成", tag="MULTI_SCALE_FUSION")
    
    def on_attention_fusion_applied(self, fusion: 'OptimizedMultiScaleFusion', enhanced_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor]):
        if self._should_log_debug():
            log_utils.debug(f"Epoch {self.current_epoch} - MultiScaleFusion - 注意力融合应用完成", tag="MULTI_SCALE_FUSION")
    
    def on_adaptive_weights_computed(self, fusion: 'OptimizedMultiScaleFusion', weights: torch.Tensor):
        if self.enable_analysis:
            self.fusion_stats['adaptive_weights'] = {
                'semantics': weights[:, 0].mean().item(), 'local': weights[:, 1].mean().item(), 'global': weights[:, 2].mean().item()
            }
        if self._should_log_debug():
            log_utils.debug(f"Epoch {self.current_epoch} - MultiScaleFusion - 自适应权重计算完成", tag="MULTI_SCALE_FUSION")
    
    def on_fusion_complete(self, fusion: 'OptimizedMultiScaleFusion', output: torch.Tensor):
        if self.enable_analysis:
            self.fusion_stats['output_stats'] = {'shape': output.shape, **self._get_tensor_stats(output)}
        if self._should_log_debug():
            log_utils.debug(f"Epoch {self.current_epoch} - MultiScaleFusion - 融合完成，输出形状: {output.shape}", tag="MULTI_SCALE_FUSION")
    
    def reset_epoch_counter(self, epoch: int = None):
        """重置epoch计数器，同步主训练循环的epoch编号"""
        self.epoch_logged = False
        if epoch is not None:
            self.current_epoch = epoch
        else:
            self.current_epoch += 1
    
    def get_stats(self) -> Dict[str, Any]:
        return {**self.fusion_stats, 'current_epoch': self.current_epoch, 'epoch_logged': self.epoch_logged}


class FusionAnalysisCallback(FusionCallback):
    """融合分析回调"""
    def __init__(self):
        self.analysis_data = {}
    
    def on_fusion_start(self, fusion: 'OptimizedMultiScaleFusion', input_shape: Tuple[int, ...]):
        self.analysis_data['input_shape'] = input_shape
    
    def on_branch_features_extracted(self, fusion: 'OptimizedMultiScaleFusion', sem_feat: torch.Tensor, loc_feat: torch.Tensor, glb_feat: torch.Tensor):
        self.analysis_data['branch_features'] = {'semantics': sem_feat.detach(), 'local': loc_feat.detach(), 'global': glb_feat.detach()}
    
    def on_attention_fusion_applied(self, fusion: 'OptimizedMultiScaleFusion', enhanced_features: Tuple[torch.Tensor, torch.Tensor, torch.Tensor]):
        self.analysis_data['enhanced_features'] = enhanced_features
    
    def on_adaptive_weights_computed(self, fusion: 'OptimizedMultiScaleFusion', weights: torch.Tensor):
        self.analysis_data['adaptive_weights'] = weights.detach()
    
    def on_fusion_complete(self, fusion: 'OptimizedMultiScaleFusion', output: torch.Tensor):
        self.analysis_data['final_output'] = output.detach()
    
    def get_analysis_data(self) -> Dict[str, Any]:
        return self.analysis_data.copy()


# ==================== 原有组件类 ====================

class GlobalStatisticsPooling(nn.Module):
    """全局统计池化模块 - 提取高阶统计特征"""
    def __init__(self, in_features):
        super().__init__()
        self.in_features = in_features

    def forward(self, x):
        if x.dim() == 3:
            x = x.squeeze(-1)
        mean, std, max_val = x.mean(dim=1, keepdim=True), x.std(dim=1, keepdim=True), x.max(dim=1, keepdim=True)[0]
        stats = torch.cat([mean.expand_as(x), std.expand_as(x), max_val.expand_as(x)], dim=1)
        return stats.view(x.size(0), -1)


class CrossPathAttention(nn.Module):
    """跨路径注意力机制 - 建模路径间的动态交互"""
    def __init__(self, feature_dim, num_heads=None, config: TrainConfig = None):
        super().__init__()
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.num_heads = num_heads if num_heads is not None else self.config.attention_num_heads
        self.feature_dim = feature_dim
        self.head_dim = feature_dim // self.num_heads
        assert self.head_dim * self.num_heads == self.feature_dim, "feature_dim must be divisible by num_heads"

        self.q_proj, self.k_proj, self.v_proj = (nn.Linear(feature_dim, feature_dim) for _ in range(3))
        self.out_proj = nn.Linear(feature_dim, feature_dim)
        self.scale = math.sqrt(self.head_dim)

    def forward(self, sem_feat, loc_feat, glb_feat):
        features = torch.stack([sem_feat, loc_feat, glb_feat], dim=1)
        q, k, v = self._project_qkv(features)
        q, k, v = self._reshape_to_multi_head(q, k, v)
        attn_output = self._calculate_attention(q, k, v)
        output = self._reshape_and_project_output(attn_output, sem_feat.size(0))
        return output[:, 0, :], output[:, 1, :], output[:, 2, :]

    def _project_qkv(self, features):
        return self.q_proj(features), self.k_proj(features), self.v_proj(features)

    def _reshape_to_multi_head(self, q, k, v):
        batch_size = q.size(0)
        reshape = lambda t: t.view(batch_size, 3, self.num_heads, self.head_dim).transpose(1, 2)
        return reshape(q), reshape(k), reshape(v)

    def _calculate_attention(self, q, k, v):
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) / self.scale
        attn_weights = F.softmax(attn_weights, dim=-1)
        return torch.matmul(attn_weights, v)

    def _reshape_and_project_output(self, attn_output, batch_size):
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, 3, self.feature_dim)
        return self.out_proj(attn_output)


class GatedFusion(nn.Module):
    """门控融合层 - 使用门控机制控制信息流"""
    def __init__(self, input_dim, output_dim, dropout_rate=None, config: TrainConfig = None):
        super().__init__()
        self.config = config or DEFAULT_TRAIN_CONFIG
        dropout = dropout_rate if dropout_rate is not None else self.config.fusion_dropout_rate
        hidden_dim = input_dim // 2

        self.gate_net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim), nn.BatchNorm1d(hidden_dim), nn.ReLU(),
            nn.Dropout(dropout), nn.Linear(hidden_dim, input_dim), nn.Sigmoid()
        )
        self.transform_net = nn.Sequential(
            nn.Linear(input_dim, output_dim), nn.BatchNorm1d(output_dim),
            nn.Dropout(dropout), nn.ReLU()
        )

    def forward(self, x):
        gate = self.gate_net(x)
        return self.transform_net(x * gate)


class AdaptiveWeightLearning(nn.Module):
    """自适应权重学习 - 基于特征统计动态调整路径权重"""
    def __init__(self, feature_dim, config: TrainConfig = None):
        super().__init__()
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.feature_dim = feature_dim
        
        stats_dim = self.config.stats_dim
        hidden_dim = max(self.config.min_hidden_dim, feature_dim // self.config.hidden_dim_factor)
        self.weight_predictor = nn.Sequential(
            nn.Linear(stats_dim, hidden_dim), nn.BatchNorm1d(hidden_dim), nn.ReLU(),
            nn.Linear(hidden_dim, 3), nn.Softmax(dim=1)
        )

    def forward(self, sem_feat, loc_feat, glb_feat):
        combined_stats = self._calculate_combined_stats(sem_feat, loc_feat, glb_feat)
        return self.weight_predictor(combined_stats)

    def _get_stats(self, feat: torch.Tensor) -> torch.Tensor:
        return torch.cat([feat.mean(dim=1, keepdim=True), feat.std(dim=1, keepdim=True), feat.max(dim=1, keepdim=True)[0]], dim=1)

    def _calculate_combined_stats(self, sem_feat, loc_feat, glb_feat):
        return torch.cat([self._get_stats(f) for f in [sem_feat, loc_feat, glb_feat]], dim=1)


class OptimizedMultiScaleFusion(nn.Module):
    def __init__(self, in_features, branch_dim=None, dropout_rate=None, use_attention_fusion=None,
                 callbacks: Optional[List[FusionCallback]] = None, config: TrainConfig = None):
        super().__init__()
        self._initialize_config(in_features, branch_dim, dropout_rate, use_attention_fusion, callbacks, config)
        self._initialize_branches()
        self._initialize_fusion_layers()
        self._initialize_weight_learning()
        self._initialize_gating_mechanism()

    def _initialize_config(self, in_features, branch_dim, dropout_rate, use_attention_fusion, callbacks, config):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.in_features = in_features
        self.branch_dim = branch_dim if branch_dim is not None else int(in_features * self.config.branch_dim_ratio)
        self.total_branch_dim = self.branch_dim * 3
        self.callbacks = callbacks or []
        self.use_attention_fusion = use_attention_fusion if use_attention_fusion is not None else self.config.use_attention_fusion
        self.dropout_rate = dropout_rate if dropout_rate is not None else self.config.dropout_rate
        
        # 新增：固定分支增益配置（从配置中读取）
        self.local_gain = self.config.local_gain  # Local分支增益
        self.semantic_gain = self.config.semantic_gain  # Semantic分支增益
        self.global_gain = self.config.global_gain  # Global分支增益
        
        # 新增：门控机制配置（从配置中读取）
        self.enable_gating_mechanism = self.config.enable_gating_mechanism  # 是否启用门控机制
        self.gate_reduction_ratio = self.config.gate_reduction_ratio  # 门控网络降维比例

    def _initialize_branches(self):
        self._initialize_semantics_branch()
        self._initialize_local_branch()
        self._initialize_global_branch()

    def _initialize_semantics_branch(self):
        self.semantics_branch = nn.Sequential(
            nn.Linear(self.in_features, self.in_features), nn.BatchNorm1d(self.in_features), nn.GELU(),
            nn.Linear(self.in_features, self.in_features // 2), nn.BatchNorm1d(self.in_features // 2), nn.Dropout(self.dropout_rate), nn.GELU(),
            nn.Linear(self.in_features // 2, self.branch_dim), nn.BatchNorm1d(self.branch_dim), nn.Tanh()
        )

    def _initialize_local_branch(self):
        cfg, gf = self.config, self.config.local_groups_factor
        self.local_branch = nn.ModuleList([
            nn.Sequential(nn.Conv1d(self.in_features, self.in_features // gf, k, padding=k//2, groups=self.in_features//gf),
                          nn.BatchNorm1d(self.in_features // gf), nn.ReLU()) for k in cfg.local_kernel_sizes
        ])
        self.local_fusion = nn.Sequential(
            nn.Conv1d(self.in_features // gf * len(cfg.local_kernel_sizes), self.branch_dim, 1),
            nn.BatchNorm1d(self.branch_dim), nn.ReLU(), nn.Flatten()
        )

    def _initialize_global_branch(self):
        self.global_branch = nn.Sequential(
            GlobalStatisticsPooling(self.in_features),
            nn.Linear(self.in_features * 3, self.in_features), nn.BatchNorm1d(self.in_features), nn.ReLU(),
            nn.Dropout(self.dropout_rate), nn.Linear(self.in_features, self.branch_dim),
            nn.BatchNorm1d(self.branch_dim), nn.ReLU()
        )

    def _initialize_fusion_layers(self):
        if self.use_attention_fusion:
            self.attention_fusion = CrossPathAttention(self.branch_dim, self._get_valid_num_heads(), self.config)
        # 移除原有的GatedFusion，改为直接加权融合
        self.output_projection = nn.Sequential(
            nn.Linear(self.branch_dim, self.in_features),
            nn.BatchNorm1d(self.in_features),
            nn.Dropout(self.dropout_rate),
            nn.ReLU()
        )

    def _get_valid_num_heads(self) -> int:
        num_heads = self.config.attention_num_heads
        if self.config.attention_head_dim_auto:
            while self.branch_dim % num_heads != 0 and num_heads > 1:
                num_heads -= 1
        return num_heads

    def _initialize_weight_learning(self):
        self.adaptive_weights = AdaptiveWeightLearning(self.branch_dim, self.config)

    def _initialize_gating_mechanism(self):
        """初始化门控机制 - 用于控制分支增益"""
        if self.enable_gating_mechanism:
            gate_hidden_dim = self.branch_dim // self.gate_reduction_ratio
            self.branch_gates = nn.ModuleDict({
                'semantic': nn.Sequential(
                    nn.Linear(self.branch_dim, gate_hidden_dim),
                    nn.ReLU(),
                    nn.Linear(gate_hidden_dim, self.branch_dim),
                    nn.Sigmoid()
                ),
                'local': nn.Sequential(
                    nn.Linear(self.branch_dim, gate_hidden_dim),
                    nn.ReLU(),
                    nn.Linear(gate_hidden_dim, self.branch_dim),
                    nn.Sigmoid()
                ),
                'global': nn.Sequential(
                    nn.Linear(self.branch_dim, gate_hidden_dim),
                    nn.ReLU(),
                    nn.Linear(gate_hidden_dim, self.branch_dim),
                    nn.Sigmoid()
                )
            })
        else:
            self.branch_gates = None

    def _get_callback_map(self, callback: FusionCallback) -> Dict[str, callable]:
        return {"fusion_start": callback.on_fusion_start, "branch_features_extracted": callback.on_branch_features_extracted,
                "attention_fusion_applied": callback.on_attention_fusion_applied, "adaptive_weights_computed": callback.on_adaptive_weights_computed,
                "fusion_complete": callback.on_fusion_complete}

    def _trigger_callbacks(self, event_type: str, *args, **kwargs) -> None:
        for cb in self.callbacks:
            try:
                if method := self._get_callback_map(cb).get(event_type):
                    method(self, *args, **kwargs)
            except Exception as e:
                log_utils.error(f"回调执行失败 {cb.__class__.__name__}: {e}", tag="MULTI_SCALE_FUSION")

    def _extract_branch_features(self, x: torch.Tensor) -> Tuple[torch.Tensor, ...]:
        x_expanded = x.unsqueeze(-1)
        sem_feat = self.semantics_branch(x)
        local_outputs = [branch(x_expanded) for branch in self.local_branch]
        loc_feat = self.local_fusion(torch.cat(local_outputs, dim=1))
        glb_feat = self.global_branch(x_expanded)
        self._trigger_callbacks("branch_features_extracted", sem_feat, loc_feat, glb_feat)
        return sem_feat, loc_feat, glb_feat

    def _apply_attention_fusion(self, sem_feat, loc_feat, glb_feat) -> Tuple[torch.Tensor, ...]:
        if self.use_attention_fusion:
            sem_feat, loc_feat, glb_feat = self.attention_fusion(sem_feat, loc_feat, glb_feat)
            self._trigger_callbacks("attention_fusion_applied", (sem_feat, loc_feat, glb_feat))
        return sem_feat, loc_feat, glb_feat

    def _apply_gating_mechanism(self, sem_feat, loc_feat, glb_feat) -> Tuple[torch.Tensor, ...]:
        """应用门控机制控制分支增益"""
        if self.enable_gating_mechanism and self.branch_gates is not None:
            # 计算门控权重
            sem_gate = self.branch_gates['semantic'](sem_feat)
            loc_gate = self.branch_gates['local'](loc_feat) 
            glb_gate = self.branch_gates['global'](glb_feat)
            
            # 应用门控和固定增益
            sem_feat_gated = sem_feat * sem_gate * self.semantic_gain
            loc_feat_gated = loc_feat * loc_gate * self.local_gain  # 1.2x增益
            glb_feat_gated = glb_feat * glb_gate * self.global_gain
        else:
            # 直接应用固定增益，不使用门控
            sem_feat_gated = sem_feat * self.semantic_gain
            loc_feat_gated = loc_feat * self.local_gain  # 1.2x增益
            glb_feat_gated = glb_feat * self.global_gain
        
        return sem_feat_gated, loc_feat_gated, glb_feat_gated

    def _apply_adaptive_weights(self, sem_feat, loc_feat, glb_feat) -> Tuple[torch.Tensor, ...]:
        weights = self.adaptive_weights(sem_feat, loc_feat, glb_feat)
        self._trigger_callbacks("adaptive_weights_computed", weights)
        return sem_feat * weights[:, 0:1], loc_feat * weights[:, 1:2], glb_feat * weights[:, 2:3]

    def _weighted_fusion(self, sem_feat, loc_feat, glb_feat) -> torch.Tensor:
        """加权融合 - 实现 fused = 1.2 * local + 1.0 * global + 1.0 * semantic"""
        # 直接按照固定权重进行融合
        fused_feat = (self.semantic_gain * sem_feat + 
                     self.local_gain * loc_feat + 
                     self.global_gain * glb_feat)
        
        # 通过投影层映射回原始维度
        fused_feat = self.output_projection(fused_feat)
        
        return fused_feat

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        self._trigger_callbacks("fusion_start", input_shape=x.shape)
        
        # 1. 提取分支特征
        sem_feat, loc_feat, glb_feat = self._extract_branch_features(x)
        
        # 2. 应用注意力融合
        sem_feat, loc_feat, glb_feat = self._apply_attention_fusion(sem_feat, loc_feat, glb_feat)
        
        # 3. 应用门控机制（包含1.2x local增益）
        sem_feat, loc_feat, glb_feat = self._apply_gating_mechanism(sem_feat, loc_feat, glb_feat)
        
        # 4. 应用自适应权重
        sem_feat, loc_feat, glb_feat = self._apply_adaptive_weights(sem_feat, loc_feat, glb_feat)
        
        # 5. 加权融合
        fused_feat = self._weighted_fusion(sem_feat, loc_feat, glb_feat)
        
        # 6. 残差连接
        output = fused_feat + x

        self._trigger_callbacks("fusion_complete", output=output)
        return output

    def get_branch_gains(self) -> Dict[str, float]:
        """获取分支增益配置"""
        return {
            'semantic': self.semantic_gain,
            'local': self.local_gain,
            'global': self.global_gain
        }

    def set_branch_gains(self, semantic_gain: float = None, local_gain: float = None, global_gain: float = None):
        """动态设置分支增益"""
        if semantic_gain is not None:
            self.semantic_gain = semantic_gain
        if local_gain is not None:
            self.local_gain = local_gain
        if global_gain is not None:
            self.global_gain = global_gain