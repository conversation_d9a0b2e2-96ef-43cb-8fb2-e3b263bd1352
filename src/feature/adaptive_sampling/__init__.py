"""
自适应采样模块

提供基于类别难度的自适应批次采样功能，支持多种采样策略和回调机制。

主要组件：
- AdaptiveBatchSampler: 主采样器类
- SamplerCallback: 回调接口
- SamplingStrategy: 策略接口
- 工厂函数: create_adaptive_sampler

版本：v2.0
变更日志：2024-01-XX 从 adaptive_batch_sampler.py 重构拆分
"""

from .sampler import AdaptiveBatchSampler, create_adaptive_sampler
from .callbacks import SamplerCallback, SamplingMonitorCallback
from .strategies import (
    SamplingStrategy, 
    AdaptiveSamplingStrategy, 
    BalancedSamplingStrategy, 
    RandomSamplingStrategy
)
from .utils import DifficultyUpdater, IndexGenerator

__all__ = [
    # 主要类
    'AdaptiveBatchSampler',
    
    # 回调机制
    'SamplerCallback',
    'SamplingMonitorCallback',
    
    # 采样策略
    'SamplingStrategy',
    'AdaptiveSamplingStrategy',
    'BalancedSamplingStrategy', 
    'RandomSamplingStrategy',
    
    # 工具类
    'DifficultyUpdater',
    'IndexGenerator',
    
    # 工厂函数
    'create_adaptive_sampler'
]

__version__ = '2.0' 