"""
自适应采样回调机制模块

职责：
- 定义采样器回调接口
- 实现采样过程监控回调
- 提供回调统计信息收集

依赖：
- src.utils.log_utils
- src.config (回调配置)

变更日志：
2024-01-XX: 从 adaptive_batch_sampler.py 重构拆分
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class SamplerCallback(ABC):
    """采样器回调接口"""
    
    @abstractmethod
    def on_sampling_start(self, sampler, **kwargs):
        """采样开始时的回调"""
        pass
    
    @abstractmethod
    def on_difficulty_updated(self, sampler, difficulties: Dict[Any, float]):
        """类别难度更新时的回调"""
        pass
    
    @abstractmethod
    def on_class_allocation(self, sampler, allocation: Dict[Any, int]):
        """类别样本分配时的回调"""
        pass
    
    @abstractmethod
    def on_sampling_complete(self, sampler, indices: List[int]):
        """采样完成时的回调"""
        pass


class SamplingMonitorCallback(SamplerCallback):
    """采样过程监控回调"""
    
    def __init__(self, callback_config: Optional[TrainConfig] = None,
                 log_config: Optional[TrainConfig] = None):
        self.callback_config = callback_config or DEFAULT_TRAIN_CONFIG
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        self.sampling_stats = {}
    
    def on_sampling_start(self, sampler, **kwargs):
        """记录采样开始"""
        # 使用默认的debug级别日志
        log_utils.debug(f"AdaptiveBatchSampler - 开始采样，目标样本数: {sampler.length_before_new_iter}", tag="SAMPLING_CALLBACK")
    
    def on_difficulty_updated(self, sampler, difficulties: Dict[Any, float]):
        """记录难度更新"""
        # 默认收集难度统计
        self.sampling_stats['difficulties'] = difficulties.copy()
        
        # 使用默认的debug级别日志
        hard_classes = [k for k, v in difficulties.items() 
                      if v > sampler.config.difficulty_threshold]
        log_utils.debug(f"AdaptiveBatchSampler - 难度更新完成，困难类别数: {len(hard_classes)}", tag="SAMPLING_CALLBACK")
    
    def on_class_allocation(self, sampler, allocation: Dict[Any, int]):
        """记录类别分配"""
        # 默认收集分配统计
        self.sampling_stats['allocation'] = allocation.copy()
        
        # 使用默认的debug级别日志
        total_allocated = sum(allocation.values())
        log_utils.debug(f"AdaptiveBatchSampler - 类别分配完成，总分配样本数: {total_allocated}", tag="SAMPLING_CALLBACK")
    
    def on_sampling_complete(self, sampler, indices: List[int]):
        """记录采样完成"""
        # 默认收集采样统计
        self.sampling_stats['final_count'] = len(indices)
        
        # 使用默认的debug级别日志
        log_utils.debug(f"AdaptiveBatchSampler - 采样完成，实际样本数: {len(indices)}", tag="SAMPLING_CALLBACK")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取采样统计信息"""
        return self.sampling_stats.copy() 