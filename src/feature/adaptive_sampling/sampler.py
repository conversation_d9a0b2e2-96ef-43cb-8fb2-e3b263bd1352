"""
自适应批次采样器主模块

职责：
- 实现AdaptiveBatchSampler主类
- 提供采样器工厂函数
- 管理采样器配置和生命周期

依赖：
- torch.utils.data.Sampler
- .callbacks (回调机制)
- .strategies (采样策略)
- .utils (工具类)
- src.config (配置管理)
- src.utils.log_utils

变更日志：
2024-01-XX: 从 adaptive_batch_sampler.py 重构拆分
"""

import torch
from typing import Dict, List, Optional, Iterator, Any
from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig

from .callbacks import SamplerCallback, SamplingMonitorCallback
from .strategies import SamplingStrategy, AdaptiveSamplingStrategy, BalancedSamplingStrategy, RandomSamplingStrategy
from .utils import DifficultyUpdater, IndexGenerator


class AdaptiveBatchSampler(torch.utils.data.Sampler):
    """
    基于类别难度的自适应批次采样器
    
    功能：
    1. 回调机制：支持采样过程监控和扩展
    2. 策略模式：支持多种采样策略
    3. 配置化：通过AdaptiveSamplerConfig统一管理参数
    """
    
    def _init_config(self, config, initial_m, max_m, hard_class_ratio):
        """初始化采样器配置，处理向后兼容性"""
        if config is not None:
            return config
        
        if initial_m is not None or max_m is not None or hard_class_ratio is not None:
            log_utils.info("检测到已弃用的参数，将创建新的采样配置", tag="SAMPLER")
            # 创建一个新的配置，但使用默认配置作为基础
            new_config = DEFAULT_TRAIN_CONFIG
            if initial_m is not None:
                new_config.initial_m = initial_m
            if max_m is not None:
                new_config.max_m = max_m
            if hard_class_ratio is not None:
                new_config.hard_class_ratio = hard_class_ratio
            return new_config
        
        return DEFAULT_TRAIN_CONFIG

    def __init__(self, dataset, model=None, 
                 config: Optional[TrainConfig] = None,
                 callbacks: Optional[List[SamplerCallback]] = None,
                 length_before_new_iter: Optional[int] = None,
                 callback_config: Optional[TrainConfig] = None,
                 log_config: Optional[TrainConfig] = None,
                 # 向后兼容的参数
                 initial_m: Optional[int] = None,
                 max_m: Optional[int] = None,
                 hard_class_ratio: Optional[float] = None):
        """初始化批次采样器"""
        self.dataset = dataset
        self.model = model
        self.config = self._init_config(config, initial_m, max_m, hard_class_ratio)
        self.callbacks = callbacks or []
        self.length_before_new_iter = length_before_new_iter or len(dataset)
        self.callback_config = callback_config or DEFAULT_TRAIN_CONFIG
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        
        # 基础数据结构
        self.class_ids = dataset.class_ids
        self._build_class_mappings()
        
        # 策略选择
        self.strategy = self._create_strategy()
        
        # 初始化类别难度
        self.class_difficulties = {}
        self.update_difficulties()
    
    def _build_class_mappings(self):
        """构建类别映射"""
        self.labels_to_indices = {}
        for idx, label in enumerate(self.class_ids):
            if label not in self.labels_to_indices:
                self.labels_to_indices[label] = []
            self.labels_to_indices[label].append(idx)
        
        self.labels_to_count = {
            label: len(indices) 
            for label, indices in self.labels_to_indices.items()
        }
    
    def _create_strategy(self) -> SamplingStrategy:
        """创建采样策略"""
        strategy_map = {
            "adaptive": AdaptiveSamplingStrategy(),
            "balanced": BalancedSamplingStrategy(),
            "random": RandomSamplingStrategy()
        }
        return strategy_map.get(self.config.sampling_mode, AdaptiveSamplingStrategy())
    
    def add_callback(self, callback: SamplerCallback):
        """添加回调函数"""
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: SamplerCallback):
        """移除回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def set_epoch(self, epoch: int):
        """设置当前训练轮次，用于确定性随机采样"""
        self.current_epoch = epoch
        # 将epoch信息传递给策略
        if hasattr(self.strategy, 'set_epoch'):
            self.strategy.set_epoch(epoch)
        # 通知所有回调当前轮次
        for callback in self.callbacks:
            if hasattr(callback, 'set_epoch'):
                callback.set_epoch(epoch)
    
    def update_difficulties(self):
        """更新类别难度分数"""
        if self.model:
            self.class_difficulties = DifficultyUpdater.update_from_model(
                self.model, self.dataset, self.class_ids, self.config
            )
        else:
            self.class_difficulties = DifficultyUpdater.update_from_dataset(
                self.dataset, self.class_ids, self.config
            )
        
        # 触发回调
        for callback in self.callbacks:
            callback.on_difficulty_updated(self, self.class_difficulties)
    
    def __len__(self):
        """返回采样器每轮迭代生成的样本数量"""
        return self.length_before_new_iter
    
    def _prepare_iteration(self):
        """准备新一轮迭代：触发回调并更新难度"""
        for callback in self.callbacks:
            callback.on_sampling_start(self)
        self.update_difficulties()

    def _get_allocation(self) -> Optional[Dict[Any, int]]:
        """获取样本分配方案，无效则返回None"""
        valid_difficulties = {
            label: score for label, score in self.class_difficulties.items() 
            if label in self.labels_to_count
        }
        
        if not valid_difficulties:
            log_utils.warning("采样器警告: 没有找到有效的类别用于采样", tag="SAMPLER")
            return None
        
        allocation = self.strategy.allocate_samples(
            valid_difficulties, self.labels_to_count, self.config
        )
        
        for callback in self.callbacks:
            callback.on_class_allocation(self, allocation)
            
        return allocation

    def _generate_final_indices(self, allocation: Optional[Dict[Any, int]]) -> List[int]:
        """根据分配方案生成最终索引，无效则回退"""
        if not allocation or sum(allocation.values()) == 0:
            if allocation is not None:
                log_utils.warning("采样器警告: 没有找到有效的样本分配方案，回退到随机采样", tag="SAMPLER")
            return IndexGenerator.fallback_random_indices(
                len(self.dataset), self.length_before_new_iter
            )
        
        return IndexGenerator.generate_indices(
            self.labels_to_indices, allocation, self.length_before_new_iter, self.config
        )

    def __iter__(self) -> Iterator[int]:
        """生成采样索引序列"""
        self._prepare_iteration()
        
        allocation = self._get_allocation()
        indices = self._generate_final_indices(allocation)
        
        # 触发完成回调
        for callback in self.callbacks:
            callback.on_sampling_complete(self, indices)
        
        return iter(indices)
    
    def get_sampling_stats(self) -> Dict[str, Any]:
        """获取采样统计信息"""
        stats = {
            "config": {
                "initial_m": self.config.initial_m,
                "max_m": self.config.max_m,
                "hard_class_ratio": self.config.hard_class_ratio,
                "sampling_mode": self.config.sampling_mode,
                "difficulty_threshold": self.config.difficulty_threshold,
                "default_difficulty": self.config.default_difficulty
            },
            "class_count": len(self.labels_to_count),
            "total_samples": sum(self.labels_to_count.values()),
            "difficulties": self.class_difficulties.copy()
        }
        
        # 从监控回调中获取额外统计信息
        for callback in self.callbacks:
            if isinstance(callback, SamplingMonitorCallback):
                stats.update(callback.get_stats())
        
        return stats


def _create_sampler_config_from_kwargs(**kwargs) -> TrainConfig:
    """从关键字参数创建采样器配置"""
    # 创建默认配置的副本
    config = TrainConfig()
    
    # 更新支持的参数
    supported_params = [
        'initial_m', 'max_m', 'hard_class_ratio', 'sampling_mode',
        'difficulty_threshold'
    ]
    
    for key in supported_params:
        if key in kwargs and hasattr(config, key):
            setattr(config, key, kwargs[key])
    
    return config

def _create_monitoring_callback(config: TrainConfig) -> Optional[SamplingMonitorCallback]:
    """创建监控回调"""
    # 默认创建监控回调
    return SamplingMonitorCallback(callback_config=config)

def create_adaptive_sampler(dataset, model=None, **kwargs) -> AdaptiveBatchSampler:
    """创建自适应采样器的便捷函数"""
    config = _create_sampler_config_from_kwargs(**kwargs)
    
    callbacks = []
    monitor_callback = _create_monitoring_callback(config)
    if monitor_callback:
        callbacks.append(monitor_callback)
    
    return AdaptiveBatchSampler(
        dataset=dataset,
        model=model,
        config=config,
        callbacks=callbacks,
        length_before_new_iter=kwargs.get('length_before_new_iter')
    ) 