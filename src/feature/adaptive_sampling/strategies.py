"""
自适应采样策略模块

职责：
- 定义采样策略接口
- 实现自适应、平衡、随机三种采样策略
- 提供样本分配算法

依赖：
- random (标准库)

变更日志：
2024-01-XX: 从 adaptive_batch_sampler.py 重构拆分
"""

import random
from abc import ABC, abstractmethod
from typing import Dict, List, Any


class SamplingStrategy(ABC):
    """采样策略接口"""
    
    @abstractmethod
    def allocate_samples(self, class_difficulties: Dict[Any, float], 
                        class_counts: Dict[Any, int], 
                        config) -> Dict[Any, int]:
        """分配每个类别的采样数量"""
        pass


class AdaptiveSamplingStrategy(SamplingStrategy):
    """自适应采样策略"""

    def _get_class_split(self, class_difficulties: Dict[Any, float], config):
        """按难度将类别划分为困难和普通"""
        sorted_classes = sorted(
            class_difficulties.keys(), 
            key=lambda x: class_difficulties.get(x, getattr(config, 'default_difficulty', 1.0)),
            reverse=True
        )
        num_hard_classes = max(1, int(len(sorted_classes) * config.hard_class_ratio))
        return sorted_classes[:num_hard_classes], sorted_classes[num_hard_classes:]

    def _allocate_samples_for_classes(self, classes: List[Any], sample_count: int, 
                                      class_counts: Dict[Any, int]) -> Dict[Any, int]:
        """为一组类别分配指定数量的样本"""
        return {
            label: min(sample_count, class_counts[label])
            for label in classes
        }

    def allocate_samples(self, class_difficulties: Dict[Any, float], 
                         class_counts: Dict[Any, int], 
                         config) -> Dict[Any, int]:
        """根据类别难度自适应分配样本"""
        if not class_difficulties:
            return {}
        
        hard_classes, normal_classes = self._get_class_split(class_difficulties, config)
        
        allocation = self._allocate_samples_for_classes(hard_classes, config.max_m, class_counts)
        allocation.update(self._allocate_samples_for_classes(normal_classes, config.initial_m, class_counts))
        
        return allocation


class BalancedSamplingStrategy(SamplingStrategy):
    """平衡采样策略"""
    
    def allocate_samples(self, class_difficulties: Dict[Any, float], 
                        class_counts: Dict[Any, int], 
                        config) -> Dict[Any, int]:
        """为所有类别分配相同数量的样本"""
        if not class_counts:
            return {}
        
        return {
            label: min(config.initial_m, count)
            for label, count in class_counts.items()
        }


class RandomSamplingStrategy(SamplingStrategy):
    """随机采样策略"""
    
    def allocate_samples(self, class_difficulties: Dict[Any, float], 
                        class_counts: Dict[Any, int], 
                        config) -> Dict[Any, int]:
        """随机分配样本数量"""
        if not class_counts:
            return {}
        
        allocation = {}
        for label, count in class_counts.items():
            sample_count = random.randint(config.initial_m, min(config.max_m, count))
            allocation[label] = sample_count
        
        return allocation 