"""
自适应采样工具类模块

职责：
- 类别难度更新和计算
- 采样索引生成和分配
- 随机采样回退机制

依赖：
- random (标准库)
- src.config (采样配置)
- deterministic_seed_manager

变更日志：
2024-01-XX: 从 adaptive_batch_sampler.py 重构拆分
"""

import random
from typing import Dict, List, Optional, Any
from config import DEFAULT_TRAIN_CONFIG, TrainConfig
from utils.deterministic_seed_manager import get_seed_manager, create_meta_learning_context


class DifficultyUpdater:
    """类别难度更新器"""

    @staticmethod
    def _initialize_difficulties(class_ids: List[Any], config: TrainConfig) -> Dict[Any, float]:
        """用默认值初始化类别难度"""
        unique_labels = list(set(class_ids))
        return {label: config.default_difficulty for label in unique_labels}

    @staticmethod
    def _apply_scores_from_dataset(difficulties: Dict[Any, float], dataset):
        """如果可用，从数据集中应用难度分数"""
        if not hasattr(dataset, 'class_difficulty_scores') or not dataset.class_difficulty_scores:
            return

        for label, score in dataset.class_difficulty_scores.items():
            try:
                key_to_update = int(label)
            except (ValueError, TypeError):
                key_to_update = label
            
            if key_to_update in difficulties:
                difficulties[key_to_update] = score
    
    @staticmethod
    def update_from_dataset(dataset, class_ids: List[Any], 
                          config: Optional[TrainConfig] = None) -> Dict[Any, float]:
        """从数据集更新类别难度"""
        config = config or DEFAULT_TRAIN_CONFIG
        
        difficulties = DifficultyUpdater._initialize_difficulties(class_ids, config)
        DifficultyUpdater._apply_scores_from_dataset(difficulties, dataset)
        
        return difficulties
    
    @staticmethod
    def update_from_model(model, dataset, class_ids: List[Any],
                         config: Optional[TrainConfig] = None, 
                         miner_feedback: Optional[Dict] = None) -> Dict[Any, float]:
        """从模型预测结果更新类别难度，支持挖掘器反馈注入"""
        # 基础难度（从数据集获取）
        base_difficulties = DifficultyUpdater.update_from_dataset(dataset, class_ids, config)
        
        # 🔧 新增：挖掘器反馈增强
        if miner_feedback and isinstance(miner_feedback, dict):
            enhanced_difficulties = DifficultyUpdater._apply_miner_feedback(
                base_difficulties, miner_feedback, config or DEFAULT_TRAIN_CONFIG
            )
            return enhanced_difficulties
        
        # TODO: 实现基于模型预测的难度评估（预留扩展点）
        return base_difficulties
    
    @staticmethod
    def _apply_miner_feedback(base_difficulties: Dict[Any, float], 
                            miner_feedback: Dict, 
                            config: TrainConfig) -> Dict[Any, float]:
        """应用挖掘器反馈调整类别难度"""
        enhanced_difficulties = base_difficulties.copy()
        
        # 提取挖掘器信号
        boundary_ratio = miner_feedback.get('boundary_ratio', 0.0)
        hard_ratio = miner_feedback.get('hard_ratio', 0.0) 
        sample_separation = miner_feedback.get('sample_separation', {})
        
        # 全局难度调整因子
        global_adjustment = DifficultyUpdater._calculate_global_adjustment(
            boundary_ratio, hard_ratio, config
        )
        
        # 样本分离质量调整
        separation_adjustment = DifficultyUpdater._calculate_separation_adjustment(
            sample_separation, config
        )
        
        # 应用调整（简单的线性组合，保持稳定性）
        total_adjustment = (global_adjustment + separation_adjustment) / 2
        
        # 对所有类别应用调整，避免过度波动
        for class_id in enhanced_difficulties:
            # 温和调整：最大变化幅度限制在±20%
            adjustment_factor = 1.0 + max(-0.2, min(0.2, total_adjustment))
            enhanced_difficulties[class_id] *= adjustment_factor
        
        return enhanced_difficulties
    
    @staticmethod 
    def _calculate_global_adjustment(boundary_ratio: float, hard_ratio: float, 
                                   config: TrainConfig) -> float:
        """计算全局难度调整因子"""
        # 边界样本比例过低 -> 增加整体难度
        if boundary_ratio < 0.1:
            return 0.05  # 小幅增加难度
        # 边界样本比例过高 -> 降低整体难度  
        elif boundary_ratio > 0.3:
            return -0.05  # 小幅降低难度
        # 困难样本比例异常 -> 相应调整
        elif hard_ratio > 0.4:
            return 0.03  # 困难样本过多，小幅增加难度
        return 0.0  # 保持当前难度
    
    @staticmethod
    def _calculate_separation_adjustment(sample_separation, 
                                       config: TrainConfig) -> float:
        """基于样本分离质量计算调整因子"""
        # 🔧 修复：处理sample_separation可能是字典或浮点值的情况
        if isinstance(sample_separation, dict):
            # 原有逻辑：处理详细分离统计
            overlap_stats = sample_separation.get('overlap_statistics', {})
            overlap_rate = overlap_stats.get('overlap_rate', 0.0)
            
            # 样本重叠率高 -> 增加难度以提升采样质量
            if overlap_rate > 0.15:
                return 0.03
            # 样本分离良好 -> 轻微降低难度
            elif overlap_rate < 0.05:
                return -0.02
            return 0.0
        elif isinstance(sample_separation, (int, float)):
            # 🔧 新增：处理简化的分离度值 (0.0-1.0)
            separation_value = float(sample_separation)
            
            # 分离度低 -> 增加难度
            if separation_value < 0.3:
                return 0.03
            # 分离度高 -> 轻微降低难度
            elif separation_value > 0.7:
                return -0.02
            return 0.0
        else:
            # 未知格式，返回中性调整
            return 0.0


class IndexGenerator:
    """索引生成器"""
    
    @staticmethod
    def _calculate_iterations(total_samples: int, target_length: int, config: TrainConfig) -> int:
        """计算采样迭代次数 - 优化版本"""
        if total_samples == 0:
            return 0
        
        # 优化策略：优先无放回采样
        if total_samples >= target_length:
            return 1  # 一次采样即可
        
        # 必须重复时，最小化重复次数
        min_iterations = max(config.min_iterations, target_length // total_samples)
        
        # 🔧 新增：重复率控制
        if hasattr(config, 'max_duplicate_ratio') and config.max_duplicate_ratio < 1.0:
            # 计算最大允许的迭代次数
            max_iterations = int(1 / (1 - config.max_duplicate_ratio))
            return min(min_iterations, max_iterations)
        
        return min_iterations

    @staticmethod
    def _sample_for_class(label_indices: List[int], samples_needed: int, epoch: int = 0) -> List[int]:
        """为一个类别进行采样"""
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        
        if len(label_indices) < samples_needed:
            # 有放回采样
            return seed_manager.sample_with_replacement(len(label_indices), samples_needed, context).tolist()
        # 无放回采样
        sampled_indices = seed_manager.sample_without_replacement(len(label_indices), samples_needed, context)
        return [label_indices[i] for i in sampled_indices]

    @staticmethod
    def _sample_one_round(allocation: Dict[Any, int],
                          labels_to_indices: Dict[Any, List[int]], epoch: int = 0) -> List[int]:
        """执行一轮采样，为每个类别抽取样本"""
        round_indices = []
        class_order = list(allocation.keys())
        
        # 使用确定性种子管理器打乱类别顺序
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        class_order = seed_manager.shuffle_list(class_order, context)
        
        for label in class_order:
            if label in labels_to_indices:
                sampled = IndexGenerator._sample_for_class(
                    labels_to_indices[label], allocation[label], epoch
                )
                round_indices.extend(sampled)
        return round_indices

    @staticmethod
    def generate_indices(labels_to_indices: Dict[Any, List[int]], 
                        allocation: Dict[Any, int], 
                        target_length: int,
                        config: Optional[TrainConfig] = None,
                        epoch: int = 0) -> List[int]:
        """生成采样索引"""
        if not allocation:
            return []
        
        config = config or DEFAULT_TRAIN_CONFIG
        total_samples = sum(allocation.values())
        iterations = IndexGenerator._calculate_iterations(total_samples, target_length, config)
        
        if iterations == 0:
            return []
        
        indices = []
        for _ in range(iterations):
            indices.extend(IndexGenerator._sample_one_round(allocation, labels_to_indices, epoch))
        
        # 截断到目标长度并使用确定性打乱
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        indices = seed_manager.shuffle_list(indices, context)
        return indices[:target_length]
    
    @staticmethod
    def fallback_random_indices(dataset_size: int, target_length: int, epoch: int = 0) -> List[int]:
        """随机采样回退机制"""
        all_indices = list(range(dataset_size))
        
        # 使用确定性种子管理器打乱
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=epoch)
        all_indices = seed_manager.shuffle_list(all_indices, context)
        return all_indices[:target_length] 