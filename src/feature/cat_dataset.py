import os
import json
from PIL import Image
from torch.utils.data import Dataset
import torchvision.transforms as transforms
from utils import log_utils


# Dataset class
class CatDataset(Dataset):
    def __init__(self, json_path, transform=None, transform_difficult=None, difficulty_threshold=0.7, enable_pseudo_difficult_detection=False):
        self.samples = []
        self.transform = transform
        self.transform_difficult = transform_difficult
        self.difficulty_threshold = difficulty_threshold
        # self.bbox_map = {}  # 用于存储每张图片的 bbox 信息
        self.difficult_classes = set()  # 存储难分类别
        self.class_difficulty_scores = {}  # 存储类别难度分数

        # 伪难类别检测开关 - 默认关闭以避免误判
        self.enable_pseudo_difficult_detection = enable_pseudo_difficult_detection

        if not os.path.isfile(json_path):
            raise FileNotFoundError(f"No JSON file found at {json_path}")

        with open(json_path, 'r') as f:
            data = json.load(f)
            for item in data:
                class_id = item["class_id"]
                for image_info in item["images"]:
                    image_path = image_info["image_path"]
                    # bbox = image_info.get("bbox", None)
                    self.samples.append((image_path, class_id))
                    # if bbox:
                    #     self.bbox_map[image_path] = bbox  # 存储 bbox 坐标用于裁剪

        self.class_ids = [label for _, label in self.samples]
        unique_classes = sorted(set(self.class_ids))
        self.class_to_idx = {cls: idx for idx, cls in enumerate(unique_classes)}
        self.num_classes = len(unique_classes)

        # 初始化类别难度分数为1.0
        for cls in unique_classes:
            self.class_difficulty_scores[cls] = 1.0

    def update_difficult_classes(self, feature_analysis):
        """根据特征分析更新难分类别，增加伪难类别验证与过滤机制"""
        if not hasattr(feature_analysis, 'items'):
            return

        # 将特征分析字典转换为类别难度分数
        class_intra_dists = feature_analysis.get('intra_dists', {})
        class_inter_dists = feature_analysis.get('inter_dists', {})

        # 清空当前难分类别集合
        self.difficult_classes.clear()

        # 动态调整难度阈值
        difficulty_scaling_factor = feature_analysis.get('difficulty_scale', 1.0)
        adjusted_threshold = self.difficulty_threshold * difficulty_scaling_factor

        # 初始化跟踪器（如果不存在）
        self._initialize_history_trackers()

        # 计算类别难度分数和识别当前难分类别
        current_difficult_classes, difficulty_scores = self._calculate_difficulty_scores(
            class_intra_dists, class_inter_dists, adjusted_threshold)

        # 更新类别历史性能指标
        self._update_class_metrics(feature_analysis)

        # 根据开关决定是否进行伪难类别检测
        if self.enable_pseudo_difficult_detection:
            # 识别伪难类别
            new_pseudo_difficult = self._identify_pseudo_difficult_classes(
                current_difficult_classes, adjusted_threshold)

            # 更新伪难类别集合
            self.pseudo_difficult_classes.update(new_pseudo_difficult)

            # 反向验证机制
            confirmed_pseudo_difficult = self._validate_pseudo_difficult(adjusted_threshold)

            # 更新最终结果
            self.pseudo_difficult_classes = confirmed_pseudo_difficult
            final_difficult_classes = current_difficult_classes - self.pseudo_difficult_classes - self.semi_frozen_classes
            self.difficult_classes = final_difficult_classes
        else:
            # 禁用伪难类别检测时，直接使用当前难分类别
            self.difficult_classes = current_difficult_classes
            # 清空伪难类别和半冻结类别
            if hasattr(self, 'pseudo_difficult_classes'):
                self.pseudo_difficult_classes.clear()
            if hasattr(self, 'semi_frozen_classes'):
                self.semi_frozen_classes.clear()
        self.class_difficulty_scores = difficulty_scores

        # 安全地获取集合大小
        pseudo_count = len(getattr(self, 'pseudo_difficult_classes', set()))
        frozen_count = len(getattr(self, 'semi_frozen_classes', set()))

        if self.enable_pseudo_difficult_detection:
            log_utils.info(f"更新难分类别: 共{len(self.difficult_classes)}个类被标记为难分，{pseudo_count}个伪难类别，{frozen_count}个半冻结类别", tag="CAT_DATASET")
        else:
            log_utils.info(f"更新难分类别: 共{len(self.difficult_classes)}个类被标记为难分 (伪难类别检测已禁用)", tag="CAT_DATASET")

    def _initialize_history_trackers(self):
        """初始化历史跟踪器"""
        if not hasattr(self, 'class_history'):
            self.class_history = {}  # 记录类别历史表现
            self.pseudo_difficult_classes = set()  # 识别出的伪难类别
            self.semi_frozen_classes = set()  # 半冻结区类别

    def _calculate_difficulty_scores(self, intra_dists, inter_dists, threshold):
        """计算类别难度分数并识别当前难分类别"""
        current_difficult_classes = set()
        difficulty_scores = {}

        for cls, intra_dist in intra_dists.items():
            if cls not in inter_dists:
                continue

            inter_dist = inter_dists[cls]
            difficulty_score = intra_dist / (inter_dist + 1e-8)

            # 统一类型转换
            try:
                cls_id = int(cls) if isinstance(cls, str) else cls
            except (ValueError, TypeError):
                cls_id = cls

            difficulty_scores[cls_id] = difficulty_score

            # 使用阈值判断难分类别
            if difficulty_score > threshold:
                current_difficult_classes.add(cls_id)

            # 更新历史记录
            self._update_class_history(cls_id, difficulty_score, cls_id in current_difficult_classes)

        return current_difficult_classes, difficulty_scores

    def _update_class_metrics(self, feature_analysis):
        # 从特征分析中提取额外指标（如果有）
        if 'class_consistency' in feature_analysis:
            for cls, consistency in feature_analysis['class_consistency'].items():
                if cls in self.class_history:
                    self.class_history[cls]['consistency'] = consistency

        if 'class_entropy' in feature_analysis:
            for cls, entropy in feature_analysis['class_entropy'].items():
                if cls in self.class_history:
                    self.class_history[cls]['prediction_entropy'] = entropy

        if 'class_confidence' in feature_analysis:
            for cls, confidence in feature_analysis['class_confidence'].items():
                if cls in self.class_history:
                    self.class_history[cls]['avg_confidence'] = confidence

    def _identify_pseudo_difficult_classes(self, current_difficult_classes, adjusted_threshold):
        # 筛选伪难类别 - 使用更严格的条件避免误判
        # 只有在有充分证据时才标记为伪难类别
        self.pseudo_difficult_classes.clear()
        new_pseudo_difficult = set()

        for cls in current_difficult_classes:
            history = self.class_history[cls]

            # 需要更多历史记录才能做出判断，避免早期误判
            if len(history['difficulty_scores']) < 5:
                continue

            # 计算平均难度分数
            avg_difficulty = sum(history['difficulty_scores']) / len(history['difficulty_scores'])

            # 检查是否有实际的指标更新（非默认值）
            has_real_metrics = (
                history['consistency'] != 0.5 or
                history['prediction_entropy'] != 0.5 or
                history['avg_confidence'] != 0.5
            )

            # 如果没有实际指标更新，跳过伪难判断
            if not has_real_metrics:
                log_utils.debug(f"类别 {cls} 缺少实际指标更新，跳过伪难判断", tag="CAT_DATASET")
                continue

            # 伪难类别判断条件 - 更严格的阈值
            is_pseudo = False
            reason = ""

            # 条件1: 极高熵 + 极高难度分数 + 连续多轮
            if (history['prediction_entropy'] > 0.8 and
                avg_difficulty > adjusted_threshold * 1.5 and
                history['consecutive_difficult'] >= 5):
                is_pseudo = True
                reason = "极高预测熵且持续高难度"

            # 条件2: 极低置信度 + 连续多轮难分 + 高难度
            elif (history['avg_confidence'] < 0.3 and
                  history['consecutive_difficult'] >= 6 and
                  avg_difficulty > adjusted_threshold * 1.3):
                is_pseudo = True
                reason = "极低置信度且持续难分"

            # 条件3: 极低一致性 + 极高难度分数 + 连续多轮
            elif (history['consistency'] < 0.4 and
                  avg_difficulty > adjusted_threshold * 1.5 and
                  history['consecutive_difficult'] >= 5):
                is_pseudo = True
                reason = "极低一致性且持续高难度"

            if is_pseudo:
                new_pseudo_difficult.add(cls)
                log_utils.debug(f"类别 {cls} 被标记为伪难类别，原因: {reason}", tag="CAT_DATASET")

        return new_pseudo_difficult

    def _validate_pseudo_difficult(self, adjusted_threshold):
        # 反向验证机制 - 对之前标记为伪难的类别进行反向检验
        confirmed_pseudo_difficult = set()
        for cls in self.pseudo_difficult_classes:
            history = self.class_history[cls]

            # 如果类别持续表现为高难度，可能并非伪难而是真实难分类别
            is_genuinely_difficult = False

            # 持续高难度但预测熵已经降低，说明是真实难分类别
            if (history['consecutive_difficult'] >= 7 and
                history['difficulty_scores'][-1] > adjusted_threshold and
                history['prediction_entropy'] < 0.4):
                is_genuinely_difficult = True

            # 如果最近预测一致性提高，也可能是真实难分类别
            elif (history['consistency'] > 0.8 and
                  history['difficulty_scores'][-1] > adjusted_threshold * 0.9):
                is_genuinely_difficult = True

            if is_genuinely_difficult:
                # 将类别从伪难名单中移除
                log_utils.debug(f"类别 {cls} 通过反向验证，从伪难名单中移除", tag="CAT_DATASET")
            else:
                # 保留在伪难名单中
                confirmed_pseudo_difficult.add(cls)

                # 提高半冻结区门槛，避免过早冻结
                if cls in self.pseudo_difficult_classes and history['consecutive_difficult'] >= 8:
                    self.semi_frozen_classes.add(cls)
                    log_utils.debug(f"类别 {cls} 移入半冻结区，暂缓强化训练", tag="CAT_DATASET")

        return confirmed_pseudo_difficult

    def _update_class_history(self, cls_id, difficulty_score, is_difficult):
        """更新类别历史记录"""
        # 确保类别在历史记录中
        if cls_id not in self.class_history:
            self.class_history[cls_id] = {
                'difficulty_scores': [],
                'is_difficult_history': [],
                'consecutive_difficult': 0,
                'consistency': 0.8,  # 提高默认一致性，避免误判
                'prediction_entropy': 0.3,  # 降低默认熵值，避免误判
                'avg_confidence': 0.7  # 提高默认置信度，避免误判
            }

        # 更新历史记录
        history = self.class_history[cls_id]
        history['difficulty_scores'].append(difficulty_score)
        history['is_difficult_history'].append(is_difficult)

        # 只保留最近的5个记录
        if len(history['difficulty_scores']) > 5:
            history['difficulty_scores'] = history['difficulty_scores'][-5:]
            history['is_difficult_history'] = history['is_difficult_history'][-5:]

        # 更新连续难分计数
        if is_difficult:
            history['consecutive_difficult'] += 1
        else:
            history['consecutive_difficult'] = 0

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        image_path, label = self.samples[idx]
        image = Image.open(image_path).convert("RGB")

        # 应用bbox裁剪(如果存在)
        # if image_path in self.bbox_map:
        #     bbox = self.bbox_map[image_path]
        #     image = image.crop(bbox)

        image = transforms.Resize((224, 224))(image)

        # 根据类别难度选择不同的变换
        if self.transform_difficult and label in self.difficult_classes:
            transformed = self.transform_difficult(image)
        elif self.transform:
            transformed = self.transform(image)
        else:
            transformed = transforms.ToTensor()(image)

        label_idx = self.class_to_idx[label]
        return transformed, label_idx
