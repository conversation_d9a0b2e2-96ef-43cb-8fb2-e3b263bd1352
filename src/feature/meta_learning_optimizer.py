"""
元学习超参数优化器模块

实现基于梯度的超参数自动优化，支持：
1. 学习率、权重衰减等优化器参数的自动调整
2. 损失函数权重的动态优化
3. 批次大小的自适应选择
4. 基于性能反馈的超参数搜索

设计原则：
- 遵循现有回调机制和配置模式
- 与现有训练流程无缝集成
- 支持多种元学习算法
"""

import torch
import torch.nn as nn
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple, Union, Callable
from collections import defaultdict, deque
import copy
import random
from utils import log_utils
from config import DEFAULT_TRAIN_CONFIG, TrainConfig


class MetaLearningCallback(ABC):
    """元学习回调接口"""
    
    @abstractmethod
    def on_meta_learning_start(self, optimizer: 'MetaLearningOptimizer', epoch: int):
        """元学习开始时的回调"""
        pass
    
    @abstractmethod
    def on_hyperparams_update(self, optimizer: 'MetaLearningOptimizer', 
                             old_params: Dict[str, Any], new_params: Dict[str, Any]):
        """超参数更新时的回调"""
        pass
    
    @abstractmethod
    def on_performance_evaluation(self, optimizer: 'MetaLearningOptimizer', 
                                 performance_metrics: Dict[str, float]):
        """性能评估时的回调"""
        pass
    
    @abstractmethod
    def on_convergence_detected(self, optimizer: 'MetaLearningOptimizer', epoch: int):
        """收敛检测时的回调"""
        pass


class MetaLearningMonitorCallback(MetaLearningCallback):
    """元学习监控回调"""
    
    def __init__(self, log_config: Optional[TrainConfig] = None):
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        self.update_history = []
        self.performance_history = []
    
    def on_meta_learning_start(self, optimizer: 'MetaLearningOptimizer', epoch: int):
        """记录元学习开始"""
        log_utils.info(f"元学习优化启动: 轮次{epoch}", tag="META_LEARNING")
    
    def _log_param_update(self, param_name: str, old_value: Any, new_value: Any):
        """记录单个超参数的更新。"""
        if isinstance(old_value, (int, float)) and isinstance(new_value, (int, float)):
            log_utils.info(f"超参数更新: {param_name} {old_value} → {new_value}", tag="META_LEARNING")

    def on_hyperparams_update(self, optimizer: 'MetaLearningOptimizer', 
                             old_params: Dict[str, Any], new_params: Dict[str, Any]):
        """记录超参数更新"""
        for param_name, new_value in new_params.items():
            if param_name in old_params:
                self._log_param_update(param_name, old_params[param_name], new_value)
        
        self.update_history.append({
            'old_params': copy.deepcopy(old_params),
            'new_params': copy.deepcopy(new_params),
            'timestamp': optimizer.current_epoch
        })

    def _log_performance_improvement(self, metric_name: str, prev_value: float, curr_value: float):
        """如果性能提升，则记录。"""
        improvement = curr_value - prev_value
        if improvement > 0:
            log_utils.info(f"性能提升: {metric_name} {prev_value:.4f} → {curr_value:.4f} (提升: +{improvement:.4f})", tag="META_LEARNING")

    def _check_and_log_improvements(self, prev_metrics: Dict[str, float], curr_metrics: Dict[str, float]):
        """检查并记录所有指标的性能改进。"""
        for metric_name, curr_value in curr_metrics.items():
            if metric_name in prev_metrics:
                self._log_performance_improvement(metric_name, prev_metrics[metric_name], curr_value)

    def on_performance_evaluation(self, optimizer: 'MetaLearningOptimizer', 
                                 performance_metrics: Dict[str, float]):
        """记录性能评估"""
        self.performance_history.append({
            'metrics': copy.deepcopy(performance_metrics),
            'timestamp': optimizer.current_epoch
        })
        
        # 更新判断条件：需要至少4个点进行性能比较
        if len(self.performance_history) >= 4:
            # 使用最近4个点计算平均性能变化
            recent_metrics = list(self.performance_history)[-4:]
            avg_improvement = 0.0
            count = 0
            
            for i in range(1, len(recent_metrics)):
                prev_metrics = recent_metrics[i-1]['metrics']
                curr_metrics = recent_metrics[i]['metrics']
                improvement = optimizer._calculate_performance_change(prev_metrics, curr_metrics)
                avg_improvement += improvement
                count += 1
            
            if count > 0:
                avg_improvement /= count
                # 记录平均性能改进
                if avg_improvement > 0:
                    log_utils.info(f"MetaLearning - 平均性能改进: {avg_improvement:.6f} (基于最近{count}个epoch)", tag="META_LEARNING")
        elif len(self.performance_history) >= 2:
            # 备选方案：当数据不足时使用2点比较
            prev_metrics = self.performance_history[-2]['metrics']
            self._check_and_log_improvements(prev_metrics, performance_metrics)

    def on_convergence_detected(self, optimizer: 'MetaLearningOptimizer', epoch: int):
        """记录收敛检测"""
        log_utils.info(f"MetaLearning - 收敛检测: 第{epoch}轮检测到收敛", tag="META_LEARNING")
    
    def get_update_history(self) -> List[Dict]:
        """获取更新历史"""
        return self.update_history.copy()
    
class HyperparameterSpace:
    """超参数搜索空间定义"""
    
    def __init__(self, config: Optional[TrainConfig] = None):
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.search_space = self._define_search_space()
        self.constraints = self._define_constraints()
        self.constraint_handlers = {
            'ratio': self._apply_ratio_constraint,
            'sum': self._apply_sum_constraint,
        }
        self.clippers = {
            'continuous': self._clip_continuous,
            'discrete': self._clip_discrete,
        }

    def _define_search_space(self) -> Dict[str, Dict]:
        """定义超参数搜索空间"""
        search_space = {
            'backbone_lr': {'type': 'continuous', 'range': self.config.lr_search_range, 'log_scale': True},
            'head_lr': {'type': 'continuous', 'range': self.config.lr_search_range, 'log_scale': True},
            'weight_decay': {'type': 'continuous', 'range': self.config.weight_decay_range, 'log_scale': True},
            'batch_size': {'type': 'discrete', 'options': self.config.batch_size_options},
        }
        
        # 只有在启用损失权重优化时才添加损失权重参数
        if getattr(self.config, 'enable_loss_weight_optimization', False):
            search_space.update({
                'circle_weight': {'type': 'continuous', 'range': self.config.circle_weight_range, 'log_scale': False},
                'arcface_weight': {'type': 'continuous', 'range': self.config.arcface_weight_range, 'log_scale': False},
            })
        
        return search_space
    
    def _define_constraints(self) -> List[Dict]:
        """定义约束条件"""
        constraints = [{
            'type': 'ratio',
            'params': ['head_lr', 'backbone_lr'],
            'min_ratio': self.config.min_lr_ratio,
            'max_ratio': self.config.max_lr_ratio
        }]
        
        # 只有在启用损失权重优化时才添加权重和约束
        if getattr(self.config, 'enable_loss_weight_optimization', False) and self.config.weight_sum_constraint:
            constraints.append({
                'type': 'sum',
                'params': ['circle_weight', 'arcface_weight'],
                'target_sum': 1.0,
                'tolerance': 0.01
            })
        
        return constraints
    
    def _apply_ratio_constraint(self, params: Dict[str, Any], constraint: Dict[str, Any]) -> Dict[str, Any]:
        """应用比例约束"""
        param1, param2 = constraint['params']
        if param1 not in params or param2 not in params:
            return params
        
        ratio = params[param1] / params[param2]
        if ratio > constraint['max_ratio']:
            params[param1] = params[param2] * constraint['max_ratio']
        elif ratio < constraint['min_ratio']:
            params[param1] = params[param2] * constraint['min_ratio']
        return params

    def _apply_sum_constraint(self, params: Dict[str, Any], constraint: Dict[str, Any]) -> Dict[str, Any]:
        """应用和约束"""
        param_names = constraint['params']
        if not all(p in params for p in param_names):
            return params

        current_sum = sum(params[p] for p in param_names)
        target_sum = constraint['target_sum']
        
        if abs(current_sum - target_sum) > constraint['tolerance']:
            scale_factor = target_sum / current_sum
            for p in param_names:
                params[p] *= scale_factor
        return params

    def _apply_constraints(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """应用所有约束条件"""
        for constraint in self.constraints:
            handler = self.constraint_handlers.get(constraint['type'])
            if handler:
                params = handler(params, constraint)
        return params
    
    def _clip_continuous(self, value: float, param_config: Dict[str, Any]) -> float:
        """裁剪连续值"""
        low, high = param_config['range']
        return np.clip(value, low, high)

    def _clip_discrete(self, value: float, param_config: Dict[str, Any]) -> int:
        """裁剪离散值，找到最近的选项"""
        options = param_config['options']
        return min(options, key=lambda x: abs(x - value))

    def clip_to_bounds(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """将参数裁剪到有效范围"""
        clipped_params = {}
        for param_name, value in params.items():
            if param_name not in self.search_space:
                clipped_params[param_name] = value
                continue

            param_config = self.search_space[param_name]
            clipper = self.clippers.get(param_config['type'])
            
            if clipper:
                clipped_params[param_name] = clipper(value, param_config)
            else:
                clipped_params[param_name] = value
        
        return clipped_params


class GradientEstimator:
    """超参数梯度估计器"""
    
    def __init__(self):
        # 移除了对MetaOptimizerConfig的依赖，使用默认参数
        self.method = "finite_difference"
        self.epsilon = 1e-5
        self.gradient_accumulation_steps = 1
    
class MetaLearningOptimizer:
    """元学习超参数优化器"""
    
    def __init__(self, 
                 config: Optional[TrainConfig] = None,
                 log_config: Optional[TrainConfig] = None,
                 callbacks: Optional[List[MetaLearningCallback]] = None):
        
        self.config = config or DEFAULT_TRAIN_CONFIG
        self.log_config = log_config or DEFAULT_TRAIN_CONFIG
        self._initialize_components()
        self._initialize_state()
        self.callbacks = callbacks or []
        self.callback_handlers = self._initialize_callback_handlers()
        
        log_utils.info("MetaLearningOptimizer 初始化完成", tag="META_LEARNING")

    def _initialize_components(self):
        """初始化优化器子组件。"""
        self.param_space = HyperparameterSpace(self.config)
        self.gradient_estimator = GradientEstimator()

    def _initialize_state(self):
        """初始化优化器状态变量。"""
        self.current_epoch = 0
        self.current_params = {}
        self.performance_history = deque(maxlen=self.config.history_length)
        self.param_history = deque(maxlen=self.config.history_length)
        self.convergence_counter = 0
        self.step_size = self.config.meta_lr
        self.exploration_rate = self.config.exploration_rate
        self.momentum_buffer = {}
        self.adam_m, self.adam_v, self.adam_t = {}, {}, 0

    def _initialize_callback_handlers(self) -> Dict[str, Callable]:
        """初始化回调事件处理器。"""
        return {
            "meta_learning_start": lambda cb, *args, **kwargs: cb.on_meta_learning_start(self, *args, **kwargs),
            "hyperparams_update": lambda cb, *args, **kwargs: cb.on_hyperparams_update(self, *args, **kwargs),
            "performance_evaluation": lambda cb, *args, **kwargs: cb.on_performance_evaluation(self, *args, **kwargs),
            "convergence_detected": lambda cb, *args, **kwargs: cb.on_convergence_detected(self, *args, **kwargs),
        }
    
    def add_callback(self, callback: MetaLearningCallback):
        """添加回调"""
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: MetaLearningCallback):
        """移除回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def _trigger_callbacks(self, event_type: str, *args, **kwargs):
        """触发回调"""
        handler_template = self.callback_handlers.get(event_type)
        if not handler_template:
            return

        for callback in self.callbacks:
            try:
                handler_template(callback, *args, **kwargs)
            except Exception as e:
                log_utils.error(f"元学习回调 {event_type} 执行失败: {e}", tag="META_LEARNING")
    
    def initialize_params(self, initial_params: Dict[str, Any]):
        """初始化超参数和优化器状态"""
        self.current_params = copy.deepcopy(initial_params)
        self.param_history.append(copy.deepcopy(initial_params))
        
        for param_name in self.current_params:
            self.momentum_buffer[param_name] = 0.0
            self.adam_m[param_name] = 0.0
            self.adam_v[param_name] = 0.0
    
    def should_optimize(self, epoch: int) -> bool:
        """判断是否应该进行元学习优化"""
        if not self.config.enable_meta_learning:
            return False
        
        is_after_start = epoch >= self.config.meta_learning_start_epoch
        is_on_interval = (epoch - self.config.meta_learning_start_epoch) % self.config.meta_learning_interval == 0
        
        return is_after_start and is_on_interval

    def optimize_hyperparameters(self, epoch: int, performance_metrics: Dict[str, float]) -> Dict[str, Any]:
        """优化超参数主流程"""
        self.current_epoch = epoch
        self._trigger_callbacks("meta_learning_start", epoch=epoch)
        
        self.performance_history.append(copy.deepcopy(performance_metrics))
        self._trigger_callbacks("performance_evaluation", performance_metrics=performance_metrics)
        
        if self._check_convergence():
            self._trigger_callbacks("convergence_detected", epoch=epoch)
            return self.current_params
        
        new_params = self._decide_and_execute_update_strategy()
        
        self._update_and_log_params(new_params)
        self._decay_exploration_rate()
        
        return self.current_params

    def _decide_and_execute_update_strategy(self) -> Dict[str, Any]:
        """决定并执行探索或利用策略"""
        from utils.deterministic_seed_manager import get_seed_manager, create_meta_learning_context, SeedScope
        
        # 🔧 使用统一种子管理器控制随机性
        manager = get_seed_manager()
        context = create_meta_learning_context(getattr(self, 'current_epoch', 0))
        
        with manager.deterministic_context(context, SeedScope.EPOCH, include_torch=False, include_random=False):
            if np.random.random() < self.exploration_rate:
                log_utils.info(f"MetaLearning - 探索模式: 探索率={self.exploration_rate:.3f}", tag="META_LEARNING")
                return self._explore_hyperparameters()
        
        log_utils.info(f"MetaLearning - 利用模式: 探索率={self.exploration_rate:.3f}", tag="META_LEARNING")
        return self._exploit_hyperparameters()

    def _update_and_log_params(self, new_params: Dict[str, Any]):
        """更新并记录参数"""
        old_params = copy.deepcopy(self.current_params)
        self.current_params = new_params
        self.param_history.append(copy.deepcopy(new_params))
        self._trigger_callbacks("hyperparams_update", old_params=old_params, new_params=new_params)

    def _decay_exploration_rate(self):
        """衰减探索率"""
        self.exploration_rate *= self.config.exploration_decay
    
    def _perturb_continuous_param(self, value: float, param_config: Dict[str, Any]) -> float:
        """对连续参数添加高斯噪声扰动"""
        param_range = param_config['range'][1] - param_config['range'][0]
        noise_std = param_range * 0.1 * self.exploration_rate
        # 🔧 随机性已在上层方法中设置种子，这里直接使用
        return value + np.random.normal(0, noise_std)

    def _perturb_discrete_param(self, value: Any, param_config: Dict[str, Any]) -> Any:
        """随机选择离散参数的新值"""
        # 🔧 随机性已在上层方法中设置种子，这里直接使用
        if np.random.random() < 0.3:  # 30%概率改变离散参数
            # 使用确定性种子管理器已设置的随机状态
            return np.random.choice(param_config['options'])
        return value

    def _explore_hyperparameters(self) -> Dict[str, Any]:
        """探索模式：随机扰动超参数"""
        new_params = copy.deepcopy(self.current_params)
        
        perturbations = {
            'continuous': self._perturb_continuous_param,
            'discrete': self._perturb_discrete_param,
        }

        for name, value in new_params.items():
            if name in self.param_space.search_space:
                config = self.param_space.search_space[name]
                perturb_func = perturbations.get(config['type'])
                if perturb_func:
                    new_params[name] = perturb_func(value, config)
        
        new_params = self.param_space.clip_to_bounds(new_params)
        return self.param_space._apply_constraints(new_params)
    
    def _exploit_hyperparameters(self) -> Dict[str, Any]:
        """利用模式：基于历史性能优化超参数"""
        # 更新判断条件：需要至少4个点进行线性回归
        if len(self.performance_history) < 4 or len(self.param_history) < 4:
            return self.current_params
        
        gradients = self._estimate_performance_gradients()
        return self._apply_optimizer_update(gradients)
    
    def _calculate_performance_change(self, perf1: Dict, perf2: Dict) -> float:
        """计算两个性能记录点之间的平均变化"""
        # 扩展主要指标列表，包含更多关键性能指标
        main_metrics = [
            'separation_ratio',      # 分离比
            'val_acc',              # 验证准确率
            'feature_quality_score', # 特征质量分数
            'inter_distance',        # 类间距离
            'intra_distance',        # 类内距离
            'fisher_score',          # Fisher判别分数
            'boundary_ratio',        # 边界样本比例
            'top1_accuracy',         # Top-1准确率
            'top3_accuracy',         # Top-3准确率
            'top5_accuracy'          # Top-5准确率
        ]
        
        changes = []
        for metric in main_metrics:
            if metric in perf1 and metric in perf2:
                # 类间距离：值越大越好，直接计算差值
                if metric == 'inter_distance':
                    changes.append(perf2[metric] - perf1[metric])
                # 类内距离：值越小越好，取负值
                elif metric == 'intra_distance':
                    changes.append(-(perf2[metric] - perf1[metric]))
                # 其他指标：值越大越好，直接计算差值
                else:
                    changes.append(perf2[metric] - perf1[metric])
        
        return np.mean(changes) if changes else 0.0

    def _estimate_performance_gradients(self) -> Dict[str, float]:
        """使用滑动窗口计算梯度，提高稳定性"""
        if len(self.performance_history) < 4 or len(self.param_history) < 4:
            return self._fallback_gradient_estimation()  # 保留原有方法作为备选
        
        # 使用最近4个点进行线性回归
        X = np.array(range(-3, 1)).reshape(-1, 1)  # 时间序列 [-3, -2, -1, 0]
        
        # 计算综合性能指标变化趋势
        performance_scores = []
        for p in list(self.performance_history)[-4:]:
            score = self._calculate_comprehensive_score(p)  # 计算综合性能得分
            performance_scores.append(score)
        
        # 计算性能变化趋势
        perf_slope, _ = np.polyfit(X.flatten(), performance_scores, 1)
        
        gradients = {}
        for name in self.current_params:
            param_values = [p[name] for p in list(self.param_history)[-4:]]
            param_slope, _ = np.polyfit(X.flatten(), param_values, 1)
            
            # 梯度 = 性能变化斜率 / 参数变化斜率
            gradients[name] = perf_slope / param_slope if abs(param_slope) > 1e-8 else 0.0
        
        return gradients
    
    def _calculate_comprehensive_score(self, performance_metrics: Dict[str, float]) -> float:
        """计算综合性能得分"""
        # 扩展主要指标列表，包含更多关键性能指标
        main_metrics = [
            'separation_ratio',      # 分离比
            'val_acc',              # 验证准确率
            'feature_quality_score', # 特征质量分数
            'inter_distance',        # 类间距离
            'intra_distance',        # 类内距离
            'fisher_score',          # Fisher判别分数
            'boundary_ratio',        # 边界样本比例
            'top1_accuracy',         # Top-1准确率
            'top3_accuracy',         # Top-3准确率
            'top5_accuracy'          # Top-5准确率
        ]
        
        scores = []
        for metric in main_metrics:
            if metric in performance_metrics:
                # 类内距离：值越小越好，需要取负值
                if metric == 'intra_distance':
                    scores.append(-performance_metrics[metric])
                # 其他指标：值越大越好，直接使用
                else:
                    scores.append(performance_metrics[metric])
        
        return np.mean(scores) if scores else 0.0
    
    def _fallback_gradient_estimation(self) -> Dict[str, float]:
        """备选梯度估计方法 - 使用原有两点差分"""
        if len(self.performance_history) < 2 or len(self.param_history) < 2:
            return {}
        
        recent_perf = list(self.performance_history)[-2:]
        recent_params = list(self.param_history)[-2:]
        
        perf_change = self._calculate_performance_change(recent_perf[0], recent_perf[1])
        
        gradients = {}
        for name, p0 in recent_params[0].items():
            if name in recent_params[1]:
                p1 = recent_params[1][name]
                param_change = p1 - p0
                gradients[name] = perf_change / param_change if abs(param_change) > 1e-8 else 0.0
        
        return gradients
    
    def _apply_optimizer_update(self, gradients: Dict[str, float]) -> Dict[str, Any]:
        """应用优化器更新"""
        # 从配置获取优化器类型，默认为adamw以与train.py保持一致
        meta_optimizer_type = getattr(self.config, 'meta_optimizer_type', 'adamw')
        
        if meta_optimizer_type == "adamw":
            updated_params = self._adamw_update(self.current_params, gradients)
        elif meta_optimizer_type == "adam":
            updated_params = self._adam_update(self.current_params, gradients)
        else: # 默认SGD
            updated_params = self._sgd_update(self.current_params, gradients)
        
        clipped_params = self.param_space.clip_to_bounds(updated_params)
        return self.param_space._apply_constraints(clipped_params)

    def _update_adamw_param(self, name: str, value: Any, grad: float) -> Any:
        """使用AdamW更新单个参数"""
        beta1, beta2, eps = 0.9, 0.999, 1e-8
        weight_decay = getattr(self.config, 'meta_weight_decay', 1e-4)
        
        self.adam_m[name] = beta1 * self.adam_m[name] + (1 - beta1) * grad
        self.adam_v[name] = beta2 * self.adam_v[name] + (1 - beta2) * grad**2
        
        m_hat = self.adam_m[name] / (1 - beta1**self.adam_t)
        v_hat = self.adam_v[name] / (1 - beta2**self.adam_t)
        
        # AdamW更新：先应用权重衰减，再应用梯度更新
        update = self.step_size * m_hat / (np.sqrt(v_hat) + eps)
        # 权重衰减项
        weight_decay_update = self.step_size * weight_decay * value
        
        return value - weight_decay_update + update

    def _adamw_update(self, params: Dict[str, Any], gradients: Dict[str, float]) -> Dict[str, Any]:
        """AdamW优化器更新"""
        self.adam_t += 1
        new_params = copy.deepcopy(params)
        
        for name, value in new_params.items():
            if name in gradients:
                new_params[name] = self._update_adamw_param(name, value, gradients[name])
        
        return new_params

    def _update_adam_param(self, name: str, value: Any, grad: float) -> Any:
        """使用Adam更新单个参数"""
        beta1, beta2, eps = 0.9, 0.999, 1e-8
        
        self.adam_m[name] = beta1 * self.adam_m[name] + (1 - beta1) * grad
        self.adam_v[name] = beta2 * self.adam_v[name] + (1 - beta2) * grad**2
        
        m_hat = self.adam_m[name] / (1 - beta1**self.adam_t)
        v_hat = self.adam_v[name] / (1 - beta2**self.adam_t)
        
        update = self.step_size * m_hat / (np.sqrt(v_hat) + eps)
        return value + update

    def _adam_update(self, params: Dict[str, Any], gradients: Dict[str, float]) -> Dict[str, Any]:
        """Adam优化器更新"""
        self.adam_t += 1
        new_params = copy.deepcopy(params)
        
        for name, value in new_params.items():
            if name in gradients:
                new_params[name] = self._update_adam_param(name, value, gradients[name])
        
        return new_params
    
    def _update_sgd_param(self, name: str, value: Any, grad: float) -> Any:
        """使用SGD with Momentum更新单个参数"""
        self.momentum_buffer[name] = (
            self.config.meta_momentum * self.momentum_buffer.get(name, 0.0) + grad
        )
        return value + self.step_size * self.momentum_buffer[name]

    def _sgd_update(self, params: Dict[str, Any], gradients: Dict[str, float]) -> Dict[str, Any]:
        """SGD with momentum更新"""
        new_params = copy.deepcopy(params)
        for name, value in new_params.items():
            if name in gradients:
                new_params[name] = self._update_sgd_param(name, value, gradients[name])
        
        return new_params
    
    def _get_recent_performance_changes(self) -> List[float]:
        """获取最近的性能变化列表"""
        performances = list(self.performance_history)[-self.config.performance_window:]
        if len(performances) < 2:
            return []
        
        changes = []
        for i in range(1, len(performances)):
            change = self._calculate_performance_change(performances[i-1], performances[i])
            changes.append(change)
        return changes

    def _update_convergence_counter(self, performance_changes: List[float]):
        """根据性能变化更新收敛计数器"""
        if not performance_changes:
            return

        avg_improvement = np.mean(performance_changes)
        if avg_improvement < self.config.improvement_threshold:
            self.convergence_counter += 1
        else:
            self.convergence_counter = 0

    def _check_convergence(self) -> bool:
        """检查是否收敛"""
        if len(self.performance_history) < self.config.performance_window:
            return False
        
        performance_changes = self._get_recent_performance_changes()
        self._update_convergence_counter(performance_changes)
        
        return self.convergence_counter >= self.config.convergence_patience
    
    def get_current_params(self) -> Dict[str, Any]:
        """获取当前超参数"""
        return copy.deepcopy(self.current_params)
    
    def get_performance_history(self) -> List[Dict[str, float]]:
        """获取性能历史"""
        return list(self.performance_history)

def create_meta_learning_system(config_type: str = "default") -> Tuple[MetaLearningOptimizer, MetaLearningMonitorCallback]:
    """创建完整的元学习系统"""
    config_factory = {
        "aggressive": "src.config.create_aggressive_meta_learning_config",
        "conservative": "src.config.create_conservative_meta_learning_config",
    }
    
    if config_type in config_factory:
        # 动态导入，避免不必要的依赖
        module_path, func_name = config_factory[config_type].rsplit('.', 1)
        module = __import__(module_path, fromlist=[func_name])
        config = getattr(module, func_name)()
    else:
        config = DEFAULT_TRAIN_CONFIG
    
    monitor_callback = MetaLearningMonitorCallback()
    optimizer = MetaLearningOptimizer(
        config=config,
        callbacks=[monitor_callback]
    )
    
    return optimizer, monitor_callback 