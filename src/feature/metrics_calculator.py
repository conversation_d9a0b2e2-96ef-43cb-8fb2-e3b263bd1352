"""
指标计算器 - 用于计算和跟踪训练过程中的特征质量指标
"""

import os
from utils import log_utils
import json
import random
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from sklearn.decomposition import PCA
import contextlib
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
import torch


# ==================== 回调机制 ====================

class MetricsCallback(ABC):
    """指标计算回调接口"""
    
    @abstractmethod
    def on_metrics_calculation_start(self, calculator: 'MetricsCalculator', embeddings_shape: Tuple[int, ...]):
        """指标计算开始时的回调"""
        pass
    
    @abstractmethod
    def on_distance_computed(self, calculator: 'MetricsCalculator', intra_avg: float, inter_avg: float):
        """距离计算完成时的回调"""
        pass
    
    @abstractmethod
    def on_quality_metrics_computed(self, calculator: 'MetricsCalculator', quality_metrics: Dict[str, Any]):
        """质量指标计算完成时的回调"""
        pass
    
    @abstractmethod
    def on_separation_ratio_updated(self, calculator: 'MetricsCalculator', separation_ratio: float):
        """特征分离比更新时的回调"""
        pass
    
    @abstractmethod
    def on_metrics_calculation_complete(self, calculator: 'MetricsCalculator', all_metrics: Dict[str, Any]):
        """指标计算完全完成时的回调"""
        pass


class MetricsMonitorCallback(MetricsCallback):
    """指标计算过程监控回调"""
    
    def __init__(self, log_level: str = "debug", enable_detailed_analysis: bool = True):
        self.log_level = log_level
        self.enable_detailed_analysis = enable_detailed_analysis
        self.calculation_stats = {}
    
    def on_metrics_calculation_start(self, calculator: 'MetricsCalculator', embeddings_shape: Tuple[int, ...]):
        """记录指标计算开始"""
        if self.log_level == "debug":
            log_utils.debug(f"MetricsCalculator - 开始指标计算，嵌入形状: {embeddings_shape}", tag="METRICS_CALCULATOR")
        self.calculation_stats['embeddings_shape'] = embeddings_shape
        self.calculation_stats['epoch'] = getattr(calculator, 'epoch_counter', 0)
    
    def on_distance_computed(self, calculator: 'MetricsCalculator', intra_avg: float, inter_avg: float):
        """记录距离计算结果"""
        self.calculation_stats['intra_distance'] = intra_avg
        self.calculation_stats['inter_distance'] = inter_avg
        
        if self.log_level == "debug":
            log_utils.debug(f"MetricsCalculator - 距离计算完成，类内: {intra_avg:.4f}, 类间: {inter_avg:.4f}", tag="METRICS_CALCULATOR")
    
    def on_quality_metrics_computed(self, calculator: 'MetricsCalculator', quality_metrics: Dict[str, Any]):
        """记录质量指标"""
        if self.enable_detailed_analysis:
            self.calculation_stats['quality_metrics'] = quality_metrics.copy()
        
        if self.log_level == "debug":
            fisher_score = quality_metrics.get('fisher_score', 0)
            log_utils.debug(f"MetricsCalculator - 质量指标计算完成，Fisher分数: {fisher_score:.4f}", tag="METRICS_CALCULATOR")
    
    def on_separation_ratio_updated(self, calculator: 'MetricsCalculator', separation_ratio: float):
        """记录特征分离比"""
        self.calculation_stats['separation_ratio'] = separation_ratio
        
        if self.log_level == "debug":
            log_utils.debug(f"MetricsCalculator - 特征分离比更新: {separation_ratio:.4f}", tag="METRICS_CALCULATOR")
    
    def on_metrics_calculation_complete(self, calculator: 'MetricsCalculator', all_metrics: Dict[str, Any]):
        """记录计算完成"""
        self.calculation_stats['total_metrics_count'] = len(all_metrics)
        
        if self.log_level == "debug":
            log_utils.debug(f"MetricsCalculator - 指标计算完成，总指标数: {len(all_metrics)}", tag="METRICS_CALCULATOR")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取计算统计信息"""
        return self.calculation_stats.copy()


class MetricsAnalysisCallback(MetricsCallback):
    """指标分析回调，用于收集详细的分析数据"""
    
    def __init__(self):
        self.analysis_data = {}
    
    def on_metrics_calculation_start(self, calculator: 'MetricsCalculator', embeddings_shape: Tuple[int, ...]):
        """初始化分析数据"""
        self.analysis_data = {'embeddings_shape': embeddings_shape}
    
    def on_distance_computed(self, calculator: 'MetricsCalculator', intra_avg: float, inter_avg: float):
        """收集距离分析数据"""
        self.analysis_data['distance_analysis'] = {
            'intra_avg': intra_avg,
            'inter_avg': inter_avg,
            'distance_ratio': inter_avg / (intra_avg + 1e-8)
        }
    
    def on_quality_metrics_computed(self, calculator: 'MetricsCalculator', quality_metrics: Dict[str, Any]):
        """收集质量指标分析"""
        self.analysis_data['quality_analysis'] = quality_metrics.copy()
    
    def on_separation_ratio_updated(self, calculator: 'MetricsCalculator', separation_ratio: float):
        """收集分离比分析"""
        self.analysis_data['separation_analysis'] = {
            'current_ratio': separation_ratio,
            'ratio_category': self._categorize_separation_ratio(separation_ratio)
        }
    
    def on_metrics_calculation_complete(self, calculator: 'MetricsCalculator', all_metrics: Dict[str, Any]):
        """收集完整分析数据"""
        self.analysis_data['complete_metrics'] = all_metrics.copy()
    
    def _categorize_separation_ratio(self, ratio: float) -> str:
        """分类特征分离比"""
        if ratio > 0.8:
            return "优秀"
        elif ratio > 0.6:
            return "良好"
        elif ratio > 0.4:
            return "基线"
        else:
            return "需要改进"
    
    def get_analysis_data(self) -> Dict[str, Any]:
        """获取完整的分析数据"""
        return self.analysis_data.copy()


# 添加工具类用于常用的检查和验证
class MetricsUtils:
    """工具类，提供数据验证和通用功能"""

    @staticmethod
    def ensure_dir_exists(path):
        """确保目录存在，不存在则创建"""
        os.makedirs(path, exist_ok=True)

    @staticmethod
    def is_valid_data(data_list):
        """检查数据列表是否有效"""
        return data_list is not None and len(data_list) > 0

    @staticmethod
    def is_valid_dict_data(data_dict):
        """检查字典数据是否有效"""
        return data_dict is not None and len(data_dict) > 0

    @staticmethod
    def safe_mean(data, default=0):
        """安全计算平均值，避免空列表"""
        return np.mean(data) if MetricsUtils.is_valid_data(data) else default

    @staticmethod
    def safe_max(data, default=0):
        """安全计算最大值，避免空列表"""
        return max(data) if MetricsUtils.is_valid_data(data) else default

    @staticmethod
    @contextlib.contextmanager
    def figure_context(figsize=None):
        """创建和管理matplotlib图形的上下文管理器"""
        fig = plt.figure(figsize=figsize) if figsize else plt.figure()
        try:
            yield fig
        finally:
            plt.close(fig)


# 优化指标计算类 - 重点关注特征质量指标
class MetricsCalculator:
    """
    计算和跟踪训练过程中的各种指标，重点关注特征质量
    """
    # 各类输出文件的默认路径
    DEFAULT_METRICS_PATH = "output/feature_metrics.json"
    DEFAULT_PLOT_PATH = "output/training_metrics.png"
    DEFAULT_WEIGHT_BALANCE_PATH = "output/weight_balance_analysis.png"
    DEFAULT_BOUNDARY_PATH = "output/boundary_analysis.png"
    DEFAULT_ANIMATION_DIR = "output/animation"

    def __init__(self, config_or_callbacks = None):
        """
        初始化指标计算器 - 支持配置对象或回调列表
        
        Args:
            config_or_callbacks: 配置对象或回调函数列表
        """
        # 兼容性处理：支持传入配置对象或回调列表
        if config_or_callbacks is None:
            self.callbacks = []
        elif isinstance(config_or_callbacks, list):
            self.callbacks = config_or_callbacks
        else:
            # 传入的是配置对象，不是回调列表
            self.callbacks = []
            
        self.reset()

        
        log_utils.info("MetricsCalculator V5 初始化完成，支持高级特征分析", tag="METRICS_CALCULATOR")

    def add_callback(self, callback: MetricsCallback) -> None:
        """添加指标计算回调"""
        self.callbacks.append(callback)
        log_utils.info(f"添加指标回调: {callback.__class__.__name__}", tag="METRICS_CALCULATOR")

    def remove_callback(self, callback: MetricsCallback) -> None:
        """移除指标计算回调"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
            log_utils.info(f"移除指标回调: {callback.__class__.__name__}", tag="METRICS_CALCULATOR")

    def _trigger_callbacks(self, event_type: str, *args, **kwargs) -> None:
        """触发回调事件"""
        callback_methods = {
            "start": "on_metrics_calculation_start",
            "distance": "on_distance_computed", 
            "quality": "on_quality_metrics_computed",
            "separation": "on_separation_ratio_updated",
            "complete": "on_metrics_calculation_complete"
        }
        
        method_name = callback_methods.get(event_type)
        if not method_name:
            return
            
        for callback in self.callbacks:
            try:
                method = getattr(callback, method_name)
                method(self, *args, **kwargs)
            except Exception as e:
                log_utils.error(f"回调执行失败 {callback.__class__.__name__}: {e}", tag="METRICS_CALCULATOR")

    def reset(self):
        """重置所有指标数据"""
        self._reset_loss_data()
        self._reset_distance_data()
        self._reset_boundary_data()
        self.current_epoch = 0  # 用于确定性采样

    def _reset_loss_data(self):
        """重置损失相关数据"""
        self.circle_losses = []
        self.center_losses = []
        self.total_losses = []
        self.circle_weights = []
        self.center_weights = []

    def _reset_distance_data(self):
        """重置距离相关数据"""
        self.intra_dists = defaultdict(list)
        self.inter_dists = defaultdict(list)  # 修改为字典结构，支持按epoch分组
        self.separation_ratios = []
        self.feature_quality_metrics = []

    def _reset_boundary_data(self):
        """重置边界样本相关数据"""
        self.boundary_samples = []
        self.boundary_stats = []
        self.hard_weights = []
        self.ms_weights = []
        self.boundary_weights = []

    def update(self, total_loss, circle_loss, arcface_loss,
               circle_w, arcface_w, hard_w=None, ms_w=None, boundary_w=None):
        """更新训练损失和权重记录 - 适配简化的损失结构"""
        self._update_loss_records(total_loss, circle_loss, arcface_loss)
        self._update_weight_records(circle_w, arcface_w)
        self._update_miner_weights(hard_w, ms_w, boundary_w)

    def _update_loss_records(self, total_loss, circle_loss, arcface_loss):
        """更新损失记录 - 适配简化的损失结构"""
        self.total_losses.append(total_loss)
        self.circle_losses.append(circle_loss)
        self.center_losses.append(arcface_loss)  # 复用 center_losses 存储 arcface_loss (历史兼容)

    def _update_weight_records(self, circle_w, arcface_w):
        """更新权重记录 - 适配简化的权重结构"""
        self.circle_weights.append(circle_w)
        self.center_weights.append(arcface_w)  # 复用 center_weights 存储 arcface_w

    def _update_miner_weights(self, hard_w, ms_w, boundary_w):
        """更新挖掘器权重记录"""
        if hard_w is not None:
            self.hard_weights.append(hard_w)
        if ms_w is not None:
            self.ms_weights.append(ms_w)
        if boundary_w is not None:
            self.boundary_weights.append(boundary_w)

    def compute_feature_metrics(self, embeddings, labels):
        """计算特征的类内距离、类间距离和其他特征质量指标 - 增强版，支持回调机制"""
        self._trigger_callbacks("start", embeddings.shape)
        self._update_epoch_counter()

        emb_np, labels_np = self._prepare_data(embeddings, labels)
        class_embeddings = self._group_by_class(emb_np, labels_np)

        self._compute_intra_class_distances(class_embeddings)
        self._compute_inter_class_distances(class_embeddings)
        
        intra_avg, inter_avg = self.get_avg_distances()
        self._trigger_callbacks("distance", intra_avg, inter_avg)

        self._compute_quality_metrics(emb_np, class_embeddings)

    def _update_epoch_counter(self):
        """更新训练轮次计数器和难度判定系数"""
        if not hasattr(self, 'epoch_counter'):
            self.epoch_counter = 0
        else:
            self.epoch_counter += 1

        self.difficulty_scale = min(1.0 + (self.epoch_counter / 40) * 0.3, 1.3)

    def _prepare_data(self, embeddings, labels):
        """准备嵌入向量和标签的numpy数组"""
        # 🔧 修复：处理3D数据，确保嵌入数据是2D的
        if embeddings.ndim > 2:
            log_utils.debug(f"🔧 输入嵌入数据维度过高 ({embeddings.ndim}D)，reshape为2D", tag="METRICS_CALCULATOR")
            # 将多维数据flatten为2D: (batch_size, -1)
            embeddings = embeddings.reshape(embeddings.shape[0], -1)
        elif embeddings.ndim < 2:
            log_utils.warning(f"输入嵌入数据维度过低 ({embeddings.ndim}D)，无法处理", tag="METRICS_CALCULATOR")
            raise ValueError(f"嵌入数据维度必须>=2，当前为{embeddings.ndim}D")
            
        return embeddings.detach().cpu().numpy(), labels.detach().cpu().numpy()

    def _group_by_class(self, embeddings, labels):
        """按类别分组嵌入向量"""
        class_embeddings = defaultdict(list)
        for i, label in enumerate(labels):
            class_embeddings[label].append(embeddings[i])
        return class_embeddings

    def _compute_intra_class_distances(self, class_embeddings):
        """计算类内距离（余弦距离）"""
        for label, embs in class_embeddings.items():
            if len(embs) > 1:
                dists = self._calculate_intra_class_distances(embs)
                if dists:
                    self.intra_dists[label].append(np.mean(dists))

    def _calculate_intra_class_distances(self, embs):
        """计算单个类内所有点对的余弦距离"""
        embs = np.array(embs)
        embs_norm = embs / (np.linalg.norm(embs, axis=1, keepdims=True) + 1e-8)
        
        dists = []
        for i in range(len(embs)):
            for j in range(i+1, len(embs)):
                dist = self._cosine_distance(embs_norm[i], embs_norm[j])
                dists.append(dist)
        return dists

    def _cosine_distance(self, vec1, vec2):
        """计算两个向量的余弦距离 - 使用统一计算器"""
        from utils.distance_calculator import get_distance_calculator
        
        # 转换为tensor
        vec1_tensor = torch.tensor(vec1) if not isinstance(vec1, torch.Tensor) else vec1
        vec2_tensor = torch.tensor(vec2) if not isinstance(vec2, torch.Tensor) else vec2
        
        calculator = get_distance_calculator("0_1")  # 训练阶段使用[0,1]范围
        distance = calculator.cosine_distance(vec1_tensor.unsqueeze(0), vec2_tensor.unsqueeze(0))
        return distance.item()

    def _compute_inter_class_distances(self, class_embeddings):
        """计算类间距离（余弦距离）"""
        classes = list(class_embeddings.keys())
        if len(classes) > 1:
            inter_dists = self._calculate_inter_class_distances(classes, class_embeddings)
            if inter_dists:
                # 使用epoch作为key，支持按epoch分组存储
                epoch = getattr(self, 'epoch_counter', 0)
                self.inter_dists[epoch].append(np.mean(inter_dists))

    def _calculate_inter_class_distances(self, classes, class_embeddings):
        """计算类间距离列表"""
        from utils.deterministic_seed_manager import get_seed_manager, create_meta_learning_context
        
        max_pairs = min(500, len(classes) * (len(classes) - 1) // 2)
        all_pairs = [(c1, c2) for c1 in classes for c2 in classes if c1 < c2]
        
        # 使用确定性种子管理器进行采样
        seed_manager = get_seed_manager()
        context = create_meta_learning_context(epoch=getattr(self, 'current_epoch', 0))
        sampled_pairs = seed_manager.shuffle_list(all_pairs, context)[:max_pairs]
        
        inter_dists = []
        for c1, c2 in sampled_pairs:
            dist = self._calculate_class_center_distance(class_embeddings[c1], class_embeddings[c2])
            inter_dists.append(dist)
        return inter_dists

    def _calculate_class_center_distance(self, embs1, embs2):
        """计算两个类中心之间的余弦距离"""
        center1 = np.mean(embs1, axis=0)
        center2 = np.mean(embs2, axis=0)
        
        center1_norm = center1 / (np.linalg.norm(center1) + 1e-8)
        center2_norm = center2 / (np.linalg.norm(center2) + 1e-8)
        
        return self._cosine_distance(center1_norm, center2_norm)

    def _compute_quality_metrics(self, emb_np, class_embeddings):
        """计算综合特征质量指标 - 增强版，支持回调机制"""
        intra_avg, inter_avg = self.get_avg_distances()

        if intra_avg > 0:
            # 使用统一距离计算器计算分离比
            from utils.distance_calculator import compute_separation_ratio
            separation_ratio = compute_separation_ratio(intra_avg, inter_avg)
            self.separation_ratios.append(separation_ratio)
            self._trigger_callbacks("separation", separation_ratio)

            quality_metrics = self._build_quality_metrics(
                separation_ratio, intra_avg, inter_avg, class_embeddings, emb_np
            )
            
            self._finalize_quality_metrics(quality_metrics, intra_avg, inter_avg, separation_ratio, len(class_embeddings))

    def _build_quality_metrics(self, separation_ratio, intra_avg, inter_avg, class_embeddings, emb_np):
        """构建质量指标字典"""
        quality_metrics = self._create_base_metrics(separation_ratio, intra_avg, inter_avg, class_embeddings)

        if len(class_embeddings) >= 3:
            self._add_geometric_metrics(quality_metrics, class_embeddings, intra_avg)

        if len(class_embeddings) >= 2:
            self._add_fisher_metrics(quality_metrics, emb_np, class_embeddings, intra_avg, inter_avg)

        self._add_structure_metrics(quality_metrics, emb_np)
        return quality_metrics

    def _finalize_quality_metrics(self, quality_metrics, intra_avg, inter_avg, separation_ratio, num_classes):
        """完成质量指标计算并触发回调"""
        self._log_feature_quality_analysis(quality_metrics, intra_avg, inter_avg, separation_ratio, num_classes)
        self._trigger_callbacks("quality", quality_metrics)
        
        self.feature_quality_metrics.append(quality_metrics)
        
        all_metrics = {
            'separation_ratio': separation_ratio,
            'intra_avg': intra_avg,
            'inter_avg': inter_avg,
            'quality_metrics': quality_metrics
        }
        self._trigger_callbacks("complete", all_metrics)

    def _create_base_metrics(self, separation_ratio, intra_avg, inter_avg, class_embeddings):
        """创建基础质量指标字典"""
        base_metrics = {
            'epoch': len(self.separation_ratios),
            'separation_ratio': separation_ratio,
            'intra_dist': intra_avg,
            'inter_dist': inter_avg,
            'difficulty_scale': getattr(self, 'difficulty_scale', 1.0),
            'intra_std': np.std([np.mean(dists) for dists in self.intra_dists.values()])
                        if self.intra_dists else 0,
            'inter_std': np.std([np.mean(dists) for dists in self.inter_dists.values()])
                        if self.inter_dists else 0,
            'class_count': len(class_embeddings)
        }
        
        # 🔧 修复：移除center_dist_min的预设值，让几何指标计算方法完全负责
        # 如果几何指标计算失败，center_dist_min将不存在于字典中，这样更安全
        # base_metrics['center_dist_min'] = 0.0  # 移除这行危险的预设值
        
        return base_metrics
    


    def _add_geometric_metrics(self, metrics_dict, class_embeddings, intra_avg):
        """添加几何分布相关指标"""
        log_utils.debug(f"🔍 开始计算几何分布指标，类别数={len(class_embeddings)}", tag="METRICS_CALCULATOR")
        try:
            centers = self._calculate_class_centers(class_embeddings)
            log_utils.debug(f"🔍 类中心计算完成，中心数={len(centers)}", tag="METRICS_CALCULATOR")
            
            # 🔧 优化：即使类别数<3，也要计算center_dist_min以确保日志显示正确
            if len(centers) >= 2:
                log_utils.debug(f"🔍 类中心数量足够，开始计算几何分布指标", tag="METRICS_CALCULATOR")
                self._compute_geometric_distribution_metrics(metrics_dict, centers, intra_avg)
                log_utils.debug(f"🔍 几何分布指标计算完成，center_dist_min={metrics_dict.get('center_dist_min', 'N/A')}", tag="METRICS_CALCULATOR")
            else:
                # 类别数不足时，确保center_dist_min保持为0.0
                log_utils.debug(f"类中心数不足({len(centers)})，center_dist_min保持为0.0", tag="METRICS_CALCULATOR")
        except Exception as e:
            log_utils.error(f"计算几何分布指标时出错: {e}", tag="METRICS_CALCULATOR")
            import traceback
            log_utils.debug(f"几何分布指标计算异常堆栈: {traceback.format_exc()}", tag="METRICS_CALCULATOR")

    def _calculate_class_centers(self, class_embeddings):
        """计算所有类的中心点"""
        centers = []
        
        if not class_embeddings:
            log_utils.debug("类别嵌入为空，返回空的类中心数组", tag="METRICS_CALCULATOR")
            return np.empty((0, 0))
            
        for label, embs in class_embeddings.items():
            if embs is None or len(embs) == 0:
                continue
                
            try:
                # 确保数据类型一致，并处理各种输入格式
                if isinstance(embs, list):
                    if len(embs) == 0:
                        continue
                    # 检查第一个元素的类型
                    if isinstance(embs[0], (list, np.ndarray)):
                        embs_array = np.array(embs)
                    else:
                        # 如果是单个向量的列表，需要reshape
                        embs_array = np.array(embs).reshape(1, -1) if len(embs) > 0 else np.empty((0, 0))
                elif isinstance(embs, np.ndarray):
                    embs_array = embs
                else:
                    log_utils.warning(f"类别{label}的嵌入类型未知: {type(embs)}，跳过", tag="METRICS_CALCULATOR")
                    continue
                
                # 确保数组不为空且形状正确
                if embs_array.size == 0:
                    continue
                    
                # 如果是1D数组，reshape为2D
                if embs_array.ndim == 1:
                    embs_array = embs_array.reshape(1, -1)
                elif embs_array.ndim > 2:
                    # 如果维度过高，flatten除第一维外的所有维度
                    embs_array = embs_array.reshape(embs_array.shape[0], -1)
                
                # 计算类中心
                if embs_array.shape[0] > 0:
                    center = np.mean(embs_array, axis=0)
                    centers.append(center)
                    
            except Exception as e:
                log_utils.warning(f"计算类别{label}的中心点时出错: {e}，跳过该类别", tag="METRICS_CALCULATOR")
                continue
        
        if len(centers) == 0:
            log_utils.debug("没有有效的类中心，返回空数组", tag="METRICS_CALCULATOR")
            return np.empty((0, 0))
        
        try:
            centers_array = np.array(centers)
            log_utils.debug(f"成功计算{len(centers)}个类中心，形状: {centers_array.shape}", tag="METRICS_CALCULATOR")
            return centers_array
        except Exception as e:
            log_utils.error(f"合并类中心数组时出错: {e}", tag="METRICS_CALCULATOR")
            return np.empty((0, 0))

    def _compute_geometric_distribution_metrics(self, metrics_dict, centers, intra_avg):
        """计算几何分布指标"""
        log_utils.debug(f"🔍 进入_compute_geometric_distribution_metrics，centers类型={type(centers)}", tag="METRICS_CALCULATOR")
        
        # 检查centers的有效性
        if centers is None or len(centers) < 2:
            log_utils.debug(f"类中心数量不足({len(centers) if centers is not None else 0})，跳过几何分布指标计算", tag="METRICS_CALCULATOR")
            return
            
        # 检查centers的形状
        if centers.ndim != 2 or centers.shape[0] < 2:
            log_utils.debug(f"类中心数组形状无效({centers.shape if hasattr(centers, 'shape') else 'unknown'})，跳过几何分布指标计算", tag="METRICS_CALCULATOR")
            return
            
        try:
            from scipy.spatial import distance_matrix
            
            # 确保centers是有效的numpy数组
            centers = np.asarray(centers)
            if centers.size == 0:
                log_utils.debug("类中心数组为空，跳过几何分布指标计算", tag="METRICS_CALCULATOR")
                return
            
            # 计算距离矩阵
            dist_matrix = distance_matrix(centers, centers)
            
            # 安全地获取上三角矩阵的索引（排除对角线）
            n_centers = len(centers)
            if n_centers < 2:
                return
                
            # 手动创建上三角索引，避免解包错误
            upper_tri_i, upper_tri_j = [], []
            for i in range(n_centers):
                for j in range(i + 1, n_centers):
                    upper_tri_i.append(i)
                    upper_tri_j.append(j)
            
            if len(upper_tri_i) == 0:
                log_utils.debug("没有有效的类中心对，跳过几何分布指标计算", tag="METRICS_CALCULATOR")
                return
                
            # 提取上三角部分的距离
            dists = dist_matrix[upper_tri_i, upper_tri_j]

            if len(dists) > 0:
                center_dist_min = float(np.min(dists))
                center_dist_mean = float(np.mean(dists))
                center_dist_std = float(np.std(dists))
                center_dist_max = float(np.max(dists))
                
                # 🔍 详细调试：检查距离分布
                zero_count = np.sum(dists == 0.0)
                very_small_count = np.sum(dists < 1e-6)
                log_utils.debug(f"🔍 距离统计: 总数={len(dists)}, 为0的数量={zero_count}, <1e-6的数量={very_small_count}", tag="METRICS_CALCULATOR")
                log_utils.debug(f"🔍 距离范围: min={center_dist_min:.8f}, max={center_dist_max:.8f}, mean={center_dist_mean:.8f}", tag="METRICS_CALCULATOR")
                
                if zero_count > 0:
                    # 找出距离为0的类中心对
                    zero_indices = np.where(dists == 0.0)[0]
                    log_utils.warning(f"🚨 发现{zero_count}对类中心距离为0！这表明某些类别在特征空间中重叠", tag="METRICS_CALCULATOR")
                    log_utils.warning(f"   索引: {zero_indices[:10]}", tag="METRICS_CALCULATOR")
                    
                    for idx in zero_indices[:5]:  # 显示前5个
                        i, j = upper_tri_i[idx], upper_tri_j[idx]
                        center_i = centers[i]
                        center_j = centers[j]
                        actual_dist = np.linalg.norm(center_i - center_j)
                        log_utils.warning(f"  类中心对({i},{j}): 距离矩阵={dists[idx]:.8f}, 实际距离={actual_dist:.8f}", tag="METRICS_CALCULATOR")
                        
                        # 检查是否真的完全相同
                        if np.allclose(center_i, center_j, atol=1e-8):
                            log_utils.error(f"    ❌ 类别{i}和{j}的类中心完全相同！需要检查训练数据", tag="METRICS_CALCULATOR")
                        else:
                            log_utils.info(f"    ✅ 类别{i}和{j}的类中心非常接近但不完全相同", tag="METRICS_CALCULATOR")
                
                metrics_dict['center_dist_mean'] = center_dist_mean
                metrics_dict['center_dist_std'] = center_dist_std
                metrics_dict['center_dist_min'] = center_dist_min
                metrics_dict['center_dist_max'] = center_dist_max
                
                log_utils.debug(f"🔍 几何分布指标赋值: center_dist_min={center_dist_min:.6f}", tag="METRICS_CALCULATOR")

                avg_center_dist = center_dist_mean
                avg_intra_compactness = intra_avg if intra_avg > 0 else 1e-8
                metrics_dict['distribution_compactness'] = avg_center_dist / avg_intra_compactness
                
                log_utils.debug(f"几何分布指标计算成功: 类中心数={n_centers}, 距离对数={len(dists)}", tag="METRICS_CALCULATOR")
                
        except Exception as e:
            log_utils.error(f"计算几何分布指标详细错误: {e}", tag="METRICS_CALCULATOR")
            import traceback
            log_utils.debug(f"几何分布指标计算堆栈: {traceback.format_exc()}", tag="METRICS_CALCULATOR")

    def _add_fisher_metrics(self, metrics_dict, emb_np, class_embeddings, intra_avg, inter_avg):
        """添加Fisher分数和判别能力指数 - 增强数值稳定性"""
        # LLM_CONTEXT: [WHAT] 增强数值稳定性保护 [WHY] 防止分母接近0导致数值爆炸 #metrics_calculation
        
        try:
            global_center = np.mean(emb_np, axis=0)
            between_class_var, class_var_details = self._calculate_between_class_variance(
                emb_np, class_embeddings, global_center
            )
            within_class_var, class_within_var_details = self._calculate_within_class_variance(
                emb_np, class_embeddings
            )
            
            self._compute_fisher_scores(metrics_dict, between_class_var, within_class_var, inter_avg, intra_avg)
            self._log_fisher_analysis(between_class_var, within_class_var, class_var_details, class_within_var_details)
        except Exception as e:
            log_utils.error(f"计算Fisher分数时出错: {e}", tag="METRICS_CALCULATOR")

    def _calculate_between_class_variance(self, emb_np, class_embeddings, global_center):
        """计算类间方差"""
        between_class_var = 0
        total_samples = len(emb_np)
        class_var_details = {}
        
        for label, embs in class_embeddings.items():
            if len(embs) > 0:
                class_center = np.mean(embs, axis=0)
                class_size = len(embs)
                class_var_contribution = class_size * np.sum((class_center - global_center) ** 2)
                between_class_var += class_var_contribution
                class_var_details[label] = {
                    'size': class_size,
                    'var_contribution': class_var_contribution / total_samples,
                    'center_distance_to_global': np.linalg.norm(class_center - global_center)
                }
        
        return between_class_var / total_samples, class_var_details

    def _calculate_within_class_variance(self, emb_np, class_embeddings):
        """计算类内方差"""
        within_class_var = 0
        total_samples = len(emb_np)
        class_within_var_details = {}
        
        for label, embs in class_embeddings.items():
            if len(embs) > 0:
                class_center = np.mean(embs, axis=0)
                class_within_var = np.sum(np.sum((embs - class_center) ** 2, axis=1))
                within_class_var += class_within_var
                class_within_var_details[label] = {
                    'within_var': class_within_var / len(embs),
                    'avg_distance_to_center': np.mean([np.linalg.norm(emb - class_center) for emb in embs])
                }
        
        return within_class_var / total_samples, class_within_var_details

    def _compute_fisher_scores(self, metrics_dict, between_class_var, within_class_var, inter_avg, intra_avg):
        """计算Fisher分数和判别能力指数 - 增强数值稳定性"""
        # LLM_CONTEXT: [WHAT] 增强数值稳定性保护 [WHY] 防止分母接近0导致数值爆炸 #metrics_calculation
        
        # 数值稳定性参数
        EPSILON = 1e-6  # 更大的epsilon确保稳定性
        MAX_FISHER_SCORE = 1000.0  # Fisher分数上限
        MAX_DISCRIMINATIVE_POWER = 10000.0  # 判别能力上限
        
        # 使用更大的epsilon确保分母稳定
        stable_within_class_var = max(within_class_var, EPSILON)
        stable_intra_avg_sq = max(intra_avg ** 2, EPSILON)
        
        # 计算Fisher分数并限制上限
        raw_fisher_score = between_class_var / stable_within_class_var
        fisher_score = min(raw_fisher_score, MAX_FISHER_SCORE)
        
        # 计算判别能力指数并限制上限
        raw_discriminative_power = (inter_avg ** 2) / stable_intra_avg_sq
        discriminative_power = min(raw_discriminative_power, MAX_DISCRIMINATIVE_POWER)
        
        # 记录是否发生了截断
        if raw_fisher_score > MAX_FISHER_SCORE:
            log_utils.warning(f"Fisher分数过大被截断: {raw_fisher_score:.2f} -> {MAX_FISHER_SCORE}", tag="METRICS_CALCULATOR")
        
        if raw_discriminative_power > MAX_DISCRIMINATIVE_POWER:
            log_utils.warning(f"判别能力指数过大被截断: {raw_discriminative_power:.2f} -> {MAX_DISCRIMINATIVE_POWER}", tag="METRICS_CALCULATOR")
        
        metrics_dict['fisher_score'] = fisher_score
        metrics_dict['discriminative_power'] = discriminative_power

    def _log_fisher_analysis(self, between_class_var, within_class_var, class_var_details, class_within_var_details):
        """记录Fisher判别分析报告"""
        fisher_score = between_class_var / max(within_class_var, 1e-6)
        
        log_utils.info(f"📊 Fisher判别分析报告:", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ Fisher分数: {fisher_score:.6f}", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 类间方差: {between_class_var:.6f}", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 类内方差: {within_class_var:.6f}", tag="METRICS_CALCULATOR")
        
        self._log_fisher_quality_rating(fisher_score)
        self._log_class_contributions(class_var_details, between_class_var)
        self._log_within_class_analysis(class_within_var_details)

    def _log_fisher_quality_rating(self, fisher_score):
        """记录Fisher分数质量评级"""
        if fisher_score > 10.0:
            fisher_quality, quality_icon = "优秀", "🟢"
        elif fisher_score > 5.0:
            fisher_quality, quality_icon = "良好", "🟡"
        elif fisher_score > 2.0:
            fisher_quality, quality_icon = "基线", "🟠"
        else:
            fisher_quality, quality_icon = "需要改进", "🔴"
        
        log_utils.info(f"  ├─ Fisher质量评级: {quality_icon} {fisher_quality}", tag="METRICS_CALCULATOR")

    def _log_class_contributions(self, class_var_details, between_class_var):
        """记录类间方差主要贡献类别"""
        if not class_var_details:
            return
            
        sorted_classes = sorted(class_var_details.items(), 
                              key=lambda x: x[1]['var_contribution'], reverse=True)
        log_utils.info(f"  ├─ 类间方差主要贡献类别 (Top 5):", tag="METRICS_CALCULATOR")
        for i, (label, details) in enumerate(sorted_classes[:5]):
            contribution_pct = (details['var_contribution'] / max(between_class_var, 1e-6)) * 100
            log_utils.info(f"  │  ├─ 类别{label}: 贡献{contribution_pct:.1f}% "
                          f"(样本数:{details['size']}, 距全局中心:{details['center_distance_to_global']:.4f})", tag="METRICS_CALCULATOR")

    def _log_within_class_analysis(self, class_within_var_details):
        """记录类内方差分析"""
        if not class_within_var_details:
            return
            
        sorted_within = sorted(class_within_var_details.items(), 
                             key=lambda x: x[1]['within_var'], reverse=True)
        log_utils.info(f"  └─ 类内方差最大类别 (Top 5):", tag="METRICS_CALCULATOR")
        for i, (label, details) in enumerate(sorted_within[:5]):
            log_utils.info(f"     ├─ 类别{label}: 类内方差{details['within_var']:.6f} "
                          f"(平均距中心:{details['avg_distance_to_center']:.4f})", tag="METRICS_CALCULATOR")

    def _add_structure_metrics(self, metrics_dict, emb_np):
        """添加特征空间结构指标（PCA重建误差、协方差条件数）"""
        try:
            # 确保输入数据是二维的
            if emb_np.ndim > 2:
                log_utils.warning(f"输入数据维度过高 ({emb_np.ndim}D)，转换为2D", tag="METRICS_CALCULATOR")
                emb_np = emb_np.reshape(emb_np.shape[0], -1)
            elif emb_np.ndim < 2:
                log_utils.warning(f"输入数据维度过低 ({emb_np.ndim}D)，跳过结构指标计算", tag="METRICS_CALCULATOR")
                return
                
            # 计算合适的PCA组件数
            n_samples, n_features = emb_np.shape
            max_components = min(n_samples - 1, n_features, 64)
            
            if max_components > 0 and n_samples > 1:
                self._compute_pca_reconstruction_error(metrics_dict, emb_np, max_components)
                self._compute_covariance_condition_number(metrics_dict, emb_np)
        except Exception as e:
            log_utils.error(f"计算特征空间结构诊断指标时出错: {e}", tag="METRICS_CALCULATOR")

    def _compute_pca_reconstruction_error(self, metrics_dict, emb_np, n_components):
        """计算PCA重建误差"""
        try:
            pca = PCA(n_components=n_components)
            pca.fit(emb_np)
            reconstructed = pca.inverse_transform(pca.transform(emb_np))
            reconstruction_error = np.mean(np.sum((emb_np - reconstructed) ** 2, axis=1))
            metrics_dict['pca_reconstruction_error'] = reconstruction_error
        except Exception as e:
            log_utils.error(f"PCA重建误差计算失败: {e}", tag="METRICS_CALCULATOR")

    def _compute_covariance_condition_number(self, metrics_dict, emb_np):
        """计算特征协方差矩阵条件数"""
        cov_matrix = np.cov(emb_np.T)
        eigenvalues = np.linalg.eigvalsh(cov_matrix)
        positive_eigenvalues = eigenvalues[eigenvalues > 1e-10]
        if len(positive_eigenvalues) > 0:
            condition_number = np.max(positive_eigenvalues) / np.min(positive_eigenvalues)
            metrics_dict['covariance_condition'] = condition_number

    def get_avg_distances(self):
        """获取平均类内和类间距离"""
        intra_avg = MetricsUtils.safe_mean([np.mean(dists) for dists in self.intra_dists.values()]) if self.intra_dists else 0
        inter_avg = MetricsUtils.safe_mean([np.mean(dists) for dists in self.inter_dists.values()]) if self.inter_dists else 0
        return intra_avg, inter_avg

    def get_latest_separation_ratio(self):
        """获取最新的特征分离比例"""
        return self.separation_ratios[-1] if MetricsUtils.is_valid_data(self.separation_ratios) else 0
    
    def set_current_epoch(self, epoch: int):
        """设置当前epoch（用于确定性采样）"""
        self.current_epoch = epoch

    def update_boundary_stats(self, boundary_samples):
        """更新边界样本统计 (重构后)"""
        if hasattr(boundary_samples, 'to_dict'):
            return self._update_from_boundary_result(boundary_samples)
        
        if not MetricsUtils.is_valid_data(boundary_samples):
            return
            
        return self._update_from_boundary_list(boundary_samples)

    def _update_from_boundary_result(self, boundary_samples):
        """从BoundaryResult对象更新统计"""
        stats = boundary_samples.to_dict()
        stats['epoch'] = len(self.total_losses)
        self.boundary_stats.append(stats)
        return stats

    def _update_from_boundary_list(self, boundary_samples):
        """从字典列表更新统计"""
        self.boundary_samples.extend(boundary_samples)
        
        stats = {
            'epoch': len(self.total_losses),
            'boundary_count': len(boundary_samples),
            'avg_similarity': MetricsUtils.safe_mean([s.get('similarity', 0) for s in boundary_samples]),
            'class_pairs': len(set([(s.get('anchor_label'), s.get('neg_label')) for s in boundary_samples]))
        }
        
        self.boundary_stats.append(stats)
        return stats

    def _log_feature_quality_analysis(self, quality_metrics, intra_avg, inter_avg, separation_ratio, num_classes):
        """记录详细的特征质量分析日志"""
        epoch = quality_metrics.get('epoch', 0)
        
        log_utils.info(f"🎯 特征质量综合分析报告 - Epoch {epoch}:", tag="METRICS_CALCULATOR")
        self._log_basic_distance_metrics(intra_avg, inter_avg, separation_ratio)
        self._log_geometric_metrics(quality_metrics)
        self._log_discriminative_metrics(quality_metrics)
        self._log_structure_metrics(quality_metrics)
        self._log_comprehensive_quality_assessment(quality_metrics, separation_ratio, num_classes)

    def _log_basic_distance_metrics(self, intra_avg, inter_avg, separation_ratio):
        """记录基础距离指标"""
        log_utils.info(f"  ┌─ 基础距离指标:", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 平均类内距离: {intra_avg:.6f}", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 平均类间距离: {inter_avg:.6f}", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 距离比 (Inter/Intra): {inter_avg/(intra_avg + 1e-8):.6f}", tag="METRICS_CALCULATOR")
        log_utils.info(f"  ├─ 特征分离比: {separation_ratio:.6f}", tag="METRICS_CALCULATOR")
        
        self._log_separation_quality_rating(separation_ratio)

    def _log_separation_quality_rating(self, separation_ratio):
        """记录分离质量评级"""
        if separation_ratio > 0.8:
            sep_quality, sep_icon = "优秀", "🟢"
        elif separation_ratio > 0.6:
            sep_quality, sep_icon = "良好", "🟡"
        elif separation_ratio > 0.4:
            sep_quality, sep_icon = "基线", "🟠"
        else:
            sep_quality, sep_icon = "需要改进", "🔴"
        
        log_utils.info(f"  ├─ 分离质量评级: {sep_icon} {sep_quality}", tag="METRICS_CALCULATOR")

    def _log_geometric_metrics(self, quality_metrics):
        """记录几何分布指标"""
        if 'center_dist_mean' in quality_metrics:
            log_utils.info(f"  ├─ 几何分布指标:", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 类中心平均距离: {quality_metrics['center_dist_mean']:.6f}", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 类中心距离标准差: {quality_metrics['center_dist_std']:.6f}", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 最小类中心距离: {quality_metrics.get('center_dist_min', 'N/A')}", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  └─ 分布紧凑度: {quality_metrics.get('distribution_compactness', 0):.6f}", tag="METRICS_CALCULATOR")
        else:
            # 🔧 修复：如果几何分布指标计算失败，明确显示
            log_utils.info(f"  ├─ 几何分布指标:", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 类中心平均距离: 计算失败", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 类中心距离标准差: 计算失败", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ 最小类中心距离: 计算失败", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  └─ 分布紧凑度: 计算失败", tag="METRICS_CALCULATOR")

    def _log_discriminative_metrics(self, quality_metrics):
        """记录判别能力指标"""
        if 'fisher_score' in quality_metrics:
            fisher_score = quality_metrics['fisher_score']
            discriminative_power = quality_metrics.get('discriminative_power', 0)
            log_utils.info(f"  ├─ 判别能力指标:", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ Fisher分数: {fisher_score:.6f}", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  └─ 判别能力指数: {discriminative_power:.6f}", tag="METRICS_CALCULATOR")

    def _log_structure_metrics(self, quality_metrics):
        """记录特征空间结构指标"""
        if 'pca_reconstruction_error' in quality_metrics:
            pca_error = quality_metrics['pca_reconstruction_error']
            log_utils.info(f"  ├─ 特征空间结构:", tag="METRICS_CALCULATOR")
            log_utils.info(f"  │  ├─ PCA重建误差: {pca_error:.6f}", tag="METRICS_CALCULATOR")
            
            if 'covariance_condition' in quality_metrics:
                cond_num = quality_metrics['covariance_condition']
                log_utils.info(f"  │  └─ 协方差条件数: {cond_num:.2f}", tag="METRICS_CALCULATOR")
                self._log_condition_number_health(cond_num)

    def _log_condition_number_health(self, cond_num):
        """记录条件数健康度评估 - 使用统一健康度评估模块"""
        from utils.feature_space_health import FeatureSpaceHealthEvaluator, HealthThresholds
        
        # 创建基于条件数的健康度评估
        thresholds = HealthThresholds()
        evaluator = FeatureSpaceHealthEvaluator(thresholds)
        
        # 将条件数映射到健康度等级
        if cond_num < 100:
            level_text, level_icon = "🟢 健康", "🟢"
            desc = f"条件数健康 ({cond_num:.2f})"
        elif cond_num < 1000:
            level_text, level_icon = "🟡 良好", "🟡"
            desc = f"条件数良好 ({cond_num:.2f})"
        elif cond_num < 10000:
            level_text, level_icon = "🟠 警告", "🟠"
            desc = f"条件数警告 ({cond_num:.2f})"
        else:
            level_text, level_icon = "🔴 特征塌缩风险", "🔴"
            desc = f"特征塌缩风险 ({cond_num:.2f})"
        
        log_utils.info(f"  │     └─ 特征空间健康度: {level_text}", tag="METRICS_CALCULATOR")

    def _log_comprehensive_quality_assessment(self, quality_metrics, separation_ratio, num_classes):
        """记录综合质量评估"""
        quality_score = self._calculate_comprehensive_quality_score(quality_metrics, separation_ratio)
        
        if quality_score > 0.8:
            overall_quality, overall_icon = "优秀", "🟢"
        elif quality_score > 0.6:
            overall_quality, overall_icon = "良好", "🟡"
        elif quality_score > 0.4:
            overall_quality, overall_icon = "基线", "🟠"
        else:
            overall_quality, overall_icon = "需要改进", "🔴"
        
        log_utils.info(f"  └─ 综合质量评估: {overall_icon} {overall_quality} (得分: {quality_score:.3f})", tag="METRICS_CALCULATOR")
        log_utils.info(f"     └─ 分析类别数: {num_classes}", tag="METRICS_CALCULATOR")

    def _calculate_comprehensive_quality_score(self, quality_metrics, separation_ratio):
        """计算综合质量得分"""
        # 基础分离比权重 (40%)
        sep_score = min(1.0, separation_ratio / 0.8) * 0.4
        
        # Fisher分数权重 (30%) - 使用动态归一化
        fisher_score = quality_metrics.get('fisher_score', 0)
        fisher_normalized = min(1.0, fisher_score / max(10.0, fisher_score * 0.5)) * 0.3
        
        # 几何分布权重 (20%)
        compactness = quality_metrics.get('distribution_compactness', 0)
        compactness_normalized = min(1.0, compactness / 5.0) * 0.2
        
        # 特征空间健康度权重 (10%)
        cond_num = quality_metrics.get('covariance_condition', 1000)
        health_score = max(0.0, 1.0 - np.log10(max(1, cond_num)) / 4.0) * 0.1
        
        return sep_score + fisher_normalized + compactness_normalized + health_score

    # ==================== 公共接口方法 ====================
    
    def calculate_comprehensive_quality_score(self, quality_metrics, separation_ratio):
        """计算综合质量得分 - 公共方法"""
        return self._calculate_comprehensive_quality_score(quality_metrics, separation_ratio)
    
    # @staticmethod
    # def calculate_quality_score_from_metrics(quality_metrics: Dict[str, float], separation_ratio: float) -> float:
    #     """静态方法：从质量指标计算综合得分 - 供ValidationEngine调用"""
    #     # 基础分离比权重 (40%)
    #     sep_score = min(1.0, separation_ratio / 0.8) * 0.4
        
    #     # Fisher分数权重 (30%) - 使用动态归一化
    #     fisher_score = quality_metrics.get('fisher_score', 0)
    #     fisher_normalized = min(1.0, fisher_score / max(10.0, fisher_score * 0.5)) * 0.3
        
    #     # 几何分布权重 (20%) - 使用距离比例作为近似
    #     avg_inter_dist = quality_metrics.get('avg_inter_distance', 0.0)
    #     avg_intra_dist = quality_metrics.get('avg_intra_distance', 1.0)
    #     distance_ratio = avg_inter_dist / max(0.001, avg_intra_dist)
    #     compactness_normalized = min(1.0, distance_ratio / 5.0) * 0.2
        
    #     # 特征空间健康度权重 (10%) - 使用标准差作为近似
    #     intra_std = quality_metrics.get('intra_std', 0.0)
    #     health_score = max(0.0, 1.0 - intra_std / max(0.5, avg_inter_dist)) * 0.1
        
    #     return sep_score + fisher_normalized + compactness_normalized + health_score
    
    # @staticmethod
    # def calculate_quality_score_from_distances(separation_ratio: float, inter_distance: float, 
    #                                          intra_distance: float, fisher_score: float = 0.0) -> float:
    #     """静态方法：从距离指标计算综合得分 - 供EarlyStopping调用"""
    #     # 基础分离比权重 (40%)
    #     sep_score = min(1.0, separation_ratio / 0.8) * 0.4
        
    #     # Fisher分数权重 (30%) - 使用动态归一化
    #     fisher_normalized = min(1.0, fisher_score / max(10.0, fisher_score * 0.5)) * 0.3
        
    #     # 距离比例权重 (20%) - 使用动态归一化
    #     distance_ratio = inter_distance / max(0.001, intra_distance)
    #     distance_normalized = min(1.0, distance_ratio / max(5.0, distance_ratio * 1.2)) * 0.2
        
    #     # 距离健康度权重 (10%)
    #     health_score = max(0.0, 1.0 - intra_distance / max(0.5, inter_distance)) * 0.1
        
    #     return sep_score + fisher_normalized + distance_normalized + health_score
