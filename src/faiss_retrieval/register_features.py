import os
import logging
import argparse
import numpy as np
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from collections import defaultdict
from PIL import Image
import tempfile

from config import BEST_MODEL_PATH

# 导入YOLO
try:
    from ultralytics import YOLO
except ImportError:
    logging.warning("未安装ultralytics，请使用 pip install ultralytics 安装")

# 修复导入路径
from faiss_retrieval.model_loader import FeatureExtractorWrapper as FeatureExtractor
from faiss_retrieval.faiss_indexer import FaissIndexer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# YOLO模型路径
YOLO_MODEL_PATH = 'src/models/yolo.pt'

def detect_cat_head(img_path, yolo_model):
    """
    使用YOLO检测猫头位置
    
    Args:
        img_path: 图像路径
        yolo_model: YOLO模型
        
    Returns:
        tuple: (是否检测到猫头, 裁剪区域 [x, y, w, h])
    """
    try:
        results = yolo_model(img_path)
        boxes = results[0].boxes
        
        if boxes is None or len(boxes) == 0:
            logging.warning(f"未检测到猫头: {img_path}")
            return False, None
        
        # 默认取最大面积的框作为猫头
        best_box = max(boxes.xyxy, key=lambda box: (box[2] - box[0]) * (box[3] - box[1]))
        x1, y1, x2, y2 = map(int, best_box.tolist())
        
        # 将xyxy格式转换为xywh格式
        bbox = [x1, y1, x2 - x1, y2 - y1]
        return True, bbox
    except Exception as e:
        logging.error(f"猫头检测失败 {img_path}: {e}")
        return False, None

def crop_cat_head(img_path, bbox):
    """
    裁剪猫头区域
    
    Args:
        img_path: 图像路径
        bbox: 边界框 [x, y, w, h]
        
    Returns:
        tuple: (是否成功裁剪, 裁剪后的图像路径或原图路径)
    """
    try:
        image = Image.open(img_path).convert('RGB')
        x, y, w, h = bbox
        
        # 防止裁剪区域越界
        x1, y1 = max(0, x), max(0, y)
        x2, y2 = min(image.width, x1 + w), min(image.height, y1 + h)
        
        if x2 > x1 and y2 > y1:  # 确保有效区域
            cropped_image = image.crop((x1, y1, x2, y2))
            
            # 使用临时文件保存裁剪后的图像
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
                cropped_path = tmp.name
                cropped_image.save(cropped_path)
                return True, cropped_path
        
        return False, img_path
    except Exception as e:
        logging.error(f"裁剪猫头失败 {img_path}: {e}")
        return False, img_path

def warmup_yolo_model(yolo_model):
    """
    预热YOLO模型，避免首次检测失败
    
    Args:
        yolo_model: 加载好的YOLO模型
    """
    if yolo_model is None:
        return
        
    try:
        # 创建一个简单的测试图像
        test_img = np.ones((640, 640, 3), dtype=np.uint8) * 128  # 灰色图像
        
        # 保存为临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
            temp_path = tmp.name
            Image.fromarray(test_img).save(temp_path)
        
        # 进行一次推理预热
        logging.info("正在预热YOLO模型...")
        _ = yolo_model(temp_path)
        logging.info("YOLO模型预热完成")
        
        # 清理临时文件
        try:
            os.unlink(temp_path)
        except:
            pass
    except Exception as e:
        logging.warning(f"YOLO模型预热失败: {e}")

def process_single_image(extractor, img_path, yolo_model=None):
    """
    处理单张图像并提取特征，使用YOLO检测猫头
    
    Args:
        extractor: 特征提取器实例
        img_path: 图像路径
        yolo_model: YOLO模型，如果为None则不进行猫头检测
        
    Returns:
        tuple: (特征向量, 元数据字典)
    """
    try:
        # 确保提取器处于eval模式
        if hasattr(extractor.model, 'eval'):
            extractor.model.eval()
            
        # 准备元数据
        parts = Path(img_path).parts
        if len(parts) >= 2:
            class_id = parts[-2]  # 倒数第二个部分通常是类别ID
        else:
            class_id = "unknown"
        
        # 使用YOLO检测猫头
        cropped_path = img_path
        temp_file_created = False
        
        if yolo_model is not None:
            detected, bbox = detect_cat_head(img_path, yolo_model)
            if detected:
                temp_file_created, cropped_path = crop_cat_head(img_path, bbox)
                logging.debug(f"猫头裁剪成功: {img_path}")
        
        # 提取特征向量
        feature = extractor.extract(cropped_path)
        
        # 如果创建了临时文件，在提取特征后删除
        if temp_file_created:
            try:
                os.unlink(cropped_path)
            except:
                pass
            
        metadata = {
            "path": str(img_path),
            "file_name": os.path.basename(img_path),
            "class_id": class_id
        }
        
        return feature, metadata
    except Exception as e:
        logging.error(f"处理图像失败 {img_path}: {e}")
        return None, None

def register_limited_features(img_folder, output_index="index/faiss_cat_index", 
                             model_path=None, device='cpu', max_workers=8, 
                             embedding_dim=512, use_yolo=True):
    """
    为每个宠物分类只注册固定的3张图片
    
    Args:
        img_folder: 图像文件夹路径（例如：output/data/processed_train）
        output_index: 输出索引路径
        model_path: 模型路径
        device: 计算设备
        max_workers: 并行处理的最大线程数
        embedding_dim: 特征向量维度
        use_yolo: 是否使用YOLO检测猫头
    """
    logging.info(f"开始处理图像文件夹: {img_folder}，每个宠物类仅注册3张固定图片")
    
    # 初始化特征提取器
    extractor = FeatureExtractor(model_path=model_path, device=device, embedding_dim=embedding_dim)
    logging.info(f"特征提取器初始化完成")
    
    # 加载YOLO模型
    yolo_model = None
    if use_yolo:
        try:
            yolo_model = YOLO(YOLO_MODEL_PATH)
            logging.info(f"YOLO模型加载完成: {YOLO_MODEL_PATH}")
            
            # 模型预热，防止首次检测失败
            warmup_yolo_model(yolo_model)
        except Exception as e:
            logging.error(f"YOLO模型加载失败: {e}")
            logging.warning("将继续使用原始图像进行特征提取")
    
    # 创建FAISS索引器
    indexer = FaissIndexer(dim=embedding_dim, index_path=output_index)
    
    # 收集所有类别目录
    class_dirs = []
    try:
        class_dirs = [d for d in os.listdir(img_folder) if os.path.isdir(os.path.join(img_folder, d))]
        class_dirs.sort()  # 排序确保处理顺序一致
    except Exception as e:
        logging.error(f"读取类别目录失败: {e}")
        return 0
    
    logging.info(f"找到 {len(class_dirs)} 个宠物类别")
    
    # 用于存储所有提取的特征和元数据
    all_features = []
    all_metadata = []
    skipped_classes = []
    processed_classes = 0
    
    # 处理每个类别
    for class_id in tqdm(class_dirs, desc="处理宠物类别"):
        class_path = os.path.join(img_folder, class_id)
        
        # 根据类别ID动态构建图片名称
        target_image_patterns = [
            f"{class_id}_clean01.jpg",         # 基础图片
            f"{class_id}_clean06.jpg",    # 第一张增强图片
            f"{class_id}_clean11.jpg",    # 第十张增强图片
            f"{class_id}_clean16.jpg",    # 第十五张增强图片
            f"{class_id}_clean21.jpg",    # 第二十张增强图片
            f"{class_id}_clean26.jpg"    # 第二十五张增强图片
        ]
        
        # 找出该类别中符合模式的3张图片
        class_image_paths = []
        all_images = os.listdir(class_path)
        
        for pattern in target_image_patterns:
            # 尝试精确匹配
            if pattern in all_images:
                class_image_paths.append(os.path.join(class_path, pattern))
                continue
                
            # 获取文件扩展名
            pattern_base, pattern_ext = os.path.splitext(pattern)
            
            # 尝试使用不同的扩展名
            found = False
            for ext in ['.png', '.jpg', '.jpeg']:
                alt_pattern = pattern_base + ext
                if alt_pattern in all_images:
                    class_image_paths.append(os.path.join(class_path, alt_pattern))
                    found = True
                    break
            
            if found:
                continue
                
            # 如果仍找不到，尝试更灵活的匹配
            if "cat." + class_id in pattern and not "_aug" in pattern:
                # 基础图像：查找任意包含cat.{class_id}但不是aug的图像
                for img in all_images:
                    if f"cat.{class_id}" in img and not "_aug" in img:
                        class_image_paths.append(os.path.join(class_path, img))
                        found = True
                        break
            elif f"cat.{class_id}_aug1" in pattern:
                # 增强图像1：查找任意aug1图像
                for img in all_images:
                    if f"_aug1." in img:
                        class_image_paths.append(os.path.join(class_path, img))
                        found = True
                        break
            elif f"cat.{class_id}_aug10" in pattern:
                # 增强图像10：查找任意aug10图像，如果没有则找其他aug图像
                for img in all_images:
                    if f"_aug10." in img:
                        class_image_paths.append(os.path.join(class_path, img))
                        found = True
                        break
                
                if not found:
                    # 找不到aug10，找任意其他aug图像（除了aug1）
                    for img in all_images:
                        if "_aug" in img and not "_aug1." in img:
                            class_image_paths.append(os.path.join(class_path, img))
                            found = True
                            break
        
        # 如果没找到足够的图片，跳过该类别
        if len(class_image_paths) < 3:
            logging.warning(f"类别 {class_id} 没有足够的目标图片，已找到 {len(class_image_paths)} 张")
            skipped_classes.append(class_id)
            continue
        
        # 限制为3张图片
        class_image_paths = class_image_paths[:3]
        
        # 处理该类的图片
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_single_image, extractor, path, yolo_model): path for path in class_image_paths}
            
            class_features = []
            class_metadata = []
            for future in as_completed(futures):
                path = futures[future]
                try:
                    feature, metadata = future.result()
                    if feature is not None and metadata is not None:
                        class_features.append(feature)
                        class_metadata.append(metadata)
                except Exception as e:
                    logging.error(f"处理 {path} 失败: {e}")
        
        # 如果成功处理了图片，添加到总列表
        if class_features:
            all_features.extend(class_features)
            all_metadata.extend(class_metadata)
            processed_classes += 1
    
    # 报告处理统计
    logging.info(f"成功处理 {processed_classes}/{len(class_dirs)} 个类别，共 {len(all_features)} 张图像")
    if skipped_classes:
        logging.warning(f"跳过了 {len(skipped_classes)} 个类别: {', '.join(skipped_classes[:10])}{'...' if len(skipped_classes) > 10 else ''}")
    
    # 如果没有提取到任何特征，返回
    if not all_features:
        logging.error("没有提取到任何特征，无法创建索引")
        return 0
    
    # 构建索引
    logging.info(f"构建FAISS索引，共 {len(all_features)} 个特征向量")
    indexer.build_index(all_features, all_metadata)
    
    # 保存索引
    logging.info(f"保存索引到 {output_index}")
    indexer.save()
    
    logging.info(f"✅ 索引构建完成，共 {len(all_features)} 张图像，保存为 {output_index}")
    return len(all_features)

def register_all_features(img_folder, output_index="index/faiss_cat_index", 
                         model_path=None, device='cpu', max_workers=8, 
                         embedding_dim=512, batch_size=100, use_yolo=True):
    """
    注册文件夹中所有猫图像的特征向量
    
    Args:
        img_folder: 图像文件夹路径
        output_index: 输出索引路径
        model_path: 模型路径
        device: 计算设备
        max_workers: 并行处理的最大线程数
        embedding_dim: 特征向量维度
        batch_size: 批处理大小，避免内存溢出
        use_yolo: 是否使用YOLO检测猫头
    """
    logging.info(f"开始处理图像文件夹: {img_folder}")
    
    # 初始化特征提取器
    extractor = FeatureExtractor(model_path=model_path, device=device, embedding_dim=embedding_dim)
    logging.info(f"特征提取器初始化完成")
    
    # 加载YOLO模型
    yolo_model = None
    if use_yolo:
        try:
            yolo_model = YOLO(YOLO_MODEL_PATH)
            logging.info(f"YOLO模型加载完成: {YOLO_MODEL_PATH}")
            
            # 模型预热，防止首次检测失败
            warmup_yolo_model(yolo_model)
        except Exception as e:
            logging.error(f"YOLO模型加载失败: {e}")
            logging.warning("将继续使用原始图像进行特征提取")
    
    # 创建FAISS索引器
    indexer = FaissIndexer(dim=embedding_dim, index_path=output_index)
    
    # 收集所有图像路径
    image_paths = []
    for root, _, files in os.walk(img_folder):
        for file in files:
            if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                image_paths.append(os.path.join(root, file))
    
    logging.info(f"找到 {len(image_paths)} 张图像")
    
    # 分批处理图像
    all_features = []
    all_metadata = []
    total_batches = (len(image_paths) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, len(image_paths))
        batch_paths = image_paths[start_idx:end_idx]
        
        logging.info(f"处理批次 {batch_idx + 1}/{total_batches}, {len(batch_paths)} 张图像")
        
        # 使用线程池并行处理
        batch_features = []
        batch_metadata = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(process_single_image, extractor, path, yolo_model): path for path in batch_paths}
            
            for future in tqdm(as_completed(futures), total=len(futures), desc=f"批次 {batch_idx + 1}"):
                path = futures[future]
                try:
                    feature, metadata = future.result()
                    if feature is not None and metadata is not None:
                        batch_features.append(feature)
                        batch_metadata.append(metadata)
                except Exception as e:
                    logging.error(f"处理 {path} 失败: {e}")
        
        # 添加到总列表
        all_features.extend(batch_features)
        all_metadata.extend(batch_metadata)
    
    # 构建索引
    logging.info(f"构建FAISS索引，共 {len(all_features)} 个特征向量")
    
    # 特征向量堆叠
    all_features = np.vstack(all_features).astype(np.float32)
    
    # 检查特征向量范数，确认是否已经归一化
    norms = np.linalg.norm(all_features, axis=1)
    already_normalized = np.allclose(norms, 1.0, atol=1e-5)
    
    if not already_normalized:
        logging.info("对特征向量进行L2归一化")
        # 仅在未归一化时进行归一化
        all_features = all_features / (np.linalg.norm(all_features, axis=1, keepdims=True) + 1e-8)
    else:
        logging.info("特征向量已归一化，跳过二次归一化")
    
    indexer.build_index(all_features, all_metadata)
    
    # 保存索引
    logging.info(f"保存索引到 {output_index}")
    indexer.save()
    
    logging.info(f"✅ 索引构建完成，共 {len(all_features)} 张图像，保存为 {output_index}")
    return len(all_features)

def register_incremental(img_folder, index_path="index/faiss_cat_index", 
                        model_path=None, device='cpu', max_workers=8, 
                        embedding_dim=512, limited=False, images_per_class=3, use_yolo=True):
    """
    增量注册新图像到现有索引
    
    Args:
        img_folder: 新图像文件夹路径
        index_path: 现有索引路径
        model_path: 模型路径
        device: 计算设备
        max_workers: 并行处理的最大线程数
        embedding_dim: 特征向量维度
        limited: 是否限制每个类别的图片数量
        images_per_class: 每个类别限制的图片数量
        use_yolo: 是否使用YOLO检测猫头
    """
    if limited:
        logging.info(f"增量注册新图像(限定每类 {images_per_class} 张): {img_folder}")
    else:
        logging.info(f"增量注册新图像: {img_folder}")
    
    # 初始化特征提取器
    extractor = FeatureExtractor(model_path=model_path, device=device, embedding_dim=embedding_dim)
    
    # 加载YOLO模型
    yolo_model = None
    if use_yolo:
        try:
            yolo_model = YOLO(YOLO_MODEL_PATH)
            logging.info(f"YOLO模型加载完成: {YOLO_MODEL_PATH}")
            
            # 模型预热，防止首次检测失败
            warmup_yolo_model(yolo_model)
        except Exception as e:
            logging.error(f"YOLO模型加载失败: {e}")
            logging.warning("将继续使用原始图像进行特征提取")
    
    # 加载现有索引
    indexer = FaissIndexer(dim=embedding_dim, index_path=index_path)
    indexer.load()
    logging.info(f"加载现有索引，包含 {indexer.get_total_vectors()} 个向量")
    
    # 获取现有类别
    existing_classes = set()
    if limited:
        for _, meta in indexer.id_map.items():
            class_id = meta.get('class_id', 'unknown')
            existing_classes.add(class_id)
        logging.info(f"现有索引包含 {len(existing_classes)} 个类别")
    
    # 收集新图像
    if limited:
        # 按类别整理图片
        class_to_images = defaultdict(list)
        for root, _, files in os.walk(img_folder):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    img_path = os.path.join(root, file)
                    # 从路径中提取类别ID
                    parts = Path(img_path).parts
                    if len(parts) >= 2:
                        class_id = parts[-2]  # 倒数第二个部分通常是类别ID
                        class_to_images[class_id].append(img_path)
        
        # 选择图片
        image_paths = []
        new_classes = 0
        for class_id, paths in class_to_images.items():
            if class_id in existing_classes:
                logging.info(f"类别 {class_id} 已存在，跳过")
                continue
            
            new_classes += 1
            # 限制每个类别的图片数量
            selected_paths = paths[:images_per_class]
            image_paths.extend(selected_paths)
            
            if len(selected_paths) < images_per_class:
                logging.warning(f"类别 {class_id} 图片数量不足 ({len(selected_paths)}/{images_per_class})")
                
        logging.info(f"将处理 {new_classes} 个新类别，共 {len(image_paths)} 张图片")
    else:
        # 收集所有图像
        image_paths = []
        for root, _, files in os.walk(img_folder):
            for file in files:
                if file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    image_paths.append(os.path.join(root, file))
        
        logging.info(f"找到 {len(image_paths)} 张新图像")
    
    if not image_paths:
        logging.warning("没有需要处理的新图片")
        return 0
    
    # 提取特征和元数据
    features = []
    metadata = []
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(process_single_image, extractor, path, yolo_model): path for path in image_paths}
        
        for future in tqdm(as_completed(futures), total=len(futures), desc="处理新图像"):
            path = futures[future]
            try:
                feature, meta = future.result()
                if feature is not None and meta is not None:
                    features.append(feature)
                    metadata.append(meta)
            except Exception as e:
                logging.error(f"处理 {path} 失败: {e}")
    
    # 添加到索引
    if features:
        features = np.vstack(features).astype(np.float32)
        
        # 检查特征向量范数，确认是否已经归一化
        norms = np.linalg.norm(features, axis=1)
        already_normalized = np.allclose(norms, 1.0, atol=1e-5)
        
        if not already_normalized:
            logging.info("对特征向量进行L2归一化")
            # 仅在未归一化时进行归一化
            features = features / (np.linalg.norm(features, axis=1, keepdims=True) + 1e-8)
        else:
            logging.info("特征向量已归一化，跳过二次归一化")
            
        indexer.add_vectors(features, metadata)
        indexer.save()
        logging.info(f"成功添加 {len(features)} 个新向量，当前总数: {indexer.get_total_vectors()}")
    else:
        logging.warning("没有新向量添加到索引")
    
    return len(features)

def delete_by_class_id(index_path, class_id):
    """
    根据类别ID删除特定宠物的所有特征向量
    
    Args:
        index_path: 索引路径
        class_id: 要删除的宠物类别ID
        
    Returns:
        bool: 删除是否成功
    """
    logging.info(f"开始删除类别 {class_id} 的特征向量...")
    
    # 加载现有索引
    try:
        indexer = FaissIndexer(index_path=index_path)
        indexer.load()
        logging.info(f"加载索引成功，当前共有 {indexer.get_total_vectors()} 个向量")
        
        # 获取类别统计信息
        class_counts = indexer.get_class_counts()
        if class_id not in class_counts:
            logging.error(f"索引中不存在类别 {class_id}")
            return False
        
        vector_count = class_counts[class_id]
        logging.info(f"找到类别 {class_id} 的 {vector_count} 个特征向量")
        
        # 确认删除
        removed_count = indexer.remove_by_class_id(class_id)
        
        # 保存更新后的索引
        indexer.save()
        logging.info(f"✅ 成功删除类别 {class_id} 的 {removed_count} 个特征向量，索引已更新")
        
        # 打印当前索引状态
        class_counts = indexer.get_class_counts()
        logging.info(f"索引现包含 {len(class_counts)} 个类别，共 {indexer.get_total_vectors()} 个向量")
        
        return True
    except Exception as e:
        logging.error(f"删除类别 {class_id} 的特征向量失败: {e}")
        return False

def list_classes(index_path):
    """
    列出索引中的所有宠物类别及其特征向量数量
    
    Args:
        index_path: 索引路径
    """
    try:
        indexer = FaissIndexer(index_path=index_path)
        indexer.load()
        
        total_vectors = indexer.get_total_vectors()
        class_counts = indexer.get_class_counts()
        
        logging.info(f"索引中共有 {len(class_counts)} 个宠物类别，{total_vectors} 个特征向量")
        
        # 按向量数量降序排列
        sorted_classes = sorted(class_counts.items(), key=lambda x: x[1], reverse=True)
        
        # 打印类别信息
        print("\n类别ID\t\t特征向量数量")
        print("-" * 30)
        for class_id, count in sorted_classes:
            print(f"{class_id}\t\t{count}")
    except Exception as e:
        logging.error(f"列出索引类别失败: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="猫图像特征注册与管理工具")
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 注册命令
    register_parser = subparsers.add_parser("register", help="注册特征")
    register_parser.add_argument("--img_folder", required=True, help="图像文件夹路径")
    register_parser.add_argument("--output_index", default="index/faiss_cat_index", help="输出索引路径")
    register_parser.add_argument("--model_path", default=BEST_MODEL_PATH, help="模型路径")
    register_parser.add_argument("--device", default="cpu", choices=["cpu", "cuda", "mps"], help="计算设备")
    register_parser.add_argument("--embedding_dim", type=int, default=512, help="特征向量维度")
    register_parser.add_argument("--incremental", action="store_true", help="是否增量注册")
    register_parser.add_argument("--limited", action="store_true", help="是否每个类别只注册3张固定图片")
    register_parser.add_argument("--max_workers", type=int, default=8, help="并行处理的最大线程数")
    register_parser.add_argument("--batch_size", type=int, default=100, help="批处理大小")
    register_parser.add_argument("--images_per_class", type=int, default=3, help="每个类别保留的图片数量")
    register_parser.add_argument("--use_yolo", action="store_true", help="是否使用YOLO检测猫头")
    
    # 删除命令
    delete_parser = subparsers.add_parser("delete", help="删除特定类别的特征向量")
    delete_parser.add_argument("--index_path", required=True, help="索引路径")
    delete_parser.add_argument("--class_id", required=True, help="要删除的宠物类别ID")
    
    # 列出类别命令
    list_parser = subparsers.add_parser("list", help="列出索引中的所有宠物类别")
    list_parser.add_argument("--index_path", required=True, help="索引路径")
    
    args = parser.parse_args()
    
    # 根据命令执行相应功能
    if args.command == "register":
        # 创建输出目录
        os.makedirs(os.path.dirname(args.output_index), exist_ok=True)
        
        # 检查参数组合
        if args.incremental and args.limited:
            # 限定数量的增量注册
            logging.info("执行限定数量的增量注册")
            register_incremental(
                args.img_folder, 
                index_path=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                limited=True,
                images_per_class=args.images_per_class,
                use_yolo=args.use_yolo
            )
        elif args.limited:
            # 有限注册
            register_limited_features(
                args.img_folder,
                output_index=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                use_yolo=args.use_yolo
            )
        elif args.incremental:
            # 普通增量注册
            register_incremental(
                args.img_folder, 
                index_path=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                limited=False,
                use_yolo=args.use_yolo
            )
        else:
            # 全量注册
            register_all_features(
                args.img_folder,
                output_index=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                batch_size=args.batch_size,
                use_yolo=args.use_yolo
            )
    elif args.command == "delete":
        # 删除特定类别的特征向量
        delete_by_class_id(
            index_path=args.index_path,
            class_id=args.class_id
        )
    elif args.command == "list":
        # 列出索引中的所有宠物类别
        list_classes(args.index_path)
    else:
        parser.print_help() 