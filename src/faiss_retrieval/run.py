#!/usr/bin/env python
"""
FAISS猫咪个体检索系统主入口
提供四种主要功能：
1. 注册特征向量
2. 增量注册特征向量
3. 限定数量增量注册特征向量
4. 启动API服务
"""

import argparse
import os
import logging
from pathlib import Path
import sys

# 添加项目根目录和src目录到导入路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
src_dir = os.path.join(project_root, "src")

if project_root not in sys.path:
    sys.path.append(project_root)
if src_dir not in sys.path:
    sys.path.append(src_dir)

from config import BEST_MODEL_PATH


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def parse_args():
    parser = argparse.ArgumentParser(description="FAISS猫咪个体检索系统")
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 共享参数
    parent_parser = argparse.ArgumentParser(add_help=False)
    parent_parser.add_argument("--model_path", default=BEST_MODEL_PATH, 
                             help="模型路径，不提供时使用预训练模型")
    parent_parser.add_argument("--device", default="mps", choices=["cpu", "cuda", "mps"], 
                             help="计算设备")
    parent_parser.add_argument("--embedding_dim", type=int, default=512, 
                             help="特征向量维度")
    
    # 注册特征命令
    register_parser = subparsers.add_parser("register", parents=[parent_parser], 
                                          help="注册特征向量")
    register_parser.add_argument("--img_folder", required=True, 
                               help="图像文件夹路径")
    register_parser.add_argument("--output_index", default="index/faiss_cat_index", 
                               help="输出索引路径")
    register_parser.add_argument("--limited", action="store_true", 
                               help="是否每个类别只注册3张固定图片")
    register_parser.add_argument("--max_workers", type=int, default=8, 
                               help="并行处理的最大线程数")
    register_parser.add_argument("--batch_size", type=int, default=100, 
                               help="批处理大小")
    register_parser.add_argument("--use_yolo", action="store_true", 
                               help="是否使用YOLO检测猫头")
    
    # 增量注册命令
    inc_parser = subparsers.add_parser("increment", parents=[parent_parser], 
                                     help="增量注册特征向量")
    inc_parser.add_argument("--img_folder", required=True, 
                          help="新图像文件夹路径")
    inc_parser.add_argument("--index_path", default="index/faiss_cat_index", 
                          help="现有索引路径")
    inc_parser.add_argument("--max_workers", type=int, default=8, 
                          help="并行处理的最大线程数")
    inc_parser.add_argument("--limited", action="store_true",
                          help="是否限制每个类别的图片数量")
    inc_parser.add_argument("--images_per_class", type=int, default=3,
                          help="每个类别保留的图片数量")
    inc_parser.add_argument("--use_yolo", action="store_true", 
                          help="是否使用YOLO检测猫头")
    
    # 限定数量增量注册命令（新增）
    limited_inc_parser = subparsers.add_parser("limited-increment", parents=[parent_parser], 
                                             help="限定数量的增量注册特征向量")
    limited_inc_parser.add_argument("--img_folder", required=True, 
                                  help="新图像文件夹路径")
    limited_inc_parser.add_argument("--index_path", default="index/faiss_cat_index", 
                                  help="现有索引路径")
    limited_inc_parser.add_argument("--max_workers", type=int, default=8, 
                                  help="并行处理的最大线程数")
    limited_inc_parser.add_argument("--images_per_class", type=int, default=3, 
                                  help="每个类别保留的图片数量")
    limited_inc_parser.add_argument("--use_yolo", action="store_true", 
                                  help="是否使用YOLO检测猫头")
    
    # API服务命令
    serve_parser = subparsers.add_parser("serve", parents=[parent_parser], 
                                       help="启动API服务")
    serve_parser.add_argument("--index_path", default="index/faiss_cat_index", 
                            help="索引路径")
    serve_parser.add_argument("--host", default="0.0.0.0", 
                            help="服务主机地址")
    serve_parser.add_argument("--port", type=int, default=8080, 
                            help="服务端口")
    serve_parser.add_argument("--debug", action="store_true", 
                            help="是否启用调试模式")
    serve_parser.add_argument("--use_yolo", action="store_true", 
                            help="是否使用YOLO检测猫头")
    
    # 删除命令
    delete_parser = subparsers.add_parser("delete", parents=[parent_parser], 
                                        help="删除特定类别的特征向量")
    delete_parser.add_argument("--index_path", default="index/faiss_cat_index", 
                             help="索引路径")
    delete_parser.add_argument("--class_id", required=True, 
                             help="要删除的宠物类别ID")
    
    # 列出类别命令
    list_parser = subparsers.add_parser("list", parents=[parent_parser], 
                                      help="列出索引中的所有宠物类别")
    list_parser.add_argument("--index_path", default="index/faiss_cat_index", 
                           help="索引路径")
    
    return parser.parse_args()

def main():
    args = parse_args()
    
    if args.command == "register":
        # 创建输出目录
        os.makedirs(os.path.dirname(args.output_index), exist_ok=True)
        
        if args.limited:
            # 导入有限注册模块
            from src.faiss_retrieval.register_features import register_limited_features
            
            # 执行有限注册（每个类别仅注册3张固定图片）
            register_limited_features(
                args.img_folder,
                output_index=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                use_yolo=args.use_yolo
            )
        else:
            # 导入注册模块
            from faiss_retrieval.register_features import register_all_features
            
            # 执行全量注册
            register_all_features(
                args.img_folder,
                output_index=args.output_index,
                model_path=args.model_path,
                device=args.device,
                max_workers=args.max_workers,
                embedding_dim=args.embedding_dim,
                batch_size=args.batch_size,
                use_yolo=args.use_yolo
            )
    
    elif args.command == "increment":
        # 导入增量注册模块
        from faiss_retrieval.register_features import register_incremental
        
        # 执行增量注册
        register_incremental(
            args.img_folder,
            index_path=args.index_path,
            model_path=args.model_path,
            device=args.device,
            max_workers=args.max_workers,
            embedding_dim=args.embedding_dim,
            limited=args.limited,
            images_per_class=args.images_per_class,
            use_yolo=args.use_yolo
        )
    
    elif args.command == "limited-increment":
        # 导入增量注册模块
        from faiss_retrieval.register_features import register_incremental
        
        # 执行限定数量的增量注册
        register_incremental(
            args.img_folder,
            index_path=args.index_path,
            model_path=args.model_path,
            device=args.device,
            max_workers=args.max_workers,
            embedding_dim=args.embedding_dim,
            limited=True,
            images_per_class=args.images_per_class,
            use_yolo=args.use_yolo
        )
    
    elif args.command == "serve":
        # 导入API服务模块
        from faiss_retrieval.app import app, init_models, create_basic_templates
        
        # 创建模板
        create_basic_templates()
        
        # 初始化模型和索引
        success = init_models(
            model_path=args.model_path,
            index_path=args.index_path,
            device=args.device,
            embedding_dim=args.embedding_dim,
            use_yolo=args.use_yolo
        )
        
        if not success:
            logging.error("初始化失败，请先构建索引")
            return
        
        # 启动服务
        logging.info(f"启动服务，地址: {args.host}:{args.port}, YOLO检测{'启用' if args.use_yolo else '禁用'}")
        app.run(host=args.host, port=args.port, debug=args.debug)
    
    elif args.command == "delete":
        # 导入删除功能
        from faiss_retrieval.register_features import delete_by_class_id
        
        # 执行删除操作
        delete_by_class_id(
            index_path=args.index_path,
            class_id=args.class_id
        )
    
    elif args.command == "list":
        # 导入列表功能
        from faiss_retrieval.register_features import list_classes
        
        # 列出索引中的类别
        list_classes(args.index_path)
    
    else:
        logging.error("未知命令，请使用 register, increment, limited-increment, serve, delete 或 list")
        return

if __name__ == "__main__":
    main() 