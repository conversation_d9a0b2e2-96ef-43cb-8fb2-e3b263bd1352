#!/usr/bin/env python
"""
权重转换工具

从训练的完整模型文件中提取特征提取器部分的权重，
并保存为一个独立的权重文件，用于FAISS检索系统。
"""

import torch
import os
import argparse
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def convert_weights(input_path, output_path):
    """
    转换权重文件
    
    Args:
        input_path: 输入的完整模型路径
        output_path: 输出的特征提取器权重路径
    """
    # 检查输入文件是否存在
    if not os.path.exists(input_path):
        logging.error(f"输入文件不存在: {input_path}")
        return False
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    try:
        # 加载完整模型
        logging.info(f"加载模型: {input_path}")
        checkpoint = torch.load(input_path, map_location='cpu')
        
        # 检查是否为字典格式
        if not isinstance(checkpoint, dict):
            logging.error(f"不支持的模型格式: {type(checkpoint)}")
            return False
        
        # 提取状态字典
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
            logging.info(f"从checkpoint的model_state_dict中提取权重")
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            logging.info(f"从checkpoint的state_dict中提取权重")
        else:
            # 假设整个字典就是状态字典
            state_dict = checkpoint
            logging.info(f"假设整个checkpoint就是状态字典")
        
        # 提取特征提取器部分的权重
        feature_extractor_dict = {}
        for key, value in state_dict.items():
            # 只保留backbone和embedding部分的权重
            if key.startswith('backbone.') or key.startswith('embedding.'):
                feature_extractor_dict[key] = value
                
        if not feature_extractor_dict:
            logging.warning("没有找到backbone或embedding权重！尝试其他格式...")
            
            # 检查是否有不同的键名模式
            if any('conv' in k for k in state_dict.keys()):
                logging.info("检测到卷积层权重，尝试直接使用...")
                feature_extractor_dict = state_dict
        
        # 打印统计信息
        total_params = len(state_dict)
        extracted_params = len(feature_extractor_dict)
        logging.info(f"总参数数量: {total_params}")
        logging.info(f"提取的特征提取器参数数量: {extracted_params}")
        
        if extracted_params == 0:
            logging.error("未能提取任何参数！")
            return False
        
        # 保存提取的权重
        logging.info(f"保存特征提取器权重到: {output_path}")
        torch.save(feature_extractor_dict, output_path)
        
        logging.info(f"转换完成，特征提取器权重已保存")
        return True
        
    except Exception as e:
        logging.error(f"转换过程中出错: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="模型权重转换工具")
    parser.add_argument("--input_path", required=True, help="输入的完整模型路径")
    parser.add_argument("--output_path", default="index/feature_extractor_weights.pth", 
                      help="输出的特征提取器权重路径")
    
    args = parser.parse_args()
    convert_weights(args.input_path, args.output_path)

if __name__ == "__main__":
    main() 