#!/usr/bin/env python3
import pickle
import os
import sys

def check_id_in_faiss_map(index_map_path, target_id):
    """检查指定ID是否存在于FAISS索引映射中"""
    try:
        # 检查文件是否存在
        if not os.path.exists(index_map_path):
            print(f"错误: 映射文件不存在: {index_map_path}")
            return False
        
        # 加载ID映射
        with open(index_map_path, 'rb') as f:
            id_map = pickle.load(f)
        
        # 检查ID是否在元数据中
        target_id = str(target_id)
        found = False
        
        # 统计包含目标ID的数量
        count = 0
        for idx, meta in id_map.items():
            class_id = meta.get('class_id', '')
            if str(class_id) == target_id:
                found = True
                count += 1
                print(f"找到ID为 {target_id} 的宠物! (索引ID: {idx})")
                print(f"元数据信息: {meta}")
        
        if found:
            print(f"总计: 在索引中找到 {count} 个 ID 为 {target_id} 的向量")
        else:
            print(f"索引中不存在 ID 为 {target_id} 的宠物")
            
        # 输出一些索引统计信息
        print(f"\n索引统计:")
        print(f"总向量数: {len(id_map)}")
        
        # 随机显示少量样本以验证数据结构
        print("\n样本数据结构:")
        for idx, meta in list(id_map.items())[:3]:
            print(f"  索引ID {idx}: {meta}")
        
        return found
    
    except Exception as e:
        print(f"检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    index_map_path = "/Users/<USER>/python/cat/src/High_Precision_Individual_Identification/index/faiss_cat_index_map.pkl"
    target_id = "30002"
    
    if len(sys.argv) > 1:
        target_id = sys.argv[1]
    
    check_id_in_faiss_map(index_map_path, target_id) 

    # 使用实例
    # python check_faiss_id.py 30002