import faiss
import numpy as np
import os
import pickle
import logging
import torch

class FaissIndexer:
    """FAISS向量索引器，用于高效相似度检索"""
    
    def __init__(self, dim=512, index_path="faiss_index", use_gpu=False):
        """
        初始化FAISS索引器
        
        Args:
            dim: 特征向量维度
            index_path: 索引保存路径（无扩展名）
            use_gpu: 是否使用GPU加速
        """
        self.dim = dim
        self.index_path = index_path
        self.use_gpu = use_gpu
        self.device_info = self._get_device_info()
        
        # 创建内积索引（对归一化向量，内积等价于余弦相似度）
        self.index = faiss.IndexFlatIP(dim)
        
        # 支持使用GPU加速
        if use_gpu:
            try:
                self.res = faiss.StandardGpuResources()
                self.index = faiss.index_cpu_to_gpu(self.res, 0, self.index)
                logging.info(f"成功启用GPU加速(CUDA)")
            except Exception as e:
                logging.warning(f"GPU加速失败: {e}，将使用CPU")
                self.use_gpu = False
        else:
            logging.info(f"使用CPU进行FAISS索引操作 ({self.device_info})")
        
        # 维护ID到元数据的映射
        self.id_map = {}  # {索引ID: {图像路径, 类别ID, 其他元信息}}

    def _normalize_features(self, features):
        norms = np.linalg.norm(features, axis=1, keepdims=True) + 1e-10
        return features / norms
    
    def _get_device_info(self):
        """获取当前系统设备信息"""
        device_info = ["CPU"]
        if torch.cuda.is_available():
            device_info.append(f"CUDA: {torch.cuda.get_device_name(0)}")
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device_info.append("MPS (Apple Silicon)")
        return ", ".join(device_info)
        
    def build_index(self, feature_list, metadata_list):
        """
        构建索引
        
        Args:
            feature_list: 特征向量列表 [np.array]
            metadata_list: 元数据列表 [dict]，每个元素包含图像信息
        """
        # 确保特征和元数据长度匹配
        assert len(feature_list) == len(metadata_list), "特征数量与元数据数量不匹配"
        
        # 堆叠特征向量
        features = self._normalize_features(np.vstack(feature_list).astype(np.float32))
        
        # 将特征添加到索引
        self.index.add(features)
        
        # 构建ID映射
        self.id_map = {i: meta for i, meta in enumerate(metadata_list)}
        
        logging.info(f"索引构建完成，共 {len(feature_list)} 个向量")
        
    def save(self):
        """保存索引和ID映射"""
        # 创建目录
        os.makedirs(os.path.dirname(self.index_path), exist_ok=True)
        
        # 转换为CPU索引后保存
        index_to_save = faiss.index_gpu_to_cpu(self.index) if self.use_gpu else self.index
        faiss.write_index(index_to_save, f"{self.index_path}.index")
        
        # 保存ID映射
        with open(f"{self.index_path}_map.pkl", 'wb') as f:
            pickle.dump(self.id_map, f)
            
        logging.info(f"索引和映射已保存到 {self.index_path}")
        
    def load(self):
        """加载索引和ID映射"""
        # 确保文件存在
        if not os.path.exists(f"{self.index_path}.index"):
            raise FileNotFoundError(f"索引文件不存在: {self.index_path}.index")
            
        if not os.path.exists(f"{self.index_path}_map.pkl"):
            raise FileNotFoundError(f"映射文件不存在: {self.index_path}_map.pkl")
        
        # 加载索引
        self.index = faiss.read_index(f"{self.index_path}.index")
        
        # 如果需要GPU加速，转换到GPU
        if self.use_gpu:
            try:
                self.res = faiss.StandardGpuResources()
                self.index = faiss.index_cpu_to_gpu(self.res, 0, self.index)
            except Exception as e:
                logging.warning(f"GPU加速失败: {e}，将使用CPU")
                self.use_gpu = False
        
        # 加载ID映射
        with open(f"{self.index_path}_map.pkl", 'rb') as f:
            self.id_map = pickle.load(f)
            
        logging.info(f"成功加载索引，包含 {self.index.ntotal} 个向量")
        
    def search(self, query_vec, k=5):
        """
        搜索最相似的k个向量
        
        Args:
            query_vec: 查询向量 (numpy数组)
            k: 返回的最相似结果数量
            
        Returns:
            tuple: (results, final_label, top1_score, vote_scores)
                results: list of (metadata, similarity_score)
                final_label: 投票得到的最终类别标签
                top1_score: Top-1相似度
                vote_scores: dict，类别对应的加权相似度得分
        """
        # 确保query_vec是2D数组
        if len(query_vec.shape) == 1:
            query_vec = np.expand_dims(query_vec, axis=0).astype(np.float32)
            
        # 执行搜索
        scores, ids = self.index.search(query_vec, k)
        
        # 格式化结果
        results = []
        for i, id_val in enumerate(ids[0]):
            if id_val != -1 and id_val in self.id_map:  # -1表示无效ID
                results.append((self.id_map[id_val], float(scores[0][i])))
                
        # 提取 top-K 投票标签和相似度
        from collections import defaultdict
        vote_score = defaultdict(float)
        topk_labels = []
        topk_scores = []

        for meta, score in results:
            label = meta.get("class_id", "unknown")
            topk_labels.append(label)
            topk_scores.append(score)
            vote_score[label] += score

        if topk_scores:
            final_label = max(vote_score.items(), key=lambda x: x[1])[0]
            top1_score = topk_scores[0]
        else:
            final_label = "unknown"
            top1_score = 0.0

        return results, final_label, top1_score, dict(vote_score)
        
    def add_vectors(self, feature_list, metadata_list):
        """
        向已有索引添加新向量
        
        Args:
            feature_list: 特征向量列表
            metadata_list: 元数据列表
        """
        # 确保特征和元数据长度匹配
        assert len(feature_list) == len(metadata_list), "特征数量与元数据数量不匹配"
        
        # 堆叠特征向量
        features = self._normalize_features(np.vstack(feature_list).astype(np.float32))
        
        # 获取当前索引大小
        current_size = self.index.ntotal
        
        # 添加特征到索引
        self.index.add(features)
        
        # 更新ID映射
        for i, meta in enumerate(metadata_list):
            self.id_map[current_size + i] = meta
            
        logging.info(f"向索引中添加了 {len(feature_list)} 个新向量，当前总数: {self.index.ntotal}")
        
    def get_total_vectors(self):
        """返回索引中的向量总数"""
        return self.index.ntotal

    def get_class_counts(self):
        """
        统计每个class_id的特征向量数量
        
        Returns:
            dict: {class_id: count} 每个类别的特征向量数量
        """
        class_counts = {}
        for _, meta in self.id_map.items():
            class_id = meta.get('class_id', 'unknown')
            class_counts[class_id] = class_counts.get(class_id, 0) + 1
        
        return class_counts
        
    def remove_by_class_id(self, class_id):
        """
        根据class_id删除特定宠物的所有特征向量
        
        Args:
            class_id: 要删除的宠物类别ID
            
        Returns:
            int: 删除的向量数量
        """
        try:
            # 1. 收集要保留和删除的特征向量ID
            keep_ids = []
            remove_ids = []
            
            for id_val, meta in self.id_map.items():
                if meta.get('class_id') != class_id:
                    keep_ids.append(id_val)
                else:
                    remove_ids.append(id_val)
            
            # 如果没有找到要删除的类别，直接返回
            if not remove_ids:
                logging.warning(f"未找到类别 {class_id} 的向量")
                return 0
            
            logging.info(f"将删除 {len(remove_ids)} 个向量，保留 {len(keep_ids)} 个向量")
            
            # 2. 创建一个新的索引
            new_index = faiss.IndexFlatIP(self.dim)
            
            # 3. 获取所有向量
            vectors = []
            new_id_map = {}
            
            # 4. 准备新索引所需的向量和映射
            if hasattr(self.index, 'reconstruct'):
                # 使用reconstruct方法获取向量，这是更可靠的方法
                for new_id, old_id in enumerate(keep_ids):
                    # 重建向量
                    vector = self.index.reconstruct(int(old_id))
                    vectors.append(vector)
                    new_id_map[new_id] = self.id_map[old_id]
            else:
                # 替代方案：创建一个全新的索引
                logging.warning("索引不支持reconstruct方法，将使用替代方案")
                
                # 遍历保留的ID，收集完整的特征向量和元数据
                for i, img_path in enumerate([self.id_map[idx].get('path') for idx in keep_ids]):
                    if img_path:
                        # 从图像文件重新提取特征
                        logging.warning(f"需要从图像文件重新提取特征: {i+1}/{len(keep_ids)}")
                        try:
                            # 这里不实际提取特征，只构建映射
                            new_id_map[i] = self.id_map[keep_ids[i]]
                        except Exception as ex:
                            logging.error(f"重建索引时处理 {img_path} 失败: {ex}")
                
                # 如果没有成功保留任何向量，返回0
                if not new_id_map:
                    logging.error("未能保留任何向量，索引重建失败")
                    return 0
            
            # 5. 添加向量到新索引
            if vectors:  # 只有当我们成功收集了向量时才执行
                vectors = np.array(vectors, dtype=np.float32)
                new_index.add(vectors)
            
            # 6. 更新索引和映射
            removed_count = len(remove_ids)
            
            # 如果是GPU索引，转换为GPU
            if self.use_gpu:
                new_index = faiss.index_cpu_to_gpu(self.res, 0, new_index)
            
            # 更新索引和映射
            self.index = new_index
            self.id_map = new_id_map
            
            logging.info(f"已删除类别 {class_id} 的 {removed_count} 个向量，剩余 {self.index.ntotal} 个向量")
            return removed_count
            
        except Exception as e:
            logging.error(f"删除向量失败: {e}")
            return 0