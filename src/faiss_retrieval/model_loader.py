# ✅ 统一架构模型加载器 - 确保训练/验证/推理三阶段一致性
# 使用统一的FeatureExtractor架构，移除简化路径

import logging
import os
# from feature.feature_extractor import FeatureExtractor
from utils import log_utils
import torch
from pathlib import Path

# 添加PyTorch序列化安全全局变量配置
try:
    # 尝试添加numpy.core.multiarray.scalar到安全列表
    import numpy as np
    from torch.serialization import add_safe_globals
    # 添加numpy.core.multiarray.scalar到安全列表
    add_safe_globals(['numpy.core.multiarray.scalar', 'numpy.core.multiarray._reconstruct'])
    log_utils.info("✅ PyTorch序列化安全全局变量配置成功")
except (ImportError, AttributeError) as e:
    log_utils.error(f"⚠️ 无法配置PyTorch序列化安全全局变量: {e}")


# ==================== 统一架构接口 ====================

class FeatureExtractorWrapper:
    """统一架构的特征提取器包装类 - 确保与训练时完全一致"""
    
    def __init__(self, model_path=None, device='cpu', embedding_dim=512):
        """
        初始化特征提取器 - 统一架构版本

        Args:
            model_path: 模型权重路径，如果为None则使用预训练权重
            device: 计算设备 'cpu'或'cuda'或'mps'
            embedding_dim: 特征向量维度
        """
        # ✅ 关键修复：直接使用训练时的FeatureExtractor架构
        self.device = self._check_device(device)
        self.device_name = str(self.device)
        self.embedding_dim = embedding_dim
        
        # 创建与训练时完全相同的FeatureExtractor
        log_utils.info(f"🔧 创建统一FeatureExtractor架构 (推理模式)")
        # 直接导入原始的FeatureExtractor类，避免递归调用
        from feature.feature_extractor import FeatureExtractor as OriginalFeatureExtractor
        self.model = OriginalFeatureExtractor(embedding_dim=embedding_dim)
        
        # 初始化特征指标（默认为空）
        self.feature_metrics = {}
        
        # 加载权重（如果提供）
        if model_path and Path(model_path).exists():
            self._load_weights(model_path)
        else:
            if model_path:
                log_utils.warning(f"⚠️ 权重文件不存在: {model_path}")
            log_utils.info("✅ 使用预训练权重初始化")
        
        # 移动到指定设备并设置为评估模式
        self.model = self.model.to(self.device)
        self.model.eval()
        
        # 创建图像预处理变换
        from torchvision import transforms
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        log_utils.info("✅ 统一架构FeatureExtractor初始化完成")
        log_utils.info(f"   ├─ 架构: 与训练时完全一致")
        log_utils.info(f"   ├─ 设备: {self.device}")
        log_utils.info(f"   └─ 权重: {'已加载' if model_path and Path(model_path).exists() else '预训练'}")

    def extract(self, img_path):
        """
        从图像提取特征向量 - 统一架构接口

        Args:
            img_path: 图像路径

        Returns:
            normalized_embedding: L2归一化后的特征向量(numpy数组)
        """
        from PIL import Image
        import numpy as np
        
        try:
            # 确保模型处于eval模式
            if hasattr(self.model, 'eval'):
                self.model.eval()
            
            # 加载和预处理图像
            image = Image.open(img_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # ✅ 关键修复：使用extract_features方法，确保与训练/验证时一致
            with torch.no_grad():
                embedding = self.model.extract_features(image_tensor)
                
                # 处理可能的返回值 - train.py会在推理时只返回特征，在训练时返回(feat, arc_logits)
                if isinstance(embedding, tuple):
                    embedding = embedding[0]  # 如果是元组，使用第一个元素(特征)
            
            # 转换为numpy数组 - 不需要再次归一化，模型内部已经做了
            embedding_np = embedding.cpu().numpy().flatten()
            
            log_utils.debug(f"✅ 特征提取完成: {img_path}")
            log_utils.debug(f"   ├─ 特征维度: {embedding_np.shape}")
            log_utils.debug(f"   └─ 特征范数: {np.linalg.norm(embedding_np):.6f}")
            
            return embedding_np
            
        except Exception as e:
            log_utils.error(f"❌ 特征提取失败: {img_path}")
            log_utils.error(f"   错误: {e}")
            raise

    def get_feature_metrics(self):
        """
        获取模型特征分析指标 - 统一架构接口

        Returns:
            dict: 特征指标字典，包含类内距离、类间距离等信息
                如果模型未加载或无指标信息，则返回空字典
        """
        if not hasattr(self, 'feature_metrics'):
            log_utils.warning("模型未包含特征分析指标信息")
            return {}

        return self.feature_metrics.copy()  # 返回副本以防止外部修改

    def print_metrics(self):
        """
        格式化打印当前模型的特征分析指标 - 统一架构接口

        Returns:
            None
        """
        metrics = self.get_feature_metrics()
        if not metrics:
            print("📊 当前模型没有可用的特征分析指标")
            return
        
        print("\n" + "="*60)
        print(" 模型特征分析指标 ".center(60, "="))
        print("="*60)
        
        # 基本指标
        print(f"类内距离: {metrics.get('intra_distance', 0):.4f}")
        print(f"类间距离: {metrics.get('inter_distance', 0):.4f}")

        if metrics.get('intra_distance', 0) > 0:
            separation = metrics.get('inter_distance', 0) / metrics.get('intra_distance', 0)
            print(f"分离比 (Inter/Intra): {separation:.4f}")

        if 'separation_ratio' in metrics:
            print(f"归一化分离比: {metrics.get('separation_ratio', 0):.4f}")

        # 其他可选指标
        if 'fisher_score' in metrics and metrics['fisher_score'] > 0:
            print(f"Fisher分数: {metrics.get('fisher_score', 0):.4f}")

        if 'compactness' in metrics and metrics['compactness'] > 0:
            print(f"特征紧凑度: {metrics.get('compactness', 0):.4f}")
            
        print("="*60)

    def _check_device(self, requested_device):
        """检查并返回可用的设备"""
        if requested_device == 'auto':
            if torch.cuda.is_available():
                device = torch.device('cuda')
                device_name = torch.cuda.get_device_name(0) if torch.cuda.is_available() else "CUDA"
                log_utils.info(f"🔧 自动检测到CUDA设备: {device_name}")
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                device = torch.device('mps')
                log_utils.info("🔧 自动检测到MPS设备(Apple Silicon)")
            else:
                device = torch.device('cpu')
                log_utils.info("🔧 使用CPU设备")
        elif requested_device == 'cuda' and torch.cuda.is_available():
            device_name = torch.cuda.get_device_name(0)
            log_utils.info(f"使用CUDA设备: {device_name}")
            device = torch.device('cuda')
        elif requested_device == 'mps' and hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            log_utils.info("使用MPS设备(Apple Silicon)")
            device = torch.device('mps')
        else:
            if requested_device != 'cpu':
                log_utils.warning(f"请求的设备 '{requested_device}' 不可用，回退到CPU")
            device = torch.device('cpu')
        
        return device

    def _load_weights(self, weight_path):
        """
        加载已训练的模型权重 - 增强版权重加载器
        
        支持功能：
        1. 多种checkpoint格式兼容
        2. 权重键名映射和修复
        3. 特征指标提取和验证
        4. 详细的加载统计和诊断
        """
        log_utils.info(f"📦 从 {weight_path} 加载模型权重")
        
        try:
            # 尝试加载checkpoint
            try:
                checkpoint = torch.load(weight_path, map_location=self.device, weights_only=False)
                log_utils.info("✅ 成功加载模型checkpoint")
            except TypeError:
                log_utils.error("🔄 使用兼容模式加载模型...")
                checkpoint = torch.load(weight_path, map_location=self.device)

            # 提取训练权重和元数据
            model_state_dict = None
            feature_analysis = {}
            
            if isinstance(checkpoint, dict):
                # 标准格式的模型checkpoint
                if 'model_state_dict' in checkpoint:
                    model_state_dict = checkpoint['model_state_dict']
                    epoch = checkpoint.get('epoch', 'unknown')
                    log_utils.info(f"📊 加载checkpoint中的模型权重 (Epoch: {epoch})")

                    # 提取特征分离比信息作为参考
                    separation_ratio = checkpoint.get('separation_ratio', 0)
                    if separation_ratio > 0:
                        log_utils.info(f"📈 模型特征分离比: {separation_ratio:.4f}")

                    # 提取特征分析数据
                    feature_analysis = checkpoint.get('feature_analysis', {})
                    if feature_analysis and isinstance(feature_analysis, dict):
                        # 使用辅助方法提取指标
                        metrics = self._extract_feature_metrics(feature_analysis)
                        self.feature_metrics = metrics

                        # 检查距离信息是否有效
                        intra_mean = metrics.get('intra_distance', 0)
                        inter_mean = metrics.get('inter_distance', 0)

                        if intra_mean == 0 and inter_mean == 0:
                            log_utils.warning("⚠️ 警告: 模型中的类内和类间距离信息为0，这可能导致检索精度下降")
                            log_utils.warning("      可能原因: 1) 模型训练时未正确计算或保存这些信息")
                            log_utils.warning("                2) 版本兼容性问题导致信息丢失")
                            log_utils.warning("      建议: 重新训练模型或使用最新版本的代码进行训练")
                        else:
                            log_utils.info(f"📊 类内距离: {intra_mean:.4f}, 类间距离: {inter_mean:.4f}")
                else:
                    model_state_dict = checkpoint
                    log_utils.info("📊 直接加载状态字典")
            else:
                model_state_dict = checkpoint
                log_utils.info("📊 直接加载整个模型")

            # 尝试加载模型权重
            if model_state_dict:
                try:
                    # 直接加载，可能会有兼容性问题
                    missing_keys, unexpected_keys = self.model.load_state_dict(model_state_dict, strict=False)
                    
                    # 权重加载统计
                    total_keys = len(self.model.state_dict())
                    loaded_keys = total_keys - len(missing_keys)
                    load_rate = loaded_keys / total_keys if total_keys > 0 else 0
                    
                    log_utils.info(f"📊 权重加载统计:")
                    log_utils.info(f"   ├─ 成功加载: {loaded_keys}/{total_keys} ({load_rate:.1%})")
                    
                    if missing_keys:
                        log_utils.warning(f"   ├─ 缺失权重: {len(missing_keys)}个")
                        if len(missing_keys) <= 10:  # 只显示前10个缺失键
                            log_utils.debug(f"      缺失键: {missing_keys}")
                    
                    if unexpected_keys:
                        log_utils.warning(f"   └─ 意外权重: {len(unexpected_keys)}个")
                        if len(unexpected_keys) <= 10:  # 只显示前10个意外键
                            log_utils.debug(f"      意外键: {unexpected_keys}")
                    
                    if load_rate >= 0.95:
                        log_utils.info("✅ 权重加载成功")
                    else:
                        log_utils.warning("⚠️ 权重部分加载，尝试权重键名映射修复...")
                        # 尝试权重键名映射修复
                        self._attempt_weight_key_mapping(model_state_dict, missing_keys, unexpected_keys)
                        
                except Exception as e:
                    log_utils.error(f"⚠️ 直接加载权重失败: {e}")
                    log_utils.info("🔄 尝试权重键名映射修复...")
                    # 尝试处理权重键名不匹配问题
                    self._attempt_weight_key_mapping(model_state_dict)
            else:
                log_utils.error("❌ 无法提取模型权重")
                
        except Exception as e:
            log_utils.error(f"❌ 权重加载失败: {e}")
            log_utils.warning("🔄 使用预训练权重继续")

    def _attempt_weight_key_mapping(self, model_state_dict, missing_keys=None, unexpected_keys=None):
        """
        尝试权重键名映射修复
        
        Args:
            model_state_dict: 原始权重字典
            missing_keys: 缺失的键列表
            unexpected_keys: 意外的键列表
        """
        log_utils.info("🔧 开始权重键名映射修复...")
        
        if model_state_dict:
            new_state_dict = {}

            # 查看我们的模型有哪些键
            model_keys = set(self.model.state_dict().keys())
            checkpoint_keys = set(model_state_dict.keys())

            # 输出键名差异以便诊断
            if missing_keys is None or unexpected_keys is None:
                missing_keys = model_keys - checkpoint_keys
                unexpected_keys = checkpoint_keys - model_keys
                
            if missing_keys:
                log_utils.debug(f"🔍 模型中缺少的键: {list(missing_keys)[:10]}...")  # 只显示前10个
            if unexpected_keys:
                log_utils.debug(f"🔍 权重中未使用的键: {list(unexpected_keys)[:10]}...")  # 只显示前10个

            # 尝试映射键名
            mapped_count = 0
            for k, v in model_state_dict.items():
                if k in model_keys:
                    # 直接匹配的键
                    new_state_dict[k] = v
                    mapped_count += 1
                elif k.startswith('module.'):
                    # 处理DataParallel的键
                    new_key = k[7:]  # 移除'module.'前缀
                    if new_key in model_keys:
                        new_state_dict[new_key] = v
                        mapped_count += 1
                elif 'backbone.classifier' in k or 'classifier' in k:
                    # 跳过分类器层
                    log_utils.debug(f"🚫 跳过分类器层权重: {k}")
                    continue
                else:
                    # 尝试其他可能的映射
                    possible_keys = [
                        k.replace('backbone.', 'backbone.'),
                        k.replace('feature_attention', 'feature_attention'),
                        k.replace('multi_scale', 'multi_scale'),
                        k.replace('embedding', 'embedding'),
                    ]

                    mapped = False
                    for pk in possible_keys:
                        if pk in model_keys:
                            new_state_dict[pk] = v
                            mapped_count += 1
                            mapped = True
                            break

                    if not mapped:
                        log_utils.debug(f"⚠️ 无法映射键: {k}")

            # 再次尝试加载映射后的权重
            if new_state_dict:
                try:
                    missing_keys, unexpected_keys = self.model.load_state_dict(new_state_dict, strict=False)
                    
                    # 重新计算加载统计
                    total_keys = len(self.model.state_dict())
                    loaded_keys = total_keys - len(missing_keys)
                    load_rate = loaded_keys / total_keys if total_keys > 0 else 0
                    
                    log_utils.info(f"🔧 映射修复后权重加载统计:")
                    log_utils.info(f"   ├─ 映射成功: {mapped_count}个键")
                    log_utils.info(f"   ├─ 成功加载: {loaded_keys}/{total_keys} ({load_rate:.1%})")
                    
                    if missing_keys:
                        log_utils.warning(f"   ├─ 仍有缺失键: {len(missing_keys)}个")
                    
                    if load_rate >= 0.90:
                        log_utils.info("✅ 映射修复后权重加载成功")
                    else:
                        log_utils.warning("⚠️ 映射修复后仍有权重缺失，可能影响性能")
                        
                except Exception as e:
                    log_utils.error(f"❌ 映射修复后仍无法加载权重: {e}")
            else:
                log_utils.error("❌ 映射后没有可用的权重")

    def _extract_feature_metrics(self, feature_analysis):
        """
        从特征分析字典中提取指标，使用统一计算器验证一致性

        Args:
            feature_analysis: 特征分析字典

        Returns:
            dict: 标准化的特征指标字典
        """
        from utils.distance_calculator import compute_separation_ratio
        
        metrics = {}

        # 处理类内距离 - 支持不同版本的字段名
        if 'avg_intra_dist' in feature_analysis:
            metrics['intra_distance'] = feature_analysis['avg_intra_dist']
        elif 'intra_mean' in feature_analysis:
            metrics['intra_distance'] = feature_analysis['intra_mean']
        elif 'intra_distance' in feature_analysis:
            metrics['intra_distance'] = feature_analysis['intra_distance']
        else:
            metrics['intra_distance'] = 0

        # 处理类间距离 - 支持不同版本的字段名
        if 'avg_inter_dist' in feature_analysis:
            metrics['inter_distance'] = feature_analysis['avg_inter_dist']
        elif 'inter_mean' in feature_analysis:
            metrics['inter_distance'] = feature_analysis['inter_mean']
        elif 'inter_distance' in feature_analysis:
            metrics['inter_distance'] = feature_analysis['inter_distance']
        else:
            metrics['inter_distance'] = 0

        # 使用统一计算器重新计算分离比，确保一致性
        if metrics['intra_distance'] > 0 and metrics['inter_distance'] > 0:
            unified_separation_ratio = compute_separation_ratio(metrics['intra_distance'], metrics['inter_distance'])
            metrics['separation_ratio'] = unified_separation_ratio
            
            # 如果原始分离比存在且差异较大，记录警告
            original_ratio = feature_analysis.get('separation_ratio', 0)
            if original_ratio > 0 and abs(unified_separation_ratio - original_ratio) > 0.1:
                print(f"⚠️ 分离比不一致: 原始={original_ratio:.4f}, 统一计算={unified_separation_ratio:.4f}")
        else:
            metrics['separation_ratio'] = feature_analysis.get('separation_ratio', 0)

        metrics['fisher_score'] = feature_analysis.get('fisher_score', 0)
        metrics['compactness'] = feature_analysis.get('compactness', 0)

        # 额外的指标提取
        if 'boundary_ratio' in feature_analysis:
            metrics['boundary_ratio'] = feature_analysis['boundary_ratio']
        if 'confidence_score' in feature_analysis:
            metrics['confidence_score'] = feature_analysis['confidence_score']

        return metrics


# ==================== 统一架构接口 ====================

# 为了避免递归调用，我们不再重新定义FeatureExtractor
# 直接使用FeatureExtractorWrapper作为主要接口


# ==================== 便捷函数 ====================

def load_feature_extractor(model_path=None, device='auto', embedding_dim=512, **kwargs):
    """
    便捷函数：加载统一架构特征提取器
    
    Args:
        model_path: 模型权重路径
        device: 计算设备，'auto'表示自动检测
        embedding_dim: 特征向量维度
        **kwargs: 其他参数
    
    Returns:
        FeatureExtractor: 统一架构特征提取器实例
    """
    return FeatureExtractorWrapper(
        model_path=model_path,
        device=device,
        embedding_dim=embedding_dim,
        **kwargs
    )


def create_compatible_extractor(model_path=None, device='cpu', embedding_dim=512):
    """
    创建统一架构的特征提取器
    
    Args:
        model_path: 模型权重路径
        device: 计算设备
        embedding_dim: 特征向量维度
    
    Returns:
        FeatureExtractor: 统一架构特征提取器
    """
    return FeatureExtractorWrapper(
        model_path=model_path,
        device=device,
        embedding_dim=embedding_dim
    )