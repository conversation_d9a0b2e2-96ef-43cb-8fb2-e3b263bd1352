# 🐱 FAISS猫咪个体检索系统

基于FAISS向量检索技术的工业级猫个体识别系统，支持大规模猫咪图像特征向量检索，实现快速精准的猫咪个体识别。

## 🏗️ 系统架构

系统由以下核心模块组成：

1. **模型加载与特征提取** (`model_loader.py`)
   - 支持EfficientNet-B2特征提取
   - 可加载自定义训练模型或使用预训练模型
   - 自动L2归一化确保相似度计算准确

2. **FAISS索引构建与保存** (`faiss_indexer.py`)
   - 使用快速内积索引 (FlatIP) 实现余弦相似度检索
   - 支持CPU与GPU加速
   - 安全的线程并发访问

3. **向量注册批量处理** (`register_features.py`)
   - 全量注册：处理文件夹中所有猫咪图像
   - 有限注册：每个猫咪类别只注册固定的3张图片
   - 增量注册：向已有索引添加新图片
   - 多线程并行处理加速特征提取

4. **在线检索API接口** (`app.py`)
   - RESTful API设计
   - 图像上传与结果可视化
   - 索引统计分析与监控

## 📦 依赖环境

```
numpy>=1.19.0
faiss-cpu>=1.7.0  # GPU版本: faiss-gpu
torch>=1.8.0
torchvision>=0.9.0
timm>=0.5.4
flask>=2.0.0
pillow>=8.0.0
tqdm>=4.62.0
pathlib>=1.0.1
werkzeug>=2.0.0
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 注册猫咪特征

```bash
# 全量注册（处理所有图片）
python -m src.faiss_retrieval.run register --img_folder output/data/processed_train --model_path output/best_feature_extractor.pth

# 有限注册（每个类别只注册3张固定图片）
python -m src.faiss_retrieval.run register --img_folder output/data/processed_train --model_path output/best_feature_extractor.pth --limited --device mps

python -m src.faiss_retrieval.run register --img_folder output/data/processed_val --model_path output/best_feature_extractor_14.pth --limited --device mps


# 增量注册新猫咪
python -m src.faiss_retrieval.run increment --img_folder /path/to/new_cats --index_path index/faiss_cat_index

python -m src.faiss_retrieval.run register --img_folder output/data/processed_val --model_path output/model_epoch_14.pth --limited --device mps

python -m src.faiss_retrieval.run increment --img_folder test/30002 --index_path index/faiss_cat_index --model_path output/model_epoch_14.pth --use_yolo

python src/test/video_detector.py --video src/test/video/17b4f44844468df76536f6f7183b7bde.mp4 --use_yolo
```

#### 有限注册说明

有限注册模式将为每个猫咪类别（文件夹）仅选取3张固定图片进行注册。对于每个类别目录（如ID为"1"的文件夹），系统会寻找以下格式的图片：

1. 基础图片：`cat.{类别ID}.png`（例如：`cat.1.png`）
2. 第一张增强图片：`cat.{类别ID}_aug1.jpg`（例如：`cat.1_aug1.jpg`）
3. 第十张增强图片：`cat.{类别ID}_aug10.jpg`（例如：`cat.1_aug10.jpg`）

系统使用智能匹配算法，如果找不到精确匹配的文件名，将会：
- 尝试查找不同扩展名（.png, .jpg, .jpeg）的相同文件
- 寻找符合模式的替代图片（如任意包含`cat.{类别ID}`但不是增强图片的基础图像）
- 对于增强图片，寻找任意包含`_aug1.`或`_aug10.`的图片

### 3. 启动检索服务

```bash
python -m src.faiss_retrieval.run serve --model_path output/best_feature_extractor.pth --index_path index/faiss_cat_index --port 8080
```

然后访问 http://localhost:5000 即可使用Web界面进行猫咪检索。

## 🔍 API接口

### 1. 图像搜索接口

- **URL**: `/search`
- **方法**: `POST`
- **参数**:
  - `image`: 图像文件
  - `top_k`: 返回的结果数量 (默认: 5)
  - `draw_result`: 是否在图像上绘制结果 (默认: false)

**响应示例**:

```json
{
  "query_image": "1615492345_cat.jpg",
  "search_time_ms": 15,
  "results": [
    {
      "score": 0.95,
      "similarity_percent": "95.00%",
      "class_id": "cat001",
      "file_name": "cat001_1.jpg",
      "image_path": "/path/to/cat001_1.jpg"
    },
    ...
  ]
}
```

### 2. 索引统计接口

- **URL**: `/stats`
- **方法**: `GET`

**响应示例**:

```json
{
  "total_vectors": 1500,
  "total_classes": 150,
  "class_distribution": {
    "cat001": 10,
    "cat002": 12,
    ...
  }
}
```

## 💡 性能优化

1. **检索性能**
   - 使用FAISS库提供毫秒级检索速度
   - 支持GPU加速，10万+向量库实现<20ms检索

2. **特征质量**
   - 使用高质量EfficientNet-B2特征
   - 特征归一化确保相似度计算稳定

3. **多线程优化**
   - 批量特征提取支持多线程并行
   - 线程安全的索引访问

## 📊 评估指标

项目实现以下性能指标：

- **准确率**: Recall@5 > 95% (同一只猫不同角度)
- **检索速度**: 平均 < 20ms (CPU模式)
- **系统延迟**: 端到端响应 < 200ms

## 🔧 进阶配置

### GPU加速

```bash
python -m src.faiss_retrieval.run serve --device cuda --model_path output/best_feature_extractor.pth
```

### 修改特征维度

```bash
python -m src.faiss_retrieval.run register --embedding_dim 512 --img_folder /path/to/cat_images
```

## 📝 TODO

- [ ] 增加量化索引支持更大规模库
- [ ] 实现类别过滤搜索
- [ ] 添加Docker容器支持 