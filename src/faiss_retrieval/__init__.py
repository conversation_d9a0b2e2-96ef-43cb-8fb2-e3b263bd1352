# FAISS猫咪个体检索系统
# 版本: 1.0.0

from faiss_retrieval.model_loader import FeatureExtractorWrapper as FeatureExtractor
from faiss_retrieval.faiss_indexer import FaissIndexer
from faiss_retrieval.register_features import register_all_features, register_incremental, register_limited_features

__all__ = [
    'FeatureExtractor',
    'FaissIndexer',
    'register_all_features',
    'register_incremental',
    'register_limited_features'
] 