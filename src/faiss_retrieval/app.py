import os
import json
import time
import logging
import argparse
from pathlib import Path
import numpy as np
from PIL import Image, ImageDraw
import threading
import sys
import tempfile

# 添加项目根目录和src目录到导入路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../.."))
src_dir = os.path.join(project_root, "src")

if project_root not in sys.path:
    sys.path.append(project_root)
if src_dir not in sys.path:
    sys.path.append(src_dir)

from flask import Flask, request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename

from faiss_retrieval.model_loader import FeatureExtractorWrapper as FeatureExtractor
from faiss_retrieval.faiss_indexer import FaissIndexer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 全局配置
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp_uploads')
ALLOWED_EXTENSIONS = {'jpg', 'jpeg', 'png'}
INDEX_LOCK = threading.Lock()  # 用于线程安全的索引访问

# 创建上传目录
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB

# 全局变量
extractor = None
indexer = None
yolo_model = None  # 添加YOLO模型全局变量

# YOLO模型路径
YOLO_MODEL_PATH = 'src/models/yolo.pt'

def allowed_file(filename):
    """检查文件是否有合法的扩展名"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def warmup_yolo_model(model):
    """预热YOLO模型，避免首次检测失败"""
    if model is None:
        return
        
    try:
        # 创建一个简单的测试图像
        test_img = np.ones((640, 640, 3), dtype=np.uint8) * 128  # 灰色图像
        
        # 保存为临时文件
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
            temp_path = tmp.name
            Image.fromarray(test_img).save(temp_path)
        
        # 进行一次推理预热
        logging.info("正在预热YOLO模型...")
        _ = model(temp_path)
        logging.info("YOLO模型预热完成")
        
        # 清理临时文件
        try:
            os.unlink(temp_path)
        except:
            pass
    except Exception as e:
        logging.warning(f"YOLO模型预热失败: {e}")

def detect_cat_head(img_path, model):
    """使用YOLO检测猫头位置"""
    try:
        results = model(img_path)
        boxes = results[0].boxes
        
        if boxes is None or len(boxes) == 0:
            logging.warning(f"未检测到猫头: {img_path}")
            return False, None
        
        # 默认取最大面积的框作为猫头
        best_box = max(boxes.xyxy, key=lambda box: (box[2] - box[0]) * (box[3] - box[1]))
        x1, y1, x2, y2 = map(int, best_box.tolist())
        
        # 将xyxy格式转换为xywh格式
        bbox = [x1, y1, x2 - x1, y2 - y1]
        return True, bbox
    except Exception as e:
        logging.error(f"猫头检测失败 {img_path}: {e}")
        return False, None

def crop_cat_head(img_path, bbox):
    """裁剪猫头区域"""
    try:
        image = Image.open(img_path).convert('RGB')
        x, y, w, h = bbox
        
        # 防止裁剪区域越界
        x1, y1 = max(0, x), max(0, y)
        x2, y2 = min(image.width, x1 + w), min(image.height, y1 + h)
        
        if x2 > x1 and y2 > y1:  # 确保有效区域
            cropped_image = image.crop((x1, y1, x2, y2))
            
            # 使用临时文件保存裁剪后的图像
            with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
                cropped_path = tmp.name
                cropped_image.save(cropped_path)
                return True, cropped_path
        
        return False, img_path
    except Exception as e:
        logging.error(f"裁剪猫头失败 {img_path}: {e}")
        return False, img_path

def draw_results_on_image(image_path, results, output_path=None):
    """在图像上绘制检索结果"""
    try:
        # 加载图像
        img = Image.open(image_path).convert('RGB')
        draw = ImageDraw.Draw(img)
        
        # 添加结果文本
        y_pos = 10
        font_size = 20
        
        for i, (metadata, score) in enumerate(results[:5]):  # 只显示前5个结果
            class_id = metadata.get('class_id', 'unknown')
            filename = metadata.get('file_name', 'unknown')
            score_percent = f"{score * 100:.1f}%"
            
            text = f"#{i+1}: {class_id} - {score_percent}"
            draw.rectangle([10, y_pos, 10 + len(text)*9, y_pos + font_size + 4], 
                          fill=(0, 0, 0, 128))
            draw.text((12, y_pos + 2), text, fill=(255, 255, 255))
            y_pos += font_size + 8
        
        # 保存或返回处理后的图像
        if output_path:
            img.save(output_path)
            return output_path
        else:
            result_path = f"{image_path}_result.jpg"
            img.save(result_path)
            return result_path
    except Exception as e:
        logging.error(f"绘制结果失败: {e}")
        return None

def init_models(model_path, index_path, device='cpu', embedding_dim=512, use_yolo=False):
    """初始化模型和索引"""
    global extractor, indexer, yolo_model
    
    # 初始化特征提取器
    extractor = FeatureExtractor(model_path=model_path, device=device, embedding_dim=embedding_dim)
    logging.info(f"特征提取器初始化完成")
    
    # 初始化FAISS索引器
    indexer = FaissIndexer(dim=embedding_dim, index_path=index_path)
    
    # 加载索引（如果存在）
    try:
        indexer.load()
        logging.info(f"索引加载完成，包含 {indexer.get_total_vectors()} 个向量")
    except FileNotFoundError as e:
        logging.warning(f"索引加载失败: {e}，需要先构建索引")
        return False
    
    # 如果启用YOLO，初始化模型
    if use_yolo:
        try:
            # 导入YOLO
            try:
                from ultralytics import YOLO
            except ImportError:
                logging.warning("未安装ultralytics，请使用 pip install ultralytics 安装")
                logging.warning("YOLO检测功能将被禁用")
                return True
                
            # 加载YOLO模型
            yolo_model = YOLO(YOLO_MODEL_PATH)
            logging.info(f"YOLO模型加载完成: {YOLO_MODEL_PATH}")
            
            # 预热模型
            warmup_yolo_model(yolo_model)
            
        except Exception as e:
            logging.error(f"YOLO模型加载失败: {e}")
            logging.warning("YOLO检测功能将被禁用")
    
    return True

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/search', methods=['POST'])
def search():
    """图像搜索接口"""
    global extractor, indexer, yolo_model
    
    # 检查是否有文件
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400
    
    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No selected image'}), 400
    
    if not file or not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file format'}), 400
    
    try:
        # 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = int(time.time())
        unique_filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        # 检查是否需要使用YOLO检测猫头
        use_yolo = request.form.get('use_yolo', 'false').lower() == 'true'
        
        # 使用YOLO检测和裁剪猫头
        processing_path = file_path
        is_cropped = False
        detected_bbox = None
        
        if use_yolo and yolo_model is not None:
            # 检测猫头
            detected, bbox = detect_cat_head(file_path, yolo_model)
            if detected:
                # 裁剪猫头
                is_cropped, cropped_path = crop_cat_head(file_path, bbox)
                if is_cropped:
                    processing_path = cropped_path
                    detected_bbox = bbox
        
        # 提取特征
        feature = extractor.extract(processing_path)
        
        # 清理临时文件
        if is_cropped and processing_path != file_path:
            try:
                os.unlink(processing_path)
            except:
                pass
        
        # 获取top_k参数，默认为5
        top_k = int(request.form.get('top_k', 5))
        
        # 搜索相似向量（线程安全）
        with INDEX_LOCK:
            start_time = time.time()
            results = indexer.search(feature, k=top_k)
            search_time = time.time() - start_time
        
        # 处理结果
        response_data = {
            'query_image': unique_filename,
            'search_time_ms': int(search_time * 1000),
            'results': [],
            'yolo_detection': use_yolo,
            'cat_head_detected': detected_bbox is not None
        }
        
        # 格式化结果
        for metadata, score in results:
            result_item = {
                'score': float(score),
                'similarity_percent': f"{score * 100:.2f}%",
                'class_id': metadata.get('class_id', 'unknown'),
                'file_name': metadata.get('file_name', 'unknown'),
                'image_path': metadata.get('path', '')
            }
            response_data['results'].append(result_item)
        
        # 是否需要在图像上绘制结果
        draw_result = request.form.get('draw_result', 'false').lower() == 'true'
        if draw_result:
            result_img_path = draw_results_on_image(file_path, results)
            if result_img_path:
                response_data['result_image'] = os.path.basename(result_img_path)
        
        return jsonify(response_data)
    
    except Exception as e:
        logging.error(f"搜索失败: {e}")
        return jsonify({'error': str(e)}), 500
    finally:
        # 可选：清理临时文件
        # os.remove(file_path) if os.path.exists(file_path) else None
        pass

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """提供上传文件的访问"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/stats', methods=['GET'])
def get_stats():
    """获取索引统计信息"""
    global indexer
    
    if indexer is None:
        return jsonify({'error': 'Index not initialized'}), 500
    
    # 获取基本统计信息
    try:
        with INDEX_LOCK:
            total_vectors = indexer.get_total_vectors()
            
            # 分析类别分布
            class_counts = {}
            for idx, meta in indexer.id_map.items():
                class_id = meta.get('class_id', 'unknown')
                if class_id in class_counts:
                    class_counts[class_id] += 1
                else:
                    class_counts[class_id] = 1
        
        stats = {
            'total_vectors': total_vectors,
            'total_classes': len(class_counts),
            'class_distribution': class_counts
        }
        
        return jsonify(stats)
    except Exception as e:
        logging.error(f"获取统计信息失败: {e}")
        return jsonify({'error': str(e)}), 500

def create_basic_templates():
    """创建基本的HTML模板"""
    # 创建模板目录
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    os.makedirs(templates_dir, exist_ok=True)
    
    # 创建index.html
    index_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>猫图像检索系统</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                line-height: 1.6;
            }
            h1 {
                color: #333;
                text-align: center;
            }
            .upload-form {
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            .results {
                margin-top: 20px;
            }
            .result-item {
                margin-bottom: 10px;
                padding: 10px;
                border: 1px solid #eee;
                border-radius: 5px;
            }
            .result-image {
                max-width: 100%;
                height: auto;
                margin-top: 20px;
            }
            .loading {
                text-align: center;
                display: none;
            }
            button {
                background-color: #4CAF50;
                color: white;
                padding: 10px 15px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background-color: #45a049;
            }
        </style>
    </head>
    <body>
        <h1>猫图像检索系统</h1>
        
        <div class="upload-form">
            <h2>上传查询图像</h2>
            <form id="search-form" enctype="multipart/form-data">
                <div>
                    <input type="file" id="image" name="image" accept=".jpg,.jpeg,.png" required>
                </div>
                <div>
                    <label for="top_k">返回结果数量:</label>
                    <input type="number" id="top_k" name="top_k" value="5" min="1" max="20">
                </div>
                <div>
                    <input type="checkbox" id="draw_result" name="draw_result" value="true">
                    <label for="draw_result">在图像上显示结果</label>
                </div>
                <div>
                    <button type="submit">搜索</button>
                </div>
            </form>
        </div>
        
        <div class="loading" id="loading">
            <p>搜索中，请稍候...</p>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <h2>查询结果</h2>
            <p id="search-time"></p>
            
            <div id="query-image-container">
                <h3>查询图像</h3>
                <img id="query-image" class="result-image">
            </div>
            
            <div id="result-image-container" style="display: none;">
                <h3>结果可视化</h3>
                <img id="result-image" class="result-image">
            </div>
            
            <h3>相似猫咪</h3>
            <div id="result-list"></div>
        </div>
        
        <script>
            document.getElementById('search-form').addEventListener('submit', function(event) {
                event.preventDefault();
                
                // 显示加载状态
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results').style.display = 'none';
                
                // 获取表单数据
                const formData = new FormData(this);
                
                // 发送请求
                fetch('/search', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载状态
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.error) {
                        alert('错误: ' + data.error);
                        return;
                    }
                    
                    // 显示结果
                    document.getElementById('results').style.display = 'block';
                    
                    // 搜索时间
                    document.getElementById('search-time').textContent = `搜索耗时: ${data.search_time_ms} 毫秒`;
                    
                    // 查询图像
                    const queryImage = document.getElementById('query-image');
                    queryImage.src = `/uploads/${data.query_image}`;
                    
                    // 结果图像
                    if (data.result_image) {
                        document.getElementById('result-image-container').style.display = 'block';
                        document.getElementById('result-image').src = `/uploads/${data.result_image}`;
                    } else {
                        document.getElementById('result-image-container').style.display = 'none';
                    }
                    
                    // 结果列表
                    const resultList = document.getElementById('result-list');
                    resultList.innerHTML = '';
                    
                    data.results.forEach((result, index) => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'result-item';
                        resultItem.innerHTML = `
                            <p><strong>#${index+1}</strong> 类别: ${result.class_id}</p>
                            <p>相似度: ${result.similarity_percent}</p>
                            <p>文件名: ${result.file_name}</p>
                        `;
                        resultList.appendChild(resultItem);
                    });
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    alert('请求失败: ' + error);
                });
            });
        </script>
    </body>
    </html>
    """
    
    with open(os.path.join(templates_dir, 'index.html'), 'w') as f:
        f.write(index_html)
    
    logging.info(f"基本HTML模板创建完成")

def main():
    parser = argparse.ArgumentParser(description="猫图像检索API服务")
    parser.add_argument("--model_path", default=None, help="模型路径")
    parser.add_argument("--index_path", default="index/faiss_cat_index", help="索引路径")
    parser.add_argument("--device", default="cpu", choices=["cpu", "cuda"], help="计算设备")
    parser.add_argument("--embedding_dim", type=int, default=512, help="特征向量维度")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=5000, help="服务端口")
    parser.add_argument("--debug", action="store_true", help="是否启用调试模式")
    
    args = parser.parse_args()
    
    # 创建基本HTML模板
    create_basic_templates()
    
    # 初始化模型和索引
    success = init_models(
        model_path=args.model_path,
        index_path=args.index_path,
        device=args.device,
        embedding_dim=args.embedding_dim
    )
    
    if not success:
        logging.error("初始化失败，请先构建索引")
        return
    
    # 启动Flask应用
    logging.info(f"启动服务，地址: {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug)

if __name__ == '__main__':
    main() 