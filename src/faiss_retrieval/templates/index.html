
    <!DOCTYPE html>
    <html>
    <head>
        <title>猫图像检索系统</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {
                font-family: Arial, sans-serif;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                line-height: 1.6;
            }
            h1 {
                color: #333;
                text-align: center;
            }
            .upload-form {
                margin: 20px 0;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            .results {
                margin-top: 20px;
            }
            .result-item {
                margin-bottom: 10px;
                padding: 10px;
                border: 1px solid #eee;
                border-radius: 5px;
            }
            .result-image {
                max-width: 100%;
                height: auto;
                margin-top: 20px;
            }
            .loading {
                text-align: center;
                display: none;
            }
            button {
                background-color: #4CAF50;
                color: white;
                padding: 10px 15px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            button:hover {
                background-color: #45a049;
            }
        </style>
    </head>
    <body>
        <h1>猫图像检索系统</h1>
        
        <div class="upload-form">
            <h2>上传查询图像</h2>
            <form id="search-form" enctype="multipart/form-data">
                <div>
                    <input type="file" id="image" name="image" accept=".jpg,.jpeg,.png" required>
                </div>
                <div>
                    <label for="top_k">返回结果数量:</label>
                    <input type="number" id="top_k" name="top_k" value="5" min="1" max="20">
                </div>
                <div>
                    <input type="checkbox" id="draw_result" name="draw_result" value="true">
                    <label for="draw_result">在图像上显示结果</label>
                </div>
                <div>
                    <button type="submit">搜索</button>
                </div>
            </form>
        </div>
        
        <div class="loading" id="loading">
            <p>搜索中，请稍候...</p>
        </div>
        
        <div class="results" id="results" style="display: none;">
            <h2>查询结果</h2>
            <p id="search-time"></p>
            
            <div id="query-image-container">
                <h3>查询图像</h3>
                <img id="query-image" class="result-image">
            </div>
            
            <div id="result-image-container" style="display: none;">
                <h3>结果可视化</h3>
                <img id="result-image" class="result-image">
            </div>
            
            <h3>相似猫咪</h3>
            <div id="result-list"></div>
        </div>
        
        <script>
            document.getElementById('search-form').addEventListener('submit', function(event) {
                event.preventDefault();
                
                // 显示加载状态
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results').style.display = 'none';
                
                // 获取表单数据
                const formData = new FormData(this);
                
                // 发送请求
                fetch('/search', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    // 隐藏加载状态
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.error) {
                        alert('错误: ' + data.error);
                        return;
                    }
                    
                    // 显示结果
                    document.getElementById('results').style.display = 'block';
                    
                    // 搜索时间
                    document.getElementById('search-time').textContent = `搜索耗时: ${data.search_time_ms} 毫秒`;
                    
                    // 查询图像
                    const queryImage = document.getElementById('query-image');
                    queryImage.src = `/uploads/${data.query_image}`;
                    
                    // 结果图像
                    if (data.result_image) {
                        document.getElementById('result-image-container').style.display = 'block';
                        document.getElementById('result-image').src = `/uploads/${data.result_image}`;
                    } else {
                        document.getElementById('result-image-container').style.display = 'none';
                    }
                    
                    // 结果列表
                    const resultList = document.getElementById('result-list');
                    resultList.innerHTML = '';
                    
                    data.results.forEach((result, index) => {
                        const resultItem = document.createElement('div');
                        resultItem.className = 'result-item';
                        resultItem.innerHTML = `
                            <p><strong>#${index+1}</strong> 类别: ${result.class_id}</p>
                            <p>相似度: ${result.similarity_percent}</p>
                            <p>文件名: ${result.file_name}</p>
                        `;
                        resultList.appendChild(resultItem);
                    });
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    alert('请求失败: ' + error);
                });
            });
        </script>
    </body>
    </html>
    