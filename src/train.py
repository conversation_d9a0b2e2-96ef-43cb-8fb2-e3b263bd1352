import os
import sys
import ssl

# 添加当前目录到Python路径
project_root = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
src_dir = os.path.dirname(__file__)
sys.path.insert(0, project_root)
sys.path.insert(0, src_dir)

# 解决SSL连接问题
try:
    _create_unverified_https_context = ssl._create_unverified_context
except AttributeError:
    pass
else:
    ssl._create_default_https_context = _create_unverified_https_context

# 设置环境变量解决HuggingFace下载问题
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''
# 设置HuggingFace缓存目录和超时
os.environ['HF_HOME'] = os.path.expanduser('~/.cache/huggingface')
os.environ['TRANSFORMERS_CACHE'] = os.path.expanduser('~/.cache/huggingface/transformers')
os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '1'

# 导入配置
from config import DEFAULT_TRAIN_CONFIG, TrainConfig, DEFAULT_VAL_CONFIG, DEFAULT_STOPPING_CONFIG

# 初始化训练配置
train_config = DEFAULT_TRAIN_CONFIG
val_config = DEFAULT_VAL_CONFIG
stopping_config = DEFAULT_STOPPING_CONFIG

# 根据配置设置环境变量
if train_config.disable_albumentations_update:
    os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'

if train_config.enable_mps_fallback:
    os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

import json
import os
from collections import Counter, defaultdict
from pathlib import Path
from datetime import datetime

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from tqdm import tqdm
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import numpy as np
import math
import random
import torch.multiprocessing as mp

# 设置多进程策略
mp.set_sharing_strategy(train_config.multiprocessing_sharing_strategy)

import time
import matplotlib.colors as mcolors
from sklearn.decomposition import PCA
from typing import Optional, Dict
from feature.cat_dataset import CatDataset
from feature.losses.multi_task_loss import MultiTaskLoss
from feature.feature_extractor import FeatureExtractor
from feature.metrics_calculator import MetricsCalculator
from feature.early_stopping import EarlyStopping
from feature.adaptive_sampling import SamplerCallback
from utils import log_utils
from feature.meta_learning_optimizer import create_meta_learning_system
from feature.curriculum_learning.factory import create_curriculum_learning_system
from feature.curriculum_learning.recovery_config import DEFAULT_RECOVERY_CONFIG

# 启用cuDNN基准（如果配置允许）
if train_config.enable_cudnn_benchmark:
    torch.backends.cudnn.benchmark = True

# 从配置获取常量
METADATA_DIR = train_config.train_metadata_path
VAL_DIR = train_config.val_metadata_path
BATCH_SIZE = train_config.batch_size
NUM_WORKERS = train_config.num_workers
IMG_SIZE = train_config.img_size

# 改为函数形式避免重复执行
def get_device():
    """根据配置获取设备"""
    device_preference = train_config.device_preference
    
    if device_preference == "auto":
        # 自动选择最佳设备
        if torch.backends.mps.is_available():
            device = torch.device("mps")
            if mp.current_process().name == 'MainProcess':
                log_utils.info("Using MPS (Apple GPU) acceleration", tag="TRAIN")
        elif torch.cuda.is_available():
            device = torch.device("cuda")
            if mp.current_process().name == 'MainProcess':
                log_utils.info("Using CUDA acceleration", tag="TRAIN")
        else:
            device = torch.device("cpu")
            if mp.current_process().name == 'MainProcess':
                log_utils.info("Using CPU mode", tag="TRAIN")
    else:
        # 使用指定设备
        device = torch.device(device_preference)
        if mp.current_process().name == 'MainProcess':
            log_utils.info(f"Using specified device: {device_preference}", tag="TRAIN")
    
    return device

# 仅在需要时调用
DEVICE = get_device()

# 从配置创建数据转换
def create_transforms(data_config=None):
    """根据配置创建数据变换"""
    if data_config is None:
        data_config = train_config
    
    # 验证数据变换
    transform_validation = transforms.Compose([
        transforms.Resize((data_config.img_size, data_config.img_size)),
        transforms.ToTensor(),
        transforms.Normalize(data_config.imagenet_mean, data_config.imagenet_std)
    ])
    
    # 训练数据变换
    transform_train = transforms.Compose([
        transforms.RandomHorizontalFlip(p=data_config.horizontal_flip_prob),
        transforms.RandomVerticalFlip(p=data_config.vertical_flip_prob),
        transforms.RandomApply([transforms.ColorJitter(
            brightness=data_config.color_jitter_brightness,
            contrast=data_config.color_jitter_contrast,
            saturation=data_config.color_jitter_saturation,
            hue=data_config.color_jitter_hue
        )], p=data_config.color_jitter_prob),
        transforms.RandomApply([transforms.GaussianBlur(
            kernel_size=data_config.gaussian_blur_kernel_size,
            sigma=data_config.gaussian_blur_sigma
        )], p=data_config.gaussian_blur_prob),
        transforms.RandomAffine(
            degrees=data_config.random_affine_degrees,
            translate=data_config.random_affine_translate,
            scale=data_config.random_affine_scale
        ),
        transforms.ToTensor(),
        transforms.Normalize(data_config.imagenet_mean, data_config.imagenet_std),
        transforms.RandomErasing(
            p=data_config.random_erasing_prob,
            scale=data_config.random_erasing_scale
        ),
    ])
    
    # 困难样本增强变换
    transform_train_difficult = transforms.Compose([
        transforms.RandomHorizontalFlip(p=data_config.difficult_horizontal_flip_prob),
        transforms.RandomVerticalFlip(p=data_config.difficult_vertical_flip_prob),
        transforms.RandomRotation(data_config.difficult_rotation_degrees),
        transforms.ColorJitter(
            brightness=data_config.difficult_color_jitter_brightness,
            contrast=data_config.difficult_color_jitter_contrast,
            saturation=data_config.difficult_color_jitter_saturation,
            hue=data_config.difficult_color_jitter_hue
        ),
        transforms.GaussianBlur(
            kernel_size=data_config.gaussian_blur_kernel_size,
            sigma=data_config.difficult_gaussian_blur_sigma
        ),
        transforms.ToTensor(),
        transforms.Normalize(data_config.imagenet_mean, data_config.imagenet_std),
        transforms.RandomErasing(
            p=data_config.difficult_random_erasing_prob,
            scale=data_config.difficult_random_erasing_scale
        ),
    ])
    
    return transform_train, transform_train_difficult, transform_validation

# 创建变换
transform_train, transform_train_difficult, transform_validation = create_transforms()


def calculate_comprehensive_model_score(val_acc: float, separation_ratio: float, fisher_score: float, 
                                      recall_at_5: float, inter_distance: float = 0.0, intra_distance: float = 1.0) -> float:
    """
    计算综合模型评分
    Args:
        val_acc: 验证准确率
        separation_ratio: 特征分离比
        fisher_score: Fisher判别分数
        recall_at_5: Recall@5指标
        inter_distance: 类间距离
        intra_distance: 类内距离
    Returns:
        综合评分 (0-1范围)
    """
    # 设置各指标权重
    weights = {
        'accuracy': 0.4,      # 验证准确率
        'fisher': 0.3,        # Fisher分数
        'separation': 0.2,    # 特征分离比
        'recall': 0.1         # Recall@5
    }
    # 归一化各指标
    norm_acc = min(1.0, max(0.0, val_acc))
    norm_fisher = min(1.0, max(0.0, fisher_score / max(20.0, fisher_score * 1.2)))
    norm_sep = min(1.0, max(0.0, separation_ratio / max(5.0, separation_ratio * 1.2)))
    norm_recall = min(1.0, max(0.0, recall_at_5))
    # 计算加权得分
    comprehensive_score = (
        weights['accuracy'] * norm_acc +
        weights['fisher'] * norm_fisher +
        weights['separation'] * norm_sep +
        weights['recall'] * norm_recall
    )
    # 距离比例奖励
    if inter_distance > 0 and intra_distance > 0:
        from utils.distance_calculator import compute_separation_ratio
        distance_ratio = compute_separation_ratio(intra_distance, inter_distance)
        distance_bonus = min(0.1, distance_ratio / 10.0)
        comprehensive_score = min(1.0, comprehensive_score + distance_bonus)
    return comprehensive_score

def train_epoch(model, loader, criterion, optimizer, epoch, metrics=None, scheduler=None, callbacks=None, curriculum_state=None):
    """
    单轮训练，回调机制管理
    Args:
        model: 训练模型
        loader: 数据加载器
        criterion: 损失函数
        optimizer: 优化器
        epoch: 当前轮次
        metrics: 指标计算器
        scheduler: 学习率调度器
        callbacks: 训练回调
        curriculum_state: 课程学习状态
    """
    from feature.training_callbacks import TrainingState, BoundaryProtectionState, create_default_callbacks
    model.train()
    # 创建默认回调
    if callbacks is None:
        callbacks = create_default_callbacks()
    # 初始化训练状态
    training_state = TrainingState(
        epoch=epoch,
        batch_idx=0,
        total_batches=len(loader),
        loss=0.0,
        metrics={
            'total_loss': 0.0,
            'batch_count': 0,
            'all_embeddings': [],
            'all_labels': [],
            'boundary_samples': [],
            'weights': {},
            'lr': 0.0
        }
    )
    # 初始化边界保护状态
    boundary_state = BoundaryProtectionState()
    # 训练开始回调
    for callback in callbacks:
        callback.on_epoch_start(training_state)
    # 计算特征质量
    intra_dist, inter_dist = 0, 0
    if metrics and hasattr(metrics, 'get_avg_distances'):
        intra_dist, inter_dist = metrics.get_avg_distances()
    # 刷新输出流
    import sys
    sys.stdout.flush()
    sys.stderr.flush()
    # 创建tqdm进度条
    with tqdm(
        loader,
        desc=f'Training Epoch {epoch}',
        leave=True,
        file=sys.stdout,
        dynamic_ncols=True,
        ascii=False,
        unit="batch",
        disable=False
    ) as pbar:
        for batch_idx, (images, labels) in enumerate(pbar):
            training_state.batch_idx = batch_idx
            training_state.metrics['batch_count'] += 1
            images, labels = images.to(DEVICE), labels.to(DEVICE)
            # 批次开始回调
            for callback in callbacks:
                callback.on_batch_start(training_state)
            # 前向传播
            optimizer.zero_grad()
            embeddings = model(images)
            # 课程学习参数
            curriculum_stage = None
            difficulty_level = None
            easy_ratio = None
            if curriculum_state:
                curriculum_stage = curriculum_state.get('stage', None)
                difficulty_level = curriculum_state.get('difficulty_stats', {}).get('avg_difficulty', None)
                easy_ratio = curriculum_state.get('easy_ratio', None)
            # 设置当前batch索引
            criterion._current_batch_idx = batch_idx
            # 计算损失
            loss, circle_loss, _ = criterion(
                embeddings, labels, epoch,
                intra_dist=intra_dist, inter_dist=inter_dist,
                curriculum_stage=curriculum_stage,
                difficulty_level=difficulty_level,
                easy_ratio=easy_ratio
            )
            # 获取BoundaryResult对象
            latest_boundary_result = criterion.get_latest_boundary_result()
            if latest_boundary_result:
                training_state.metrics['boundary_samples'].append(latest_boundary_result)
            # 反向传播
            loss.backward()
            # 梯度裁剪
            hyperparams = train_config
            max_norm = (hyperparams.gradient_clip_base_norm +
                        hyperparams.gradient_clip_decay_factor *
                        max(0, hyperparams.gradient_clip_decay_epochs - epoch) /
                        hyperparams.gradient_clip_decay_epochs)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=max_norm)
            optimizer.step()
            # 更新学习率
            if scheduler is not None:
                scheduler.step()
            # 记录损失和学习率
            training_state.loss = loss.item()
            training_state.metrics['total_loss'] += loss.item()
            training_state.metrics['lr'] = optimizer.param_groups[0]["lr"] if optimizer else 0.0
            
            # 获取权重（无论是否有metrics都需要获取，用于显示）
            weights = criterion.get_weights()
            circle_w = weights['circle']
            arcface_w = weights.get('subcenter_arcface', 0)
            training_state.metrics['weights'] = weights
            
            if metrics:
                # 挖掘器权重
                if 'hard_weight' in weights:
                    hard_w = weights['hard_weight']
                    ms_w = weights['ms_weight']
                    boundary_w = weights['boundary_weight']
                    circle_loss_val = circle_loss.item() if hasattr(circle_loss, 'item') else float(circle_loss)
                    metrics.update(loss.item(), circle_loss_val, circle_loss_val,
                                  circle_w, arcface_w, hard_w, ms_w, boundary_w)
                else:
                    circle_loss_val = circle_loss.item() if hasattr(circle_loss, 'item') else float(circle_loss)
                    metrics.update(loss.item(), circle_loss_val, circle_loss_val,
                                  circle_w, arcface_w)
                # 收集样本
                if len(training_state.metrics['all_embeddings']) < 1000:
                    training_state.metrics['all_embeddings'].append(embeddings.detach())
                    training_state.metrics['all_labels'].append(labels.detach())
            # 批次结束回调
            for callback in callbacks:
                callback.on_batch_end(training_state)
            # 每5个batch更新进度条
            if batch_idx % 5 == 0:
                current_avg_loss = training_state.metrics['total_loss'] / training_state.metrics['batch_count']
                progress_info = {
                    'Loss': f'{current_avg_loss:.4f}',
                    'Circle': f'{circle_w:.3f}',
                    'ArcFace': f'{arcface_w:.3f}',
                    'Boundary': f'{len(training_state.metrics["boundary_samples"])}',
                    'LR': f'{optimizer.param_groups[0]["lr"]:.1e}' if optimizer else 'N/A'
                }
                pbar.set_postfix(progress_info)
                # 日志记录详细进度
                if batch_idx % hyperparams.progress_interval == 0:
                    log_utils.debug(f"Epoch {epoch}, Batch {batch_idx}: "
                                   f"Loss={current_avg_loss:.4f}, "
                                   f"Boundary={len(training_state.metrics['boundary_samples'])}", tag="TRAIN")
    # 训练结束回调
    for callback in callbacks:
        callback.on_epoch_end(training_state)
    # 计算平均损失
    avg_loss = training_state.metrics['total_loss'] / max(training_state.metrics['batch_count'], 1)
    # 🔧 重构：移除训练阶段的特征指标计算，交由验证阶段处理
    # 保留基础统计信息以维持兼容性
    if metrics and training_state.metrics['all_embeddings']:
        # 只进行轻量级的统计更新，不执行复杂的特征分析
        metrics.set_current_epoch(epoch)
        # 简化边界样本统计
        if training_state.metrics['boundary_samples']:
            for boundary_result in training_state.metrics['boundary_samples']:
                metrics.update_boundary_stats(boundary_result)
    # 记录训练进度
    log_utils.info(f"Epoch {epoch} 完成: 平均损失={avg_loss:.4f}, "
                   f"边界样本={sum(br.boundary_count for br in training_state.metrics['boundary_samples']) if training_state.metrics['boundary_samples'] else 0}", tag="TRAIN")
    # 返回训练统计信息
    train_stats = {
        'boundary_samples': sum(br.boundary_count for br in training_state.metrics['boundary_samples']) if training_state.metrics['boundary_samples'] else 0,
        'total_batches': training_state.total_batches,
        'batch_count': training_state.metrics['batch_count']
    }
    return avg_loss, train_stats

class TrainingSamplerCallback(SamplerCallback):
    """训练过程中的采样回调 - 演示回调机制扩展性"""
    
    def __init__(self):
        self.epoch_sampling_stats = {}
        self.difficult_class_history = []
        self.current_epoch = 0  # 内部跟踪epoch
        self.epoch_logged = False  # 控制每个epoch只输出一次
        # 🔧 新增：挖掘器反馈支持
        self.criterion = None  # 将在外部设置
    
    def set_epoch(self, epoch: int):
        """设置当前epoch（由外部训练循环调用）"""
        if self.current_epoch != epoch:
            self.current_epoch = epoch
            self.epoch_logged = False  # 重置日志标志
    
    # TrainingCallback接口方法
    def on_epoch_start(self, training_state):
        """训练回调：轮次开始"""
        self.set_epoch(training_state.epoch)
        if not self.epoch_logged:
            log_utils.debug(f"Epoch {self.current_epoch} - 训练轮次开始", tag="TRAIN")
    
    def on_batch_start(self, training_state):
        """训练回调：批次开始"""
        pass  # 采样回调主要关注采样阶段，不需要在每个batch开始时执行
    
    def on_batch_end(self, training_state):
        """训练回调：批次结束"""
        pass  # 采样回调主要关注采样阶段，不需要在每个batch结束时执行
    
    def on_epoch_end(self, training_state):
        """训练回调：轮次结束"""
        if training_state.epoch % 10 == 0 and self.difficult_class_history:
            # 每10轮分析一次难度趋势
            recent_trend = self.difficult_class_history[-10:] if len(self.difficult_class_history) >= 10 else self.difficult_class_history
            if recent_trend:
                avg_hard_classes = sum(recent_trend) / len(recent_trend)
                log_utils.info(f"难度趋势分析: 近{len(recent_trend)}轮平均困难类别数 = {avg_hard_classes:.1f}", tag="TRAIN")
    
    # SamplerCallback接口方法
    def on_sampling_start(self, sampler, **kwargs):
        """采样开始时记录当前epoch信息"""
        if not self.epoch_logged:
            log_utils.debug(f"Epoch {self.current_epoch} - 开始自适应采样", tag="TRAIN")
            self.epoch_logged = True
    
    def on_difficulty_updated(self, sampler, difficulties):
        """记录类别难度变化趋势，并注入挖掘器反馈"""
        log_utils.info(f"🔧 DEBUG: 回调被调用，criterion={self.criterion is not None}")
        # 🔧 核心功能：挖掘器反馈注入
        if self.criterion and hasattr(sampler, 'model') and sampler.model:
            miner_info = self.criterion.get_latest_miner_info()
            if miner_info:
                # 使用挖掘器反馈更新难度
                from feature.adaptive_sampling.utils import DifficultyUpdater
                
                # 🔧 修复：获取class_ids
                class_ids = getattr(sampler, 'class_ids', list(difficulties.keys()))
                dataset = getattr(sampler, 'dataset', None)
                config = getattr(sampler, 'config', None)
                
                # 🔧 处理dataset缺失情况
                if dataset is None:
                    # 创建临时数据集对象用于兼容性
                    class TempDataset:
                        def __init__(self, difficulty_scores):
                            self.class_difficulty_scores = difficulty_scores
                    dataset = TempDataset(difficulties)
                
                enhanced_difficulties = DifficultyUpdater.update_from_model(
                    sampler.model, dataset, class_ids, 
                    config, miner_feedback=miner_info
                )
                
                # 🔧 更新采样器的难度分数（如果支持）
                if hasattr(sampler, 'class_difficulties'):
                    sampler.class_difficulties = enhanced_difficulties
                elif hasattr(sampler, 'update_difficulties'):
                    sampler.update_difficulties(enhanced_difficulties)
                
                # 🔧 详细的反馈日志
                boundary_ratio = miner_info.get('boundary_ratio', 0.0)
                hard_ratio = miner_info.get('hard_ratio', 0.0) 
                sample_separation = miner_info.get('sample_separation', 0.0)
                difficulty_signal = miner_info.get('difficulty_signal', 0.0)
                
                log_utils.info(
                    f"🔧 挖掘器反馈增强 - "
                    f"hard_ratio={hard_ratio:.3f}, "
                    f"boundary_ratio={boundary_ratio:.3f}, "
                    f"sample_separation={sample_separation:.3f}, "
                    f"difficulty_signal={difficulty_signal:.3f}, "
                    f"影响类别数={len(enhanced_difficulties)}",
                    tag="MINER_FEEDBACK"
                )
        
        # 原有功能：趋势分析
        hard_classes = [k for k, v in difficulties.items() if v > 1.0]
        self.difficult_class_history.append(len(hard_classes))
        
        # 每10轮分析一次难度趋势  
        if len(self.difficult_class_history) % 10 == 0:
            recent_trend = self.difficult_class_history[-10:]
            avg_hard_classes = sum(recent_trend) / len(recent_trend)
            log_utils.info(f"难度趋势分析: 近10轮平均困难类别数 = {avg_hard_classes:.1f}", tag="TRAIN")
    
    def on_class_allocation(self, sampler, allocation):
        """分析类别采样分配的合理性"""
        total_allocated = sum(allocation.values())
        high_allocation_classes = [k for k, v in allocation.items() if v >= sampler.config.max_m * 0.8]
        
        if len(high_allocation_classes) > len(allocation) * 0.5:
            log_utils.warning(f"高采样分配类别过多: {len(high_allocation_classes)}/{len(allocation)}", tag="TRAIN")
    
    def on_sampling_complete(self, sampler, indices):
        """采样完成后的质量检查"""
        unique_samples = len(set(indices))
        duplicate_ratio = 1 - (unique_samples / len(indices))
        
        log_utils.debug(f"采样重复率: {duplicate_ratio:.2%}", tag="TRAIN")

class ConfigurationManager:
    """配置管理器 - 应用协调层配置管理组件"""
    
    def __init__(self):
        self.train_config = train_config
        self.val_config = val_config
        self.stopping_config = stopping_config
        
    def get_hyperparameters_config(self):
        """获取超参数配置"""
        return self.train_config
        
    def get_data_config(self):
        """获取数据配置"""
        return self.train_config
        
    def get_environment_config(self):
        """获取环境配置"""
        return self.train_config
    
    def get_val_config(self):
        """获取验证配置"""
        return self.val_config
    
    def get_stopping_config(self):
        """获取早停配置"""
        return self.stopping_config
        
    def get_device(self):
        """根据配置获取设备"""
        device_preference = self.train_config.device_preference
        
        if device_preference == "auto":
            # 自动选择最佳设备
            if torch.backends.mps.is_available():
                device = torch.device("mps")
                if mp.current_process().name == 'MainProcess':
                    log_utils.info("Using MPS (Apple GPU) acceleration", tag="CONFIG")
            elif torch.cuda.is_available():
                device = torch.device("cuda")
                if mp.current_process().name == 'MainProcess':
                    log_utils.info("Using CUDA acceleration", tag="CONFIG")
            else:
                device = torch.device("cpu")
                if mp.current_process().name == 'MainProcess':
                    log_utils.info("Using CPU mode", tag="CONFIG")
        else:
            # 使用指定设备
            device = torch.device(device_preference)
            if mp.current_process().name == 'MainProcess':
                log_utils.info(f"Using specified device: {device_preference}", tag="CONFIG")
        
        return device


class DatasetManager:
    """数据集管理器 - 数据处理层组件"""
    
    def __init__(self, data_config):
        self.data_config = data_config
        self.train_dataset = None
        self.val_dataset = None
        
    def create_datasets(self, train_metadata_dir, val_metadata_dir, transform_train, transform_train_difficult, transform_validation):
        """创建训练和验证数据集"""
        log_utils.info("Loading datasets...", tag="DATASET_MANAGER")
        self.train_dataset = CatDataset(
            train_metadata_dir,
        transform=transform_train,
        transform_difficult=transform_train_difficult,
            difficulty_threshold=self.data_config.difficulty_threshold,
            enable_pseudo_difficult_detection=self.data_config.enable_pseudo_difficult_detection
        )
        self.val_dataset = CatDataset(val_metadata_dir, transform=transform_validation)
        
        log_utils.info(f"Training set: {len(self.train_dataset)} samples, {self.train_dataset.num_classes} classes", tag="DATASET_MANAGER")
        log_utils.info(f"Validation set: {len(self.val_dataset)} samples", tag="DATASET_MANAGER")
        
        return self.train_dataset, self.val_dataset
        
    def get_train_dataset(self):
        """获取训练数据集"""
        return self.train_dataset
        
    def get_val_dataset(self):
        """获取验证数据集"""
        return self.val_dataset
        
    def get_num_classes(self):
        """获取类别数量"""
        return self.train_dataset.num_classes if self.train_dataset else 0


class SampleSelector:
    """样本选择器 - 数据处理层组件"""
    
    def __init__(self, train_dataset):
        self.train_dataset = train_dataset
        self.adaptive_sampler = None
        
    def create_adaptive_sample_selector(self, hyperparams, criterion=None):
        """创建自适应样本选择器"""
        from feature.adaptive_sampling import create_adaptive_sampler
        training_callback = TrainingSamplerCallback()
        # 🔧 新增：连接criterion以支持挖掘器反馈
        training_callback.criterion = criterion
        self.adaptive_sampler = create_adaptive_sampler(
            dataset=self.train_dataset,
            model=None,
            initial_m=hyperparams.initial_m,
            max_m=hyperparams.max_m,
            hard_class_ratio=hyperparams.hard_class_ratio,
            sampling_mode=hyperparams.sampling_mode,
            length_before_new_iter=len(self.train_dataset),
            enable_monitoring=True,
            log_level="info"
        )
        self.adaptive_sampler.add_callback(training_callback)
        log_utils.info(f"已添加自定义采样回调，当前回调数量: {len(self.adaptive_sampler.callbacks)}", tag="SAMPLE_SELECTOR")
        return self.adaptive_sampler
    def update_model_reference(self, model):
        """更新模型引用以启用基于模型的难度评估"""
        if self.adaptive_sampler:
            self.adaptive_sampler.model = model
            log_utils.info("已将模型引用传递给采样器，启用基于模型的难度评估", tag="SAMPLE_SELECTOR")
            
    def log_sampling_statistics(self, epoch, hyperparams):
        """记录采样统计信息"""
        if epoch % hyperparams.sampling_stats_interval == 0:
            sampling_stats = self.adaptive_sampler.get_sampling_stats()
            difficulty_based_count = len([k for k, v in sampling_stats['difficulties'].items() if v > 1.0])
            from feature.adaptive_sampling.strategies import AdaptiveSamplingStrategy
            strategy = AdaptiveSamplingStrategy()
            hard_classes, _ = strategy._get_class_split(sampling_stats['difficulties'], self.adaptive_sampler.config)
            ratio_based_count = len(hard_classes)
            log_utils.info(f"Epoch {epoch+1} - 采样统计: 类别数={sampling_stats['class_count']}, 总样本数={sampling_stats['total_samples']}, 困难类别数={difficulty_based_count}(基于难度>1.0), 采样困难类别数={ratio_based_count}(基于比例{self.adaptive_sampler.config.hard_class_ratio:.2f})", tag="SAMPLE_SELECTOR")


class MetricsCollector:
    """指标收集器 - 数据处理层组件"""
    
    def __init__(self):
        self.metrics_calculator = None
        
    def initialize_metrics_calculator(self):
        """初始化指标计算器"""
        self.metrics_calculator = MetricsCalculator()
        return self.metrics_calculator
        
    def get_metrics_calculator(self):
        """获取指标计算器"""
        return self.metrics_calculator


class ModelPersistence:
    """模型持久化 - 数据处理层组件"""
    
    def __init__(self, output_dir="output"):
        self.output_dir = output_dir
        
    def save_epoch_model(self, model, optimizer, epoch, comprehensive_score, separation_ratio, val_acc, feature_analysis, criterion):
        """保存每轮模型"""
        log_utils.info(f"✅ 保存轮次 {epoch} 的模型...", tag="MODEL_PERSISTENCE")
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'comprehensive_score': comprehensive_score,
            'separation_ratio': separation_ratio,
            'val_acc': val_acc,
            'feature_analysis': feature_analysis,
            'criterion_state': criterion.state_dict() if hasattr(criterion, 'state_dict') else None,
            'metrics': {'train_loss': 0.0, 'val_acc': val_acc}
        }, f"{self.output_dir}/model_epoch_{epoch}.pth")
        log_utils.info(f"   ├─ 综合评分: {comprehensive_score:.4f} (验证准确率: {val_acc:.4f}, 分离比: {separation_ratio:.4f})", tag="MODEL_PERSISTENCE")
        
    def save_final_model(self, model, optimizer, epoch, separation_ratio, val_acc, feature_analysis):
        """保存最终模型"""
        torch.save({
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'separation_ratio': separation_ratio,
            'val_acc': val_acc,
            'feature_analysis': feature_analysis
        }, f"{self.output_dir}/final_feature_extractor.pth")
        log_utils.info(f"✓ 最终模型已保存 (epoch {epoch})", tag="MODEL_PERSISTENCE")



class CurriculumLearningManager:
    """课程学习管理器 - 业务逻辑层组件"""
    
    def __init__(self):
        self.curriculum_scheduler = None
        self.curriculum_sampler = None
        self.curriculum_monitor = None
        
    def initialize_curriculum_system(self):
        """初始化课程学习系统"""
        log_utils.info("初始化在线课程学习系统（集成优化恢复机制）...", tag="CURRICULUM_LEARNING")
        self.curriculum_scheduler, self.curriculum_sampler, self.curriculum_monitor = create_curriculum_learning_system(
            config=None,
            enable_monitoring=True,
            recovery_config=DEFAULT_RECOVERY_CONFIG  # 🚨 关键：启用优化的恢复配置
        )
        log_utils.info("课程学习系统初始化完成（已启用紧急恢复优化）", tag="CURRICULUM_LEARNING")
        
    def get_current_curriculum_state(self):
        """获取当前课程状态"""
        return self.curriculum_scheduler.get_current_curriculum_state()
        
    def should_update_curriculum(self, epoch):
        """判断是否需要更新课程"""
        return self.curriculum_scheduler.should_update_curriculum(epoch)
        
    def update_curriculum(self, epoch, performance_metrics, train_sampler=None, train_stats=None):
        """更新课程学习状态 - 集成恢复系统监控"""
        log_utils.info(f"更新课程学习状态 - 轮次 {epoch}", tag="CURRICULUM_LEARNING")

        # 🚨 关键：计算目标识别数量（用于崩溃检测）- 修复版
        recognition_count = 0

        # 优先使用边界样本数量
        if 'target_recognition_count' in performance_metrics and performance_metrics['target_recognition_count'] > 0:
            recognition_count = performance_metrics['target_recognition_count']
        elif train_stats and 'boundary_samples' in train_stats and train_stats['boundary_samples'] > 0:
            recognition_count = train_stats['boundary_samples']
        else:
            # 🎯 修复：基于验证准确率更合理地估算识别数量
            val_acc = performance_metrics.get('val_acc', 0.0)
            separation_ratio = performance_metrics.get('separation_ratio', 0.0)

            # 如果验证准确率 > 50% 且分离比 > 1.0，认为模型正常工作
            if val_acc > 0.5 and separation_ratio > 1.0:
                recognition_count = max(15, int(val_acc * 25))  # 至少15个识别
            else:
                recognition_count = int(val_acc * 30)  # 原估算逻辑

        # 获取总损失
        total_loss = performance_metrics.get('total_loss', 0.0)

        # 🚨 调用增强的更新方法（包含崩溃检测）
        self.curriculum_scheduler.update_curriculum(
            epoch, performance_metrics, recognition_count, total_loss
        )

        curriculum_state = self.curriculum_scheduler.get_current_curriculum_state()
        recovery_state = curriculum_state.get('recovery_state', 'normal')

        # 增强的状态日志
        log_utils.info(f"课程学习状态: 阶段={curriculum_state['stage']}, "
                      f"简单样本比例={curriculum_state['easy_ratio']:.2f}, "
                      f"平均难度={curriculum_state['difficulty_stats']['avg_difficulty']:.3f}, "
                      f"恢复状态={recovery_state}, "
                      f"识别数={recognition_count}", tag="CURRICULUM_LEARNING")
        
        if train_sampler and hasattr(train_sampler, 'update_curriculum_state'):
            train_sampler.update_curriculum_state(curriculum_state)
        if train_stats and hasattr(train_stats, 'sample_losses') and hasattr(train_stats, 'sample_ids'):
            self.curriculum_scheduler.update_sample_difficulties(
                train_stats['sample_ids'],
                losses=train_stats['sample_losses']
            )
            
    def receive_loss_feedback(self, loss_feedback, epoch):
        """接收损失反馈"""
        self.curriculum_scheduler.receive_loss_feedback(loss_feedback, epoch)


class FeatureExtractionPipeline:
    """特征提取管道 - 业务逻辑层组件"""
    
    def __init__(self, hyperparams, DEVICE):
        self.hyperparams = hyperparams
        self.DEVICE = DEVICE
        self.model = None
        
    def initialize_feature_extractor(self):
        """初始化特征提取模型"""
        log_utils.info("Initializing enhanced feature extractor...", tag="FEATURE_EXTRACTION")
        self.model = FeatureExtractor(embedding_dim=self.hyperparams.embedding_dim).to(self.DEVICE)
        return self.model
        
    def get_model(self):
        """获取模型实例"""
        return self.model


class LossComputationEngine:
    """损失计算引擎 - 业务逻辑层组件"""
    
    def __init__(self, num_classes, hyperparams):
        self.num_classes = num_classes
        self.hyperparams = hyperparams
        self.criterion = None
        
    def initialize_loss_function(self):
        """初始化多任务损失函数"""
        # 使用默认配置
        self.criterion = MultiTaskLoss(
            num_classes=self.num_classes,
            feat_dim=self.hyperparams.embedding_dim,
            loss_mode=self.hyperparams.loss_mode,
            circle_margin=self.hyperparams.circle_margin,
            circle_gamma=self.hyperparams.circle_gamma,
            confidence_threshold=self.hyperparams.confidence_threshold,
            loss_weight_config=self.hyperparams
        )
        return self.criterion
        
    def get_criterion(self):
        """获取损失函数实例"""
        return self.criterion


class GlobalAdjustmentCoordinator:
    """全局调整协调器 - 确保每个epoch只执行一种调整机制"""
    
    def __init__(self):
        self.epoch_locks = set()
        self.adjustment_log = {}
        
    def request_adjustment(self, epoch: int, adjustment_type: str, requester: str) -> bool:
        """请求执行调整，返回是否允许"""
        if epoch in self.epoch_locks:
            log_utils.info(f"🔒 Epoch {epoch}: 调整请求被拒绝 - {requester}:{adjustment_type} (已有调整执行)", 
                          tag="ADJUSTMENT_COORDINATOR")
            return False
        
        # 允许调整并锁定epoch
        self.epoch_locks.add(epoch)
        self.adjustment_log[epoch] = {'type': adjustment_type, 'requester': requester}
        log_utils.info(f"✅ Epoch {epoch}: 调整请求批准 - {requester}:{adjustment_type}", 
                      tag="ADJUSTMENT_COORDINATOR")
        
        # 清理过旧记录
        if len(self.epoch_locks) > 50:
            min_keep_epoch = epoch - 50
            self.epoch_locks = {e for e in self.epoch_locks if e >= min_keep_epoch}
            self.adjustment_log = {e: info for e, info in self.adjustment_log.items() if e >= min_keep_epoch}
        
        return True
    
    def is_epoch_locked(self, epoch: int) -> bool:
        """检查epoch是否已被锁定"""
        return epoch in self.epoch_locks
    
    def get_epoch_adjustment_info(self, epoch: int) -> Optional[Dict]:
        """获取epoch的调整信息"""
        return self.adjustment_log.get(epoch)


class ValidationOrchestrator:
    """验证协调器 - 应用协调层验证组件"""
    
    def __init__(self, device, metrics_calculator=None):
        self.device = device
        self.metrics_calculator = metrics_calculator
        self.validation_engine = None
        # 立即初始化验证引擎，避免运行时重复初始化
        self.initialize_validation_engine()
        
    def initialize_validation_engine(self):
        """初始化验证引擎"""
        from feature.validation_engine import ValidationEngine
        self.validation_engine = ValidationEngine(self.device, metrics_calculator=self.metrics_calculator)
        
    def coordinate_validation(self, model, val_loader, criterion, log_confusion=False, epoch=None):
        """协调验证流程 - 🎯 增强：集成视频场景验证"""
        # 执行传统验证
        traditional_results = self.validation_engine.validate(
            model, val_loader, criterion, log_confusion=log_confusion, epoch=epoch
        )

        # 🎯 新增：根据配置执行视频场景验证
        if (epoch is not None and
            train_config.enable_video_validation and
            epoch % train_config.video_validation_interval == 0):

            video_results = self._execute_video_scenario_validation(model, epoch)
            if video_results and 'error' not in video_results:
                # 将视频验证结果融入传统结果
                traditional_results['video_validation'] = video_results

                # 计算域适应评分
                domain_adaptation_score = self._compute_domain_adaptation_score(traditional_results, video_results)
                traditional_results['domain_adaptation_score'] = domain_adaptation_score

                log_utils.info(f"🎯 域适应评分: {domain_adaptation_score:.4f}", tag="DOMAIN_ADAPTATION")

        return traditional_results

    def _execute_video_scenario_validation(self, model, epoch):
        """执行视频场景验证"""
        try:
            # 从配置获取视频验证参数
            video_path = train_config.video_validation_path
            target_class_id = train_config.video_target_class_id
            frame_skip = train_config.video_frame_skip
            threshold = train_config.video_threshold

            # 检查视频文件是否存在
            if not os.path.exists(video_path):
                log_utils.warning(f"视频验证文件不存在: {video_path}", tag="VIDEO_VALIDATION")
                return None

            log_utils.info(f"🎬 第{epoch}轮执行视频场景验证", tag="VIDEO_VALIDATION")

            # 执行视频验证
            video_results = self.validation_engine.validate_with_video_scenario(
                model=model,
                video_path=video_path,
                target_class_id=target_class_id,
                frame_skip=frame_skip,
                threshold=threshold
            )

            return video_results

        except Exception as e:
            log_utils.error(f"视频场景验证失败: {e}", tag="VIDEO_VALIDATION")
            return None

    def _compute_domain_adaptation_score(self, traditional_results, video_results):
        """计算域适应评分"""
        try:
            # 获取传统验证指标
            val_accuracy = traditional_results.get('val_accuracy', 0)

            # 获取视频验证指标
            video_metrics = video_results.get('video_metrics', {})
            recognition_rate = video_metrics.get('recognition_rate', 0)
            video_score = video_metrics.get('video_validation_score', 0)

            # 计算域适应评分：传统验证与视频验证的一致性
            # 理想情况下，两者应该接近
            consistency_score = 1.0 - abs(val_accuracy - recognition_rate)

            # 综合评分：考虑一致性和视频表现
            domain_adaptation_score = (consistency_score * 0.6 + video_score * 0.4)

            return max(0.0, min(1.0, domain_adaptation_score))

        except Exception as e:
            log_utils.error(f"域适应评分计算失败: {e}", tag="DOMAIN_ADAPTATION")
            return 0.0


class OptimizationManager:
    """优化管理器 - 业务逻辑层组件"""
    
    def __init__(self, hyperparams):
        self.hyperparams = hyperparams
        self.optimizer = None
        self.scheduler = None
        self.meta_optimizer = None
        self.meta_monitor = None
        
    def initialize_optimizer(self, model):
        """初始化优化器和调度器"""
        from feature.optimizer_config import OptimizerConfig
        optimizer_config = OptimizerConfig()
        param_groups = optimizer_config.configure_optimizer_params(
            model,
            backbone_lr=self.hyperparams.backbone_lr,
            head_lr=self.hyperparams.head_lr,
            weight_decay=self.hyperparams.weight_decay,
            head_lr_ratio=self.hyperparams.head_lr_ratio,
            max_ratio=self.hyperparams.max_lr_ratio
        )

        # 初始化AdamW优化器
        self.optimizer = torch.optim.AdamW(param_groups, weight_decay=self.hyperparams.weight_decay)

        # 使用余弦退火调度器
        max_epochs = self.hyperparams.total_training_epochs
        scheduler_func = optimizer_config.create_warm_cosine_schedule_with_restart(max_epochs)
        self.scheduler = torch.optim.lr_scheduler.LambdaLR(
            self.optimizer,
            lr_lambda=scheduler_func
        )
        return self.optimizer, self.scheduler
        
    def initialize_meta_optimizer(self, criterion):
        """初始化元学习超参数优化器"""
        log_utils.info("初始化元学习超参数优化器...", tag="OPTIMIZATION")
        self.meta_optimizer, self.meta_monitor = create_meta_learning_system(config_type="default")
        
        # 基础超参数
        initial_hyperparams = {
            'backbone_lr': self.hyperparams.backbone_lr,
            'head_lr': self.hyperparams.head_lr,
            'weight_decay': self.hyperparams.weight_decay,
            'batch_size': self.hyperparams.batch_size if hasattr(self.hyperparams, 'batch_size') else 32,
        }
        
        # 只有在启用损失权重优化时才添加损失权重参数
        if getattr(self.hyperparams, 'enable_loss_weight_optimization', False):
            current_weights = criterion.get_weights()
            initial_hyperparams.update({
                'circle_weight': current_weights.get('circle', 0.7),
                'arcface_weight': current_weights.get('arcface', 0.3)
            })
            log_utils.info("元学习优化器已启用损失权重优化", tag="OPTIMIZATION")
        else:
            log_utils.info("元学习优化器已禁用损失权重优化，AdaptiveWeightController将作为唯一权重调整源", tag="OPTIMIZATION")
        
        self.meta_optimizer.initialize_params(initial_hyperparams)
        log_utils.info("元学习优化器初始化完成", tag="OPTIMIZATION")
        
    def should_optimize_meta_params(self, epoch):
        """判断是否需要进行元学习优化"""
        if self.meta_optimizer is None:
            return False
        return self.meta_optimizer.should_optimize(epoch)
        
    def optimize_meta_parameters(self, epoch, performance_metrics, criterion):
        """执行元学习超参数优化"""
        log_utils.info(f"执行元学习超参数优化 - 轮次 {epoch}", tag="OPTIMIZATION")
        optimized_params = self.meta_optimizer.optimize_hyperparameters(epoch, performance_metrics)
        if optimized_params != self.meta_optimizer.get_current_params():
            log_utils.info("应用元学习优化后的超参数", tag="OPTIMIZATION")
            self._apply_optimized_parameters(optimized_params, criterion)
            
    def _apply_optimized_parameters(self, optimized_params, criterion):
        """应用优化后的参数"""
        if 'backbone_lr' in optimized_params:
            for param_group in self.optimizer.param_groups:
                if 'backbone' in param_group.get('name', ''):
                    param_group['lr'] = optimized_params['backbone_lr']
        if 'head_lr' in optimized_params:
            for param_group in self.optimizer.param_groups:
                if 'head' in param_group.get('name', ''):
                    param_group['lr'] = optimized_params['head_lr']
        if 'weight_decay' in optimized_params:
            for param_group in self.optimizer.param_groups:
                param_group['weight_decay'] = optimized_params['weight_decay']
        
        # 只有在启用损失权重优化时才应用损失权重参数
        if getattr(self.hyperparams, 'enable_loss_weight_optimization', False):
            if 'circle_weight' in optimized_params:
                criterion.weight_controller.weights['circle'] = optimized_params['circle_weight']
            if 'arcface_weight' in optimized_params:
                criterion.weight_controller.weights['arcface'] = optimized_params['arcface_weight']
            criterion.weight_controller._normalize_weights()
        else:
            log_utils.info("损失权重优化已禁用，保持AdaptiveWeightController作为唯一权重调整源", tag="OPTIMIZATION")


class TrainingOrchestrator:
    """训练协调器 - 应用协调层核心组件"""

    def __init__(self):
        self._tqdm_bars = []
        self.config_manager = ConfigurationManager()
        self.dataset_manager = DatasetManager(self.config_manager.get_data_config())
        self.feature_extraction_pipeline = FeatureExtractionPipeline(self.config_manager.get_hyperparameters_config(), DEVICE)
        # 延迟LossComputationEngine的初始化，直到数据集加载完成
        self.loss_computation_engine = None
        self.optimization_manager = OptimizationManager(self.config_manager.get_hyperparameters_config())
        self.metrics_collector = MetricsCollector()
        self.device = DEVICE

    def orchestrate_training(self):
        """协调整个训练过程"""
        log_utils.info("开始训练协调", tag="TRAINING_ORCHESTRATOR")
        
        # 获取配置
        hyperparams = self.config_manager.get_hyperparameters_config()
        data_config = self.config_manager.get_data_config()
        env_config = self.config_manager.get_environment_config()
        val_config = self.config_manager.get_val_config()
        stopping_config = self.config_manager.get_stopping_config()
        
        # 设置随机种子
        from utils.deterministic_seed_manager import get_seed_manager
        seed_manager = get_seed_manager(hyperparams.random_seed)
        seed_manager.set_global_seed(hyperparams.random_seed)
        
        # 创建目录
        os.makedirs(data_config.output_dir, exist_ok=True)
        os.makedirs(data_config.logs_dir, exist_ok=True)
        
        # 训练参数
        max_epochs = hyperparams.total_training_epochs
        
        # 记录训练配置
        training_config_info = {
            'max_epochs': max_epochs,
            'batch_size': data_config.batch_size,
            'num_workers': data_config.num_workers,
            'pin_memory': data_config.pin_memory,
            'persistent_workers': data_config.persistent_workers,
            'prefetch_factor': data_config.prefetch_factor,
            'multiprocessing_context': data_config.multiprocessing_context
        }
        
        log_utils.info(f"Training configuration: {training_config_info}", tag="TRAINING_ORCHESTRATOR")
        
        # 初始化数据集（在模型初始化之前）
        train_metadata_dir = data_config.train_metadata_path
        val_metadata_dir = data_config.val_metadata_path
        transform_train, transform_train_difficult, transform_validation = create_transforms(data_config)
        
        self.dataset_manager.create_datasets(
            train_metadata_dir, val_metadata_dir, 
            transform_train, transform_train_difficult, transform_validation
        )
        
        # 🔧 修复：在数据集加载完成后再初始化损失函数，确保num_classes正确
        self.loss_computation_engine = LossComputationEngine(self.dataset_manager.get_num_classes(), self.config_manager.get_hyperparameters_config())
        
        # 初始化组件
        model = self.feature_extraction_pipeline.initialize_feature_extractor()
        criterion = self.loss_computation_engine.initialize_loss_function()
        optimizer, scheduler = self.optimization_manager.initialize_optimizer(model)
        # 初始化元学习优化器
        self.optimization_manager.initialize_meta_optimizer(criterion)
        
        # 初始化样本选择器
        sample_selector = SampleSelector(self.dataset_manager.get_train_dataset())
        adaptive_sampler = sample_selector.create_adaptive_sample_selector(hyperparams, criterion)
        
        # 🔧 关键修复：将模型引用传递给采样器，启用双向反馈机制
        sample_selector.update_model_reference(model)
        
        # 创建验证数据加载器（不需要自适应采样）
        val_batch_size = min(val_config.max_val_batch_size, int(data_config.batch_size * val_config.val_batch_size_factor))
        val_loader = DataLoader(
            self.dataset_manager.get_val_dataset(),
            batch_size=val_batch_size,
            shuffle=False,
            num_workers=data_config.num_workers,
            pin_memory=data_config.pin_memory,
            persistent_workers=data_config.persistent_workers,
            prefetch_factor=data_config.prefetch_factor,
            multiprocessing_context=data_config.multiprocessing_context
        )
        
        # 初始化早停机制
        early_stopping = EarlyStopping(
            config=stopping_config,
            patience=stopping_config.patience,
            delta=stopping_config.delta,
            min_epochs=stopping_config.early_stopping_min_epochs
        )
        
        # 初始化训练采样器回调
        sampler_callback = TrainingSamplerCallback()
        
        # 初始化模型持久化
        model_persistence = ModelPersistence(data_config.output_dir)
        
        # 初始化课程学习管理器
        curriculum_manager = CurriculumLearningManager()
        curriculum_manager.initialize_curriculum_system()
        
        # 初始化验证协调器
        validation_orchestrator = ValidationOrchestrator(self.device, self.metrics_collector.get_metrics_calculator())
        
        # 初始化全局调整协调器
        global_coordinator = GlobalAdjustmentCoordinator()
        
        # 确保所有组件都在正确的设备上
        model = model.to(self.device)
        criterion = criterion.to(self.device)
        
        # 训练循环
        best_comprehensive_score = 0.0
        best_separation_ratio = 0.0
        best_val_acc = 0.0
        best_feature_analysis = None
        
        for epoch in range(1, max_epochs + 1):
            log_utils.info(f"开始第 {epoch} 轮训练", tag="TRAINING_ORCHESTRATOR")
            
            # 设置epoch
            sampler_callback.set_epoch(epoch)
            
            # 更新采样器
            adaptive_sampler.set_epoch(epoch)
            train_loader = DataLoader(
                self.dataset_manager.get_train_dataset(),
                batch_size=data_config.batch_size,
                shuffle=False,
                num_workers=data_config.num_workers,
                pin_memory=data_config.pin_memory,
                persistent_workers=data_config.persistent_workers,
                prefetch_factor=data_config.prefetch_factor,
                multiprocessing_context=data_config.multiprocessing_context,
                sampler=adaptive_sampler
            )
            
            # 训练一个epoch
            train_loss, train_stats = train_epoch(
                model, train_loader, criterion, optimizer, epoch,
                metrics=self.metrics_collector.get_metrics_calculator(),
                scheduler=scheduler,
                callbacks=[sampler_callback],
                curriculum_state=curriculum_manager.get_current_curriculum_state()
            )
            
            # 验证
            val_results = validation_orchestrator.coordinate_validation(
                model, val_loader, criterion, log_confusion=False, epoch=epoch
            )
            
            # 🔧 修复：处理验证引擎返回的元组格式
            if isinstance(val_results, tuple) and len(val_results) >= 2:
                val_acc, feature_analysis, confusion_samples = val_results
                separation_ratio = feature_analysis.get('separation_ratio', 0.0)
                fisher_score = feature_analysis.get('fisher_score', 0.0)
                recall_at_5 = feature_analysis.get('structured_topk_metrics', {}).get('recall_at_5', 0.0)
            else:
                # 后备处理：假设是字典格式
                val_acc = val_results.get('val_accuracy', 0.0)
                separation_ratio = val_results.get('separation_ratio', 0.0)
                fisher_score = val_results.get('fisher_score', 0.0)
                recall_at_5 = val_results.get('recall_at_5', 0.0)
                feature_analysis = val_results.get('feature_analysis', {})
            
            # 计算综合得分
            comprehensive_score = calculate_comprehensive_model_score(
                val_acc, separation_ratio, fisher_score, recall_at_5
            )
            
            # 更新最佳模型
            # if comprehensive_score > best_comprehensive_score:
            best_comprehensive_score = comprehensive_score
            best_separation_ratio = separation_ratio
            best_val_acc = val_acc
            best_feature_analysis = feature_analysis
                
            # 保存最佳模型
            model_persistence.save_epoch_model(
                model, optimizer, epoch, comprehensive_score,
                separation_ratio, val_acc, feature_analysis, criterion
            )
            
            # 早停检查
            early_stopping.update_best_if_improved(
                val_acc, model, separation_ratio, epoch,
                weights=criterion.get_weights(),
                val_boundary_info=feature_analysis,
                shared_quality_score=feature_analysis.get('feature_quality_score', 0.5)
            )
            if early_stopping.should_stop():
                log_utils.info(f"Early stopping triggered at epoch {epoch}", tag="TRAINING_ORCHESTRATOR")
                break
            
            # 课程学习更新
            if curriculum_manager.should_update_curriculum(epoch):
                # 🚨 构造增强的性能指标字典（包含恢复系统所需指标）
                performance_metrics = {
                    'val_acc': val_acc,
                    'separation_ratio': separation_ratio,
                    'fisher_score': fisher_score,
                    'recall_at_5': recall_at_5,
                    'comprehensive_score': comprehensive_score,
                    'feature_quality_score': feature_analysis.get('feature_quality_score', 0.5),
                    'boundary_samples': feature_analysis.get('boundary_samples', 0),  # 🚨 关键指标
                    'total_loss': train_loss,
                    'target_recognition_count': train_stats.get('boundary_samples', 0)  # 🚨 目标识别数
                }
                curriculum_manager.update_curriculum(
                    epoch, performance_metrics, train_sampler=adaptive_sampler, train_stats=train_stats
                )
            
            # 元学习优化
            if self.optimization_manager.should_optimize_meta_params(epoch):
                self.optimization_manager.optimize_meta_parameters(epoch, performance_metrics, criterion)
            
            # 记录训练统计
            sample_selector.log_sampling_statistics(epoch, hyperparams)
            
            log_utils.info(f"Epoch {epoch} 完成: "
                          f"Train Loss={train_loss:.4f}, "
                          f"Val Acc={val_acc:.4f}, "
                          f"Separation={separation_ratio:.4f}, "
                          f"Fisher={fisher_score:.4f}, "
                          f"Comprehensive={comprehensive_score:.4f}", tag="TRAINING_ORCHESTRATOR")
        
        # 保存最终模型
        model_persistence.save_final_model(
            model, optimizer, max_epochs, best_separation_ratio, best_val_acc, best_feature_analysis
        )
        
        log_utils.info("训练协调完成", tag="TRAINING_ORCHESTRATOR")
        
        return {
            'best_comprehensive_score': best_comprehensive_score,
            'best_separation_ratio': best_separation_ratio,
            'best_val_acc': best_val_acc,
            'best_feature_analysis': best_feature_analysis
        }


def main():
    """主函数"""
    # torch.autograd.set_detect_anomaly(True)  # 异常检测已完成，关闭以提高性能
    
    # 创建训练协调器并开始训练
    orchestrator = TrainingOrchestrator()
    return orchestrator.orchestrate_training()


if __name__ == '__main__':
    main()
