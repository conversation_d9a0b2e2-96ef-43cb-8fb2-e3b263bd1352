# uuid: 3e4a427a-781a-45fd-a497-bfc3fef3b053

import os
import json
import logging
from pathlib import Path
from ultralytics import <PERSON>OLO
import cv2
from tqdm import tqdm

# --------------------- 配置路径 ---------------------
YOLO_MODEL_PATH = 'src/models/yolo.pt'
IMAGE_ROOT = 'output/data/processed_train'
OUTPUT_JSON_PATH = 'output/metadata/train_meta.json'
DETECTED_IMAGE_FORMATS = ('.jpg', '.png', '.jpeg')  # 允许的图像格式
LOG_FILE = 'output/logs/yolo_detection.log'

# --------------------- 设置日志 ---------------------
os.makedirs(os.path.dirname(LOG_FILE), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --------------------- 初始化YOLO模型 ---------------------
model = YOLO(YOLO_MODEL_PATH)

# --------------------- 主逻辑 ---------------------
def detect_cat_faces():
    all_metadata = []
    processed_classes = 0
    skipped_classes = 0
    error_classes = []

    class_dirs = [d for d in sorted(os.listdir(IMAGE_ROOT)) if os.path.isdir(os.path.join(IMAGE_ROOT, d))]
    logger.info(f"找到{len(class_dirs)}个类别文件夹")

    for class_id in tqdm(class_dirs, desc="Processing classes"):
        try:
            class_path = os.path.join(IMAGE_ROOT, class_id)
            image_files = sorted([
                f for f in os.listdir(class_path)
                if f.lower().endswith(DETECTED_IMAGE_FORMATS)
            ])

            if len(image_files) <= 0:
                logger.warning(f"⚠️ 类别 {class_id} 不包含图像，跳过。")
                skipped_classes += 1
                continue

            logger.info(f"处理类别: {class_id}, 包含{len(image_files)}张图片")
            
            class_images_meta = []
            max_area_box = None
            max_area = 0

            detection_count = 0
            for image_name in image_files:
                image_path = os.path.join(class_path, image_name)
                try:
                    results = model(image_path)

                    boxes = results[0].boxes
                    if boxes is None or len(boxes) == 0:
                        logger.warning(f"⚠️ 警告: {image_path} 未检测到猫头，将使用类别中的最大区域")
                        continue

                    # 找出当前图片中最大面积的框
                    current_box = max(boxes.xyxy, key=lambda box: (box[2] - box[0]) * (box[3] - box[1]))
                    current_bbox = current_box.tolist()
                    current_area = (current_bbox[2] - current_bbox[0]) * (current_bbox[3] - current_bbox[1])
                    
                    # 更新类别中的最大面积框
                    if current_area > max_area:
                        max_area = current_area
                        max_area_box = current_bbox
                    
                    class_images_meta.append({
                        "image_path": image_path,
                        "bbox": current_bbox
                    })
                    detection_count += 1
                except Exception as e:
                    logger.error(f"处理图片 {image_path} 时出错: {str(e)}")

            logger.info(f"类别 {class_id}: 成功检测{detection_count}/{len(image_files)}张图片")
            
            # 处理未检测到猫头的图片
            if max_area_box is not None:
                missing_count = 0
                for image_name in image_files:
                    image_path = os.path.join(class_path, image_name)
                    # 检查该图片是否已经处理过
                    if not any(meta["image_path"] == image_path for meta in class_images_meta):
                        logger.info(f"🔄 应用最大区域框到: {image_path}")
                        class_images_meta.append({
                            "image_path": image_path,
                            "bbox": max_area_box
                        })
                        missing_count += 1
                
                logger.info(f"类别 {class_id}: 对{missing_count}张未检测到的图片应用了最大区域框")
                
                # 添加到总元数据中
                all_metadata.append({
                    "class_id": class_id,
                    "images": class_images_meta
                })
                processed_classes += 1
            else:
                logger.error(f"❌ 类别 {class_id} 中没有任何图片检测到猫头，跳过该类别")
                error_classes.append(class_id)
                skipped_classes += 1
        except Exception as e:
            logger.error(f"处理类别 {class_id} 时出错: {str(e)}")
            error_classes.append(class_id)
            skipped_classes += 1

    # 写入JSON
    os.makedirs(os.path.dirname(OUTPUT_JSON_PATH), exist_ok=True)
    with open(OUTPUT_JSON_PATH, 'w') as f:
        json.dump(all_metadata, f, indent=2, ensure_ascii=False)

    logger.info(f"✅ 完成：共处理{len(class_dirs)}个类别")
    logger.info(f"- 成功写入{processed_classes}个类别数据到{OUTPUT_JSON_PATH}")
    logger.info(f"- 跳过{skipped_classes}个类别")
    
    if error_classes:
        logger.warning(f"- 出错的类别: {', '.join(error_classes)}")
    
    return all_metadata

# --------------------- 运行 ---------------------
if __name__ == '__main__':
    detect_cat_faces()