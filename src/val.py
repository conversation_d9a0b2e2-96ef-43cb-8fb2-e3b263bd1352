import os
import json
from glob import glob
from pathlib import Path
import logging
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE

import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import timm
import torch.nn as nn
import torch.optim as optim
from tqdm import tqdm
from torch.nn.functional import normalize
import numpy as np
from collections import defaultdict
import random
import time

METADATA_DIR = "output/metadata"
VAL_DIR = "output/data/processed_val"
BATCH_SIZE = 8
EPOCHS = 10
NUM_WORKERS = 2
IMG_SIZE = 224
DEVICE = "mps" if torch.backends.mps.is_available() else "cuda" if torch.cuda.is_available() else "cpu"

# 创建logs目录
os.makedirs("logs", exist_ok=True)

# 生成带时间戳的日志文件名
log_filename = f"logs/val_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

# 配置日志系统 - 同时输出到控制台和文件
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()
    ]
)
logging.info(f"日志将同时保存到: {log_filename}")

os.makedirs("output", exist_ok=True)

transform = transforms.Compose([
    transforms.Resize((IMG_SIZE, IMG_SIZE)),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406],
                         [0.229, 0.224, 0.225])
])

class CatDataset(Dataset):
    def __init__(self, json_path, transform=None):
        self.samples = []
        self.transform = transform
        
        with open(json_path, 'r') as f:
            data = json.load(f)
            for item in data:
                class_id = item["class_id"]
                for image_info in item["images"]:
                    self.samples.append((image_info["image_path"], class_id))
                    
        self.class_ids = sorted(list(set(label for _, label in self.samples)))
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.class_ids)}

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        image_path, label = self.samples[idx]
        image = Image.open(image_path).convert("RGB")
        if self.transform:
            image = self.transform(image)
        return image, image_path

def create_base_model():
    """创建预训练的EfficientNet-B2基础模型"""
    if not hasattr(create_base_model, '_model'):
        create_base_model._model = timm.create_model('efficientnet_b2', pretrained=True)
    return create_base_model._model

def get_model():
    """获取完整的模型"""
    base_model = create_base_model()
    return nn.Sequential(*list(base_model.children())[:-1])  # 去掉分类器层

def extract_features(model, dataloader):
    """提取图像特征向量
    
    Args:
        model: 预训练的特征提取模型
        dataloader: 包含图像数据的DataLoader
        
    Returns:
        features: 字典,key为图像路径,value为特征向量
    """
    # 将模型设置为评估模式
    model.eval()
    features = {}
    
    # 关闭梯度计算以加快推理速度
    with torch.no_grad():
        for images, paths in tqdm(dataloader, desc="Extracting Features", ncols=100):
            # 将图像数据移至目标设备
            images = images.to(DEVICE)
            
            # 提取特征并移除空间维度(H/W)
            outputs = model(images).squeeze(-1).squeeze(-1)  # 去除 H/W
            
            # L2归一化特征向量
            outputs = normalize(outputs, p=2, dim=1)  # L2 normalize
            
            # 将特征向量存入字典
            for path, feat in zip(paths, outputs.cpu()):
                features[path] = feat.tolist()
                
    return features

def compute_avg_sim(features, dataset):
    """统计每类图像特征的平均相似度"""
    from sklearn.metrics.pairwise import cosine_similarity

    class_to_feats = defaultdict(list)
    for image_path, label in dataset.samples:
        class_to_feats[label].append(features[image_path])

    for class_id, feats in class_to_feats.items():
        if len(feats) < 2:
            continue
        sims = cosine_similarity(feats)
        upper_triangle = sims[np.triu_indices_from(sims, k=1)]
        avg_sim = np.mean(upper_triangle)
        # logging.info(f"Class {class_id} 平均相似度: {avg_sim:.4f}")

def visualize_features(features, dataset, title="Feature Visualization"):
    """使用t-SNE可视化特征空间"""
    from collections import defaultdict
    import random
    import time

    max_per_class = 5
    class_to_items = defaultdict(list)
    for path, label in dataset.samples:
        if path in features:
            class_to_items[label].append((path, features[path]))

    vectors, labels = [], []
    # 设置确定性种子用于可重现的采样
    random.seed(42)
    for cls, items in class_to_items.items():
        sampled = random.sample(items, min(len(items), max_per_class))
        for path, vec in sampled:
            vectors.append(vec)
            labels.append(cls)

    # Convert vectors to a NumPy array
    vectors = np.array(vectors)

    tsne = TSNE(n_components=2, random_state=42, perplexity=5)
    start = time.time()
    reduced = tsne.fit_transform(vectors)
    print(f"[TSNE] done in {time.time() - start:.2f}s")

    plt.figure(figsize=(10, 8))
    # Convert labels to integers
    labels = [int(label) for label in labels]
    scatter = plt.scatter(reduced[:, 0], reduced[:, 1], c=labels, cmap='tab10', alpha=0.7)
    plt.title(title)
    plt.colorbar(scatter, label='Class ID')
    plt.grid(True)
    plt.savefig("output/feature_tsne_train.png")
    plt.close()

def main():
    train_dataset = CatDataset("output/metadata/train_meta.json", transform=transform)
    val_dataset = CatDataset("output/metadata/val_meta.json", transform=transform)
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=NUM_WORKERS)

    model = get_model().to(DEVICE)

    # 提取特征
    train_feats = extract_features(model, train_loader)
    val_feats = extract_features(model, val_loader)

    # 保存特征
    with open("output/features_train.json", "w") as f:
        json.dump(train_feats, f, indent=2)
    with open("output/features_val.json", "w") as f:
        json.dump(val_feats, f, indent=2)

    # 评估特征表现
    compute_avg_sim(train_feats, train_dataset)
    compute_avg_sim(val_feats, val_dataset)

    visualize_features(train_feats, train_dataset)

if __name__ == "__main__":
    main()