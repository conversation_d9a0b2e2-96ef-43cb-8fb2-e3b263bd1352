"""
统一特征归一化工具模块

本模块提供了项目中所有特征归一化操作的统一接口，解决了原有代码中
归一化操作分散、维护成本高、逻辑注入难等问题。

主要功能：
1. 统一的L2归一化接口
2. 安全的归一化检查
3. 可配置的归一化策略
4. 支持训练/验证阶段切换
5. 模长调控和重标定
"""

import torch
import torch.nn.functional as F
from typing import Optional, Union
import logging

# 获取日志记录器
logger = logging.getLogger(__name__)


def l2_normalize(x: torch.Tensor, p: int = 2, dim: int = 1, eps: float = 1e-12) -> torch.Tensor:
    """
    标准L2归一化函数
    
    Args:
        x: 输入特征张量
        p: 范数类型，默认为2（L2范数）
        dim: 归一化维度，默认为1（特征维度）
        eps: 数值稳定性参数，防止除零
        
    Returns:
        归一化后的特征张量，模长为1
    """
    return F.normalize(x, p=p, dim=dim, eps=eps)


def safe_l2_normalize(x: torch.Tensor, p: int = 2, dim: int = 1, eps: float = 1e-12, 
                     assert_norm: bool = False, tolerance: float = 1e-3) -> torch.Tensor:
    """
    安全的L2归一化函数，可选启用模长检查
    
    Args:
        x: 输入特征张量
        p: 范数类型，默认为2（L2范数）
        dim: 归一化维度，默认为1（特征维度）
        eps: 数值稳定性参数
        assert_norm: 是否启用模长检查
        tolerance: 模长检查容差
        
    Returns:
        归一化后的特征张量
        
    Raises:
        AssertionError: 当启用模长检查且归一化结果不符合预期时
    """
    out = F.normalize(x, p=p, dim=dim, eps=eps)
    
    if assert_norm:
        norm = torch.norm(out, p=p, dim=dim)
        expected_norm = torch.ones_like(norm)
        
        if not torch.allclose(norm, expected_norm, atol=tolerance):
            mean_norm = norm.mean().item()
            logger.warning(f"特征归一化检查失败！平均模长: {mean_norm:.6f}, 期望: 1.0")
            if assert_norm:
                raise AssertionError(f"Feature norm not close to 1! Mean norm = {mean_norm:.6f}")
    
    return out


class FeatureNormalizer:
    """
    高级特征归一化器，支持复杂策略和配置
    
    功能特性：
    - 可配置的归一化参数
    - 模长重标定支持
    - 训练/验证阶段切换
    - 调试和断言检查
    - 统计信息收集
    """
    
    def __init__(self, 
                 p: int = 2, 
                 dim: int = 1, 
                 eps: float = 1e-12,
                 scale: Optional[float] = None,
                 enable_assert: bool = False,
                 tolerance: float = 1e-3,
                 training_mode: bool = True,
                 validation_mode: bool = True,
                 collect_stats: bool = False):
        """
        初始化特征归一化器
        
        Args:
            p: 范数类型
            dim: 归一化维度
            eps: 数值稳定性参数
            scale: 模长重标定因子，None表示不重标定
            enable_assert: 是否启用断言检查
            tolerance: 断言检查容差
            training_mode: 训练阶段是否启用归一化
            validation_mode: 验证阶段是否启用归一化
            collect_stats: 是否收集统计信息
        """
        self.p = p
        self.dim = dim
        self.eps = eps
        self.scale = scale
        self.enable_assert = enable_assert
        self.tolerance = tolerance
        self.training_mode = training_mode
        self.validation_mode = validation_mode
        self.collect_stats = collect_stats
        
        # 统计信息
        self.stats = {
            'call_count': 0,
            'total_samples': 0,
            'mean_input_norm': 0.0,
            'mean_output_norm': 0.0,
            'norm_violations': 0
        } if collect_stats else None
        
        logger.info(f"FeatureNormalizer初始化: p={p}, dim={dim}, scale={scale}")
    
    def __call__(self, x: torch.Tensor, training: Optional[bool] = None) -> torch.Tensor:
        """
        执行特征归一化
        
        Args:
            x: 输入特征张量
            training: 是否为训练模式，None表示自动检测
            
        Returns:
            归一化后的特征张量
        """
        # 确定当前模式
        if training is None:
            training = x.requires_grad
        
        # 检查是否应该执行归一化
        if training and not self.training_mode:
            return x
        if not training and not self.validation_mode:
            return x
        
        # 收集输入统计信息
        if self.collect_stats:
            self._update_input_stats(x)
        
        # 执行归一化
        x_norm = F.normalize(x, p=self.p, dim=self.dim, eps=self.eps)
        
        # 模长重标定
        if self.scale is not None:
            x_norm = x_norm * self.scale
        
        # 收集输出统计信息
        if self.collect_stats:
            self._update_output_stats(x_norm)
        
        # 断言检查
        if self.enable_assert:
            self._check_normalization(x_norm)
        
        return x_norm
    
    def _update_input_stats(self, x: torch.Tensor):
        """更新输入统计信息"""
        self.stats['call_count'] += 1
        batch_size = x.size(0)
        self.stats['total_samples'] += batch_size
        
        input_norms = torch.norm(x, p=self.p, dim=self.dim)
        mean_input_norm = input_norms.mean().item()
        
        # 增量更新均值
        total_calls = self.stats['call_count']
        self.stats['mean_input_norm'] = (
            (self.stats['mean_input_norm'] * (total_calls - 1) + mean_input_norm) / total_calls
        )
    
    def _update_output_stats(self, x: torch.Tensor):
        """更新输出统计信息"""
        output_norms = torch.norm(x, p=self.p, dim=self.dim)
        mean_output_norm = output_norms.mean().item()
        
        # 增量更新均值
        total_calls = self.stats['call_count']
        self.stats['mean_output_norm'] = (
            (self.stats['mean_output_norm'] * (total_calls - 1) + mean_output_norm) / total_calls
        )
    
    def _check_normalization(self, x: torch.Tensor):
        """检查归一化结果"""
        norms = torch.norm(x, p=self.p, dim=self.dim)
        expected_norm = self.scale if self.scale is not None else 1.0
        expected_norms = torch.full_like(norms, expected_norm)
        
        if not torch.allclose(norms, expected_norms, atol=self.tolerance):
            if self.collect_stats:
                self.stats['norm_violations'] += 1
            
            mean_norm = norms.mean().item()
            logger.warning(f"归一化检查失败！平均模长: {mean_norm:.6f}, 期望: {expected_norm}")
            
            if self.enable_assert:
                raise AssertionError(
                    f"Normalization check failed! Mean norm = {mean_norm:.6f}, "
                    f"Expected = {expected_norm}"
                )
    
    def get_stats(self) -> dict:
        """获取统计信息"""
        if not self.collect_stats:
            return {}
        return self.stats.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        if self.collect_stats:
            self.stats = {
                'call_count': 0,
                'total_samples': 0,
                'mean_input_norm': 0.0,
                'mean_output_norm': 0.0,
                'norm_violations': 0
            }
    
    def set_training_mode(self, training: bool):
        """设置训练模式"""
        self.training_mode = training
        logger.debug(f"FeatureNormalizer训练模式设置为: {training}")
    
    def set_validation_mode(self, validation: bool):
        """设置验证模式"""
        self.validation_mode = validation
        logger.debug(f"FeatureNormalizer验证模式设置为: {validation}")
    
    def set_scale(self, scale: Optional[float]):
        """设置模长重标定因子"""
        self.scale = scale
        logger.info(f"FeatureNormalizer模长重标定因子设置为: {scale}")


# 全局归一化器实例（可选使用）
_global_normalizer: Optional[FeatureNormalizer] = None


def get_global_normalizer() -> FeatureNormalizer:
    """获取全局归一化器实例"""
    global _global_normalizer
    if _global_normalizer is None:
        _global_normalizer = FeatureNormalizer()
        logger.info("创建全局FeatureNormalizer实例")
    return _global_normalizer


def set_global_normalizer(normalizer: FeatureNormalizer):
    """设置全局归一化器实例"""
    global _global_normalizer
    _global_normalizer = normalizer
    logger.info("设置全局FeatureNormalizer实例")


def normalize_with_global(x: torch.Tensor, training: Optional[bool] = None) -> torch.Tensor:
    """使用全局归一化器进行归一化"""
    return get_global_normalizer()(x, training)


# 便捷函数：从配置创建归一化器
def create_normalizer_from_config(config) -> FeatureNormalizer:
    """
    从配置对象创建归一化器
    
    Args:
        config: 配置对象，应包含l2_norm_p和l2_norm_dim属性
        
    Returns:
        配置好的FeatureNormalizer实例
    """
    p = getattr(config, 'l2_norm_p', 2)
    dim = getattr(config, 'l2_norm_dim', 1)
    
    # 检查是否有多尺度融合配置
    if hasattr(config, 'normalize_p'):
        p = config.normalize_p
    if hasattr(config, 'normalize_dim'):
        dim = config.normalize_dim
    
    normalizer = FeatureNormalizer(p=p, dim=dim)
    logger.info(f"从配置创建FeatureNormalizer: p={p}, dim={dim}")
    
    return normalizer


# 兼容性函数：保持与原有F.normalize调用的兼容性
def normalize(x: torch.Tensor, p: int = 2, dim: int = 1, eps: float = 1e-12) -> torch.Tensor:
    """
    兼容性归一化函数，与F.normalize接口一致
    
    这个函数提供了与原有F.normalize调用完全兼容的接口，
    便于进行渐进式重构。
    """
    return F.normalize(x, p=p, dim=dim, eps=eps) 