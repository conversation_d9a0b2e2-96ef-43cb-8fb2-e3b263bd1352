import os
import logging
from logging.handlers import RotatingFileHandler
import time
import sys
from pathlib import Path

# 获取项目根目录和日志目录
ROOT_DIR = Path(os.path.abspath(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
LOG_DIR = ROOT_DIR / "output" / "logs"
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 生成日志文件名，使用固定日期，这样就会追加到同一个文件
log_file = LOG_DIR / f"app_{time.strftime('%Y%m%d')}.log"

# 创建全局logger
logger = logging.getLogger("global")
logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别，允许所有日志级别

# 防止重复添加处理器
if not logger.handlers:
    # 创建文件处理器 - 使用RotatingFileHandler，限制单个日志文件大小
    file_handler = RotatingFileHandler(
        filename=log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8"
    )
    file_handler.setLevel(logging.DEBUG)  # 确保文件处理器也是DEBUG级别

    # 创建控制台处理器 - 使用stderr避免与tqdm冲突
    console_handler = logging.StreamHandler(sys.stderr)
    console_handler.setLevel(logging.INFO)  # 设置控制台处理器为INFO级别，减少输出干扰

    # 设置日志格式 - 简洁美观的格式，移除冗余信息
    # 格式说明：
    # - %(asctime)s: 时间戳
    # - %(levelname)s: 日志级别 (INFO/WARNING/ERROR等)
    # - %(message)s: 实际消息内容 (已包含TAG信息)
    formatter = logging.Formatter(
        "[%(asctime)s] [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

# 防止日志重复输出
logger.propagate = False

def get_logger():
    """获取全局日志实例"""
    return logger

# 为了便于使用，直接提供日志级别函数
def debug(msg, *args, tag=None, **kwargs):
    _log_with_tag(logger.debug, msg, tag, *args, **kwargs)

def info(msg, *args, tag=None, **kwargs):
    _log_with_tag(logger.info, msg, tag, *args, **kwargs)

def warning(msg, *args, tag=None, **kwargs):
    _log_with_tag(logger.warning, msg, tag, *args, **kwargs)

def error(msg, *args, tag=None, **kwargs):
    _log_with_tag(logger.error, msg, tag, *args, **kwargs)

def critical(msg, *args, tag=None, **kwargs):
    _log_with_tag(logger.critical, msg, tag, *args, **kwargs)

def exception(msg, *args, tag=None, **kwargs):
    """记录异常信息，包含堆栈跟踪"""
    _log_with_tag(logger.exception, msg, tag, *args, **kwargs)

def _log_with_tag(log_func, msg, tag, *args, **kwargs):
    """内部函数：处理带tag的日志消息
    
    Args:
        log_func: 日志函数(debug, info, warning, error, critical, exception)
        msg: 日志消息
        tag: 可选标签，用于标识日志来源模块或功能
        *args, **kwargs: 传递给日志函数的其他参数
        
    Examples:
        log_utils.info("训练开始", tag="TRAIN")
        log_utils.warning("验证准确率下降", tag="VALIDATION") 
        log_utils.error("挖掘器异常", tag="MINER")
    """
    if tag:
        formatted_msg = f"[{tag}] {msg}"
    else:
        formatted_msg = msg
    log_func(formatted_msg, *args, **kwargs)

# 允许自定义日志级别
def set_level(level):
    """设置日志级别

    Args:
        level: logging模块中定义的级别，如logging.DEBUG, logging.INFO等
    """
    logger.setLevel(level)
    for handler in logger.handlers:
        handler.setLevel(level)