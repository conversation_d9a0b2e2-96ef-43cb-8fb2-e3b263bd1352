"""
并行管理器

提供多线程和多进程并行处理能力，支持：
1. 线程池管理
2. 进程池管理  
3. 异步任务执行
4. 资源监控和清理
"""

import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import Callable, Any, List, Dict, Optional, Tuple
import time
import queue
from . import log_utils


class ParallelManager:
    """
    并行处理管理器 - 统一管理线程池、进程池和异步任务队列
    
    功能：
    1. 线程池管理 (ThreadPoolExecutor)
    2. 进程池管理 (ProcessPoolExecutor) 
    3. 异步任务队列 (Queue + Worker Thread)
    4. 并行状态监控和性能统计
    5. 资源生命周期管理
    """
    
    def __init__(self, max_workers: int = None, enable_async_analysis: bool = True, 
                 thread_name_prefix: str = "ParallelManager"):
        """
        初始化并行处理管理器
        
        Args:
            max_workers: 最大工作线程数，默认为min(8, cpu_count())
            enable_async_analysis: 是否启用异步分析
            thread_name_prefix: 线程名称前缀
        """
        self.max_workers = max_workers or min(8, mp.cpu_count())
        self.enable_async_analysis = enable_async_analysis
        self.thread_name_prefix = thread_name_prefix
        
        # 线程池和进程池
        self._thread_pool = None
        self._process_pool = None
        
        # 异步任务队列
        self._analysis_queue = queue.Queue()
        self._analysis_thread = None
        self._task_handlers = {}  # 任务类型到处理函数的映射
        
        # 并行状态管理
        self._parallel_state_lock = threading.Lock()
        self._async_results = []
        self._is_initialized = False
        self._is_shutdown = False
        
        # 性能统计
        self._stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0.0,
            'avg_execution_time': 0.0
        }
        
        log_utils.info(f"🚀 并行处理管理器初始化 (最大工作线程: {self.max_workers})")
    
    def __del__(self):
        """析构函数：确保资源清理"""
        try:
            self.shutdown()
        except Exception:
            pass  # 忽略析构时的异常
    
    def initialize(self):
        """初始化线程池和进程池"""
        if self._is_initialized:
            return
        
        try:
            # 初始化线程池
            if self._thread_pool is None:
                self._thread_pool = ThreadPoolExecutor(
                    max_workers=self.max_workers, 
                    thread_name_prefix=self.thread_name_prefix
                )
            
            # 初始化进程池（仅在多核环境下）
            if self._process_pool is None and self.max_workers > 1:
                self._process_pool = ProcessPoolExecutor(
                    max_workers=min(4, self.max_workers)
                )
            
            # 启动异步分析线程
            if self.enable_async_analysis and self._analysis_thread is None:
                self._analysis_thread = threading.Thread(
                    target=self._async_analysis_worker, 
                    daemon=True,
                    name=f"{self.thread_name_prefix}_AsyncWorker"
                )
                self._analysis_thread.start()
            
            self._is_initialized = True
            log_utils.info(f"🔧 并行处理管理器初始化完成 (线程池: {self.max_workers}, 进程池: {min(4, self.max_workers) if self.max_workers > 1 else 0})")
            
        except Exception as e:
            log_utils.error(f"并行处理管理器初始化失败: {e}")
            raise
    
    def shutdown(self, wait: bool = True, timeout: float = 5.0):
        """关闭并行处理管理器，清理所有资源"""
        if self._is_shutdown:
            return
        
        try:
            # 停止异步分析线程
            if self._analysis_thread and self._analysis_thread.is_alive():
                self._analysis_queue.put(None)  # 发送停止信号
                if wait:
                    self._analysis_thread.join(timeout=timeout)
            
            # 关闭线程池
            if self._thread_pool:
                self._thread_pool.shutdown(wait=wait)
                self._thread_pool = None
            
            # 关闭进程池
            if self._process_pool:
                self._process_pool.shutdown(wait=wait)
                self._process_pool = None
            
            # 等待异步结果完成
            if wait:
                for future in self._async_results:
                    try:
                        future.result(timeout=1.0)
                    except Exception as e:
                        log_utils.error(f"异步任务完成时出现异常: {e}")
            
            self._async_results.clear()
            self._is_shutdown = True
            log_utils.info("🧹 并行处理管理器资源清理完成")
            
        except Exception as e:
            log_utils.error(f"清理并行处理管理器时出现错误: {e}")
    
    def register_task_handler(self, task_type: str, handler: Callable):
        """注册异步任务处理函数"""
        self._task_handlers[task_type] = handler
        log_utils.debug(f"注册异步任务处理器: {task_type}")
    
    def submit_thread_task(self, func: Callable, *args, **kwargs):
        """提交线程池任务"""
        if not self._is_initialized:
            self.initialize()
        
        if self._thread_pool is None:
            raise RuntimeError("线程池未初始化")
        
        start_time = time.time()
        future = self._thread_pool.submit(func, *args, **kwargs)
        
        # 包装future以收集统计信息
        def _collect_stats(f):
            try:
                result = f.result()
                execution_time = time.time() - start_time
                with self._parallel_state_lock:
                    self._stats['tasks_completed'] += 1
                    self._stats['total_execution_time'] += execution_time
                    self._stats['avg_execution_time'] = (
                        self._stats['total_execution_time'] / self._stats['tasks_completed']
                    )
                return result
            except Exception as e:
                with self._parallel_state_lock:
                    self._stats['tasks_failed'] += 1
                raise e
        
        # 添加回调
        future.add_done_callback(lambda f: _collect_stats(f))
        
        with self._parallel_state_lock:
            self._stats['tasks_submitted'] += 1
        
        return future
    
    def submit_process_task(self, func: Callable, *args, **kwargs):
        """提交进程池任务"""
        if not self._is_initialized:
            self.initialize()
        
        if self._process_pool is None:
            raise RuntimeError("进程池未初始化或不可用")
        
        start_time = time.time()
        future = self._process_pool.submit(func, *args, **kwargs)
        
        with self._parallel_state_lock:
            self._stats['tasks_submitted'] += 1
        
        return future
    
    def submit_async_task(self, task_type: str, *args):
        """提交异步任务到队列"""
        if not self._is_initialized:
            self.initialize()
        
        if not self.enable_async_analysis:
            raise RuntimeError("异步分析未启用")
        
        if task_type not in self._task_handlers:
            raise ValueError(f"未注册的任务类型: {task_type}")
        
        self._analysis_queue.put((task_type, args))
        # log_utils.debug(f"提交异步任务: {task_type}")
    
    def wait_async_tasks(self, timeout: float = None):
        """等待所有异步任务完成"""
        if self.enable_async_analysis and self._analysis_queue:
            if timeout:
                # 使用超时等待
                start_time = time.time()
                while not self._analysis_queue.empty():
                    if time.time() - start_time > timeout:
                        log_utils.warning(f"异步任务等待超时 ({timeout}s)")
                        break
                    time.sleep(0.1)
            else:
                # 无超时等待
                self._analysis_queue.join()
    
    def submit_parallel_tasks(self, func: Callable, task_args_list: List[Tuple], 
                            use_process_pool: bool = False, timeout: float = None):
        """批量提交并行任务并收集结果"""
        if not self._is_initialized:
            self.initialize()
        
        pool = self._process_pool if use_process_pool else self._thread_pool
        if pool is None:
            raise RuntimeError(f"{'进程池' if use_process_pool else '线程池'}未初始化")
        
        # 提交所有任务
        futures = []
        for args in task_args_list:
            if isinstance(args, tuple):
                future = pool.submit(func, *args)
            else:
                future = pool.submit(func, args)
            futures.append(future)
        
        # 收集结果
        results = []
        for future in as_completed(futures, timeout=timeout):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                log_utils.error(f"并行任务执行失败: {e}")
                results.append(None)
        
        return results
    
    def _async_analysis_worker(self):
        """异步分析工作线程"""
        log_utils.debug("异步分析工作线程启动")
        
        while True:
            try:
                task = self._analysis_queue.get(timeout=1.0)
                if task is None:  # 停止信号
                    log_utils.debug("收到停止信号，异步分析工作线程退出")
                    break
                
                task_type, args = task
                
                if task_type in self._task_handlers:
                    try:
                        handler = self._task_handlers[task_type]
                        handler(*args)
                    except Exception as e:
                        log_utils.error(f"异步任务 {task_type} 执行失败: {e}")
                else:
                    log_utils.warning(f"未知的异步任务类型: {task_type}")
                
                self._analysis_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                log_utils.error(f"异步分析工作线程错误: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._parallel_state_lock:
            stats = self._stats.copy()
        
        stats.update({
            'parallel_config': {
                'max_workers': self.max_workers,
                'async_analysis_enabled': self.enable_async_analysis,
                'thread_name_prefix': self.thread_name_prefix
            },
            'pool_status': {
                'thread_pool_active': self._thread_pool is not None,
                'process_pool_active': self._process_pool is not None,
                'analysis_thread_active': (
                    self._analysis_thread is not None and self._analysis_thread.is_alive()
                )
            },
            'queue_status': {
                'queue_size': (
                    self._analysis_queue.qsize() 
                    if hasattr(self._analysis_queue, 'qsize') else 0
                ),
                'pending_results': len(self._async_results)
            },
            'initialization_status': {
                'is_initialized': self._is_initialized,
                'is_shutdown': self._is_shutdown
            }
        })
        
        return stats
    
    @property
    def is_available(self) -> bool:
        """检查并行处理管理器是否可用"""
        return self._is_initialized and not self._is_shutdown
    
    @property
    def thread_pool(self):
        """获取线程池实例（仅供高级用户使用）"""
        if not self._is_initialized:
            self.initialize()
        return self._thread_pool
    
    @property
    def process_pool(self):
        """获取进程池实例（仅供高级用户使用）"""
        if not self._is_initialized:
            self.initialize()
        return self._process_pool


# 全局并行处理管理器实例
_global_parallel_manager = None


def get_parallel_manager(max_workers: int = None, enable_async_analysis: bool = True,
                        thread_name_prefix: str = "GlobalParallel") -> ParallelManager:
    """获取全局并行处理管理器实例"""
    global _global_parallel_manager
    
    if _global_parallel_manager is None:
        _global_parallel_manager = ParallelManager(
            max_workers=max_workers,
            enable_async_analysis=enable_async_analysis,
            thread_name_prefix=thread_name_prefix
        )
    
    return _global_parallel_manager


def shutdown_global_parallel_manager():
    """关闭全局并行处理管理器"""
    global _global_parallel_manager
    
    if _global_parallel_manager is not None:
        _global_parallel_manager.shutdown()
        _global_parallel_manager = None 