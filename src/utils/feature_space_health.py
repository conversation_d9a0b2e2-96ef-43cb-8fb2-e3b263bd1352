"""
特征空间健康度评估统一模块
整合所有健康度相关的评估逻辑，避免代码重复
"""
from typing import Tuple, Dict, Any
from dataclasses import dataclass
from enum import Enum
import numpy as np
from . import log_utils


class HealthLevel(Enum):
    """健康度等级枚举"""
    EXCELLENT = ("🟢 优秀", 4)
    HEALTHY = ("🟢 健康", 3)
    GOOD = ("🟡 良好", 2)
    AVERAGE = ("🟡 一般", 1)
    POOR = ("🔴 较差", 0)


@dataclass
class HealthThresholds:
    """健康度评估阈值配置"""
    # 距离比例阈值
    distance_ratio_excellent: float = 5.0
    distance_ratio_healthy: float = 3.0
    distance_ratio_good: float = 2.0
    distance_ratio_average: float = 1.5
    
    # 特征标准差阈值
    feature_std_min: float = 0.1
    feature_std_max: float = 1.0
    
    # 特征范数阈值  
    feature_norm_min: float = 0.5
    feature_norm_max: float = 2.0
    
    # 边界样本比例阈值
    boundary_ratio_min: float = 0.1
    boundary_ratio_max: float = 0.4
    
    # Fisher分数阈值
    fisher_score_excellent: float = 10.0
    fisher_score_healthy: float = 5.0
    fisher_score_good: float = 2.0
    fisher_score_average: float = 1.0


@dataclass
class HealthMetrics:
    """健康度指标数据结构"""
    distance_ratio: float = 0.0
    feature_std: float = 0.0
    feature_norm: float = 0.0
    boundary_ratio: float = 0.0
    fisher_score: float = 0.0
    overall_level: HealthLevel = HealthLevel.POOR
    detailed_scores: Dict[str, Tuple[HealthLevel, str]] = None


class FeatureSpaceHealthEvaluator:
    """特征空间健康度评估器 - 统一实现"""
    
    def __init__(self, thresholds: HealthThresholds = None):
        self.thresholds = thresholds or HealthThresholds()
        
    def evaluate_distance_ratio_health(self, inter_distance: float, intra_distance: float) -> Tuple[HealthLevel, str]:
        """评估距离比例健康度"""
        if intra_distance <= 0:
            return HealthLevel.POOR, "类内距离为零，无法计算比例"
            
        ratio = inter_distance / intra_distance
        
        if ratio >= self.thresholds.distance_ratio_excellent:
            return HealthLevel.EXCELLENT, f"类间类内距离比例优秀 ({ratio:.2f})"
        elif ratio >= self.thresholds.distance_ratio_healthy:
            return HealthLevel.HEALTHY, f"类间类内距离比例健康 ({ratio:.2f})"
        elif ratio >= self.thresholds.distance_ratio_good:
            return HealthLevel.GOOD, f"类间类内距离比例良好 ({ratio:.2f})"
        elif ratio >= self.thresholds.distance_ratio_average:
            return HealthLevel.AVERAGE, f"类间类内距离比例一般 ({ratio:.2f})"
        else:
            return HealthLevel.POOR, f"类间类内距离比例较差 ({ratio:.2f})"
    
    def evaluate_feature_statistics_health(self, feature_std: float, feature_norm: float) -> Tuple[HealthLevel, str]:
        """评估特征统计健康度"""
        std_healthy = self.thresholds.feature_std_min <= feature_std <= self.thresholds.feature_std_max
        norm_healthy = self.thresholds.feature_norm_min <= feature_norm <= self.thresholds.feature_norm_max
        
        if std_healthy and norm_healthy:
            return HealthLevel.HEALTHY, f"特征统计健康 (std={feature_std:.3f}, norm={feature_norm:.3f})"
        elif std_healthy or norm_healthy:
            return HealthLevel.AVERAGE, f"特征统计一般 (std={feature_std:.3f}, norm={feature_norm:.3f})"
        else:
            issues = []
            if not std_healthy:
                issues.append(f"标准差异常({feature_std:.3f})")
            if not norm_healthy:
                issues.append(f"范数异常({feature_norm:.3f})")
            return HealthLevel.POOR, f"特征统计较差: {', '.join(issues)}"
    
    def evaluate_boundary_health(self, boundary_ratio: float) -> Tuple[HealthLevel, str]:
        """评估边界样本健康度"""
        if self.thresholds.boundary_ratio_min <= boundary_ratio <= self.thresholds.boundary_ratio_max:
            return HealthLevel.HEALTHY, f"边界样本比例健康 ({boundary_ratio:.2%})"
        elif boundary_ratio < self.thresholds.boundary_ratio_min:
            return HealthLevel.POOR, f"边界样本过少 ({boundary_ratio:.2%})"
        else:
            return HealthLevel.AVERAGE, f"边界样本过多 ({boundary_ratio:.2%})"
    
    def evaluate_fisher_health(self, fisher_score: float) -> Tuple[HealthLevel, str]:
        """评估Fisher分数健康度"""
        if fisher_score >= self.thresholds.fisher_score_excellent:
            return HealthLevel.EXCELLENT, f"Fisher分数优秀 ({fisher_score:.2f})"
        elif fisher_score >= self.thresholds.fisher_score_healthy:
            return HealthLevel.HEALTHY, f"Fisher分数健康 ({fisher_score:.2f})"
        elif fisher_score >= self.thresholds.fisher_score_good:
            return HealthLevel.GOOD, f"Fisher分数良好 ({fisher_score:.2f})"
        elif fisher_score >= self.thresholds.fisher_score_average:
            return HealthLevel.AVERAGE, f"Fisher分数一般 ({fisher_score:.2f})"
        else:
            return HealthLevel.POOR, f"Fisher分数较差 ({fisher_score:.2f})"
    
    def comprehensive_health_evaluation(self, 
                                      inter_distance: float = None,
                                      intra_distance: float = None,
                                      feature_std: float = None,
                                      feature_norm: float = None,
                                      boundary_ratio: float = None,
                                      fisher_score: float = None) -> HealthMetrics:
        """综合健康度评估"""
        metrics = HealthMetrics()
        detailed_scores = {}
        health_scores = []
        
        # 距离比例评估
        if inter_distance is not None and intra_distance is not None:
            metrics.distance_ratio = inter_distance / max(intra_distance, 1e-8)
            level, desc = self.evaluate_distance_ratio_health(inter_distance, intra_distance)
            detailed_scores['distance_ratio'] = (level, desc)
            health_scores.append(level.value[1])
        
        # 特征统计评估
        if feature_std is not None and feature_norm is not None:
            metrics.feature_std = feature_std
            metrics.feature_norm = feature_norm
            level, desc = self.evaluate_feature_statistics_health(feature_std, feature_norm)
            detailed_scores['feature_stats'] = (level, desc)
            health_scores.append(level.value[1])
        
        # 边界样本评估
        if boundary_ratio is not None:
            metrics.boundary_ratio = boundary_ratio
            level, desc = self.evaluate_boundary_health(boundary_ratio)
            detailed_scores['boundary'] = (level, desc)
            health_scores.append(level.value[1])
        
        # Fisher分数评估
        if fisher_score is not None:
            metrics.fisher_score = fisher_score
            level, desc = self.evaluate_fisher_health(fisher_score)
            detailed_scores['fisher'] = (level, desc)
            health_scores.append(level.value[1])
        
        # 计算综合健康度
        if health_scores:
            avg_score = np.mean(health_scores)
            if avg_score >= 3.5:
                metrics.overall_level = HealthLevel.EXCELLENT
            elif avg_score >= 2.5:
                metrics.overall_level = HealthLevel.HEALTHY
            elif avg_score >= 1.5:
                metrics.overall_level = HealthLevel.GOOD
            elif avg_score >= 0.5:
                metrics.overall_level = HealthLevel.AVERAGE
            else:
                metrics.overall_level = HealthLevel.POOR
        
        metrics.detailed_scores = detailed_scores
        return metrics
    
    def log_health_report(self, metrics: HealthMetrics, tag: str = "HEALTH"):
        """记录健康度报告"""
        log_utils.info(f"特征空间健康度报告:", tag=tag)
        log_utils.info(f"  ├─ 综合健康度: {metrics.overall_level.value[0]}", tag=tag)
        
        for metric_name, (level, desc) in metrics.detailed_scores.items():
            log_utils.info(f"  ├─ {desc}", tag=tag)
        
        # 健康度建议
        if metrics.overall_level in [HealthLevel.POOR, HealthLevel.AVERAGE]:
            suggestions = self._generate_health_suggestions(metrics)
            if suggestions:
                log_utils.info(f"  └─ 优化建议: {'; '.join(suggestions)}", tag=tag)
    
    def _generate_health_suggestions(self, metrics: HealthMetrics) -> list:
        """生成健康度优化建议"""
        suggestions = []
        
        for metric_name, (level, desc) in metrics.detailed_scores.items():
            if level in [HealthLevel.POOR, HealthLevel.AVERAGE]:
                if metric_name == 'distance_ratio':
                    if metrics.distance_ratio < 2.0:
                        suggestions.append("增加类间距离或减少类内距离")
                elif metric_name == 'feature_stats':
                    if metrics.feature_std < self.thresholds.feature_std_min:
                        suggestions.append("增加特征多样性")
                    elif metrics.feature_std > self.thresholds.feature_std_max:
                        suggestions.append("减少特征噪声")
                elif metric_name == 'boundary':
                    if metrics.boundary_ratio < self.thresholds.boundary_ratio_min:
                        suggestions.append("调整边界挖掘参数")
                elif metric_name == 'fisher':
                    if metrics.fisher_score < 2.0:
                        suggestions.append("优化特征判别性")
        
        return suggestions


# 全局默认评估器实例
_default_evaluator = FeatureSpaceHealthEvaluator()

# 便捷函数接口

def evaluate_boundary_health(boundary_ratio: float) -> tuple:
    """便捷函数：评估边界健康度"""
    return _default_evaluator.evaluate_boundary_health(boundary_ratio)

def evaluate_distance_ratio_health(inter_distance: float, intra_distance: float) -> Tuple[HealthLevel, str]:
    """便捷函数：评估距离比例健康度"""
    return _default_evaluator.evaluate_distance_ratio_health(inter_distance, intra_distance)

def evaluate_feature_statistics_health(feature_std: float, feature_norm: float) -> Tuple[HealthLevel, str]:
    """便捷函数：评估特征统计健康度"""
    return _default_evaluator.evaluate_feature_statistics_health(feature_std, feature_norm)

def comprehensive_health_evaluation(**kwargs) -> HealthMetrics:
    """便捷函数：综合健康度评估"""
    return _default_evaluator.comprehensive_health_evaluation(**kwargs)

def log_health_report(metrics: HealthMetrics, tag: str = "HEALTH"):
    """便捷函数：记录健康度报告"""
    _default_evaluator.log_health_report(metrics, tag) 