#!/usr/bin/env python3
"""
确定性种子管理器

统一管理项目中所有随机性操作的种子控制，确保结果可重现。

职责：
- 提供确定性种子生成策略
- 管理临时随机状态
- 支持多层级种子控制
- 提供上下文管理器接口

Dependencies:
- torch
- numpy
- random
"""

import torch
import numpy as np
import random
from typing import Optional, Union, Dict, Any, Generator
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum


class SeedScope(Enum):
    """种子作用域枚举"""
    EPOCH = "epoch"             # 基于epoch的种子
    BATCH = "batch"             # 基于batch的种子
    TRIAL = "trial"             # 基于trial的种子


@dataclass
class SeedContext:
    """种子上下文信息"""
    epoch: Optional[int] = None
    batch: Optional[int] = None
    trial: Optional[int] = None
    sample_idx: Optional[int] = None
    class_idx: Optional[int] = None
    base_seed: int = 42
    
    def generate_seed(self, scope: SeedScope, multiplier: int = 1000) -> int:
        """根据上下文和作用域生成确定性种子"""
        components = [self.base_seed]
        
        if scope in [SeedScope.EPOCH, SeedScope.BATCH, SeedScope.TRIAL]:
            if self.epoch is not None:
                components.append(self.epoch * multiplier)
        
        if scope in [SeedScope.BATCH, SeedScope.TRIAL]:
            if self.batch is not None:
                components.append(self.batch * (multiplier // 10))
        
        if scope == SeedScope.TRIAL:
            if self.trial is not None:
                components.append(self.trial * (multiplier // 100))
            if self.class_idx is not None:
                components.append(self.class_idx * (multiplier // 1000))
        
        return sum(components) % (2**31 - 1)  # 确保在int32范围内


class DeterministicSeedManager:
    """确定性种子管理器"""
    
    def __init__(self, base_seed: int = 42):
        self.base_seed = base_seed
        self._original_states = {}
        
    def create_context(self, **kwargs) -> SeedContext:
        """创建种子上下文"""
        return SeedContext(base_seed=self.base_seed, **kwargs)
    
    @contextmanager
    def deterministic_context(self, context: SeedContext, scope: SeedScope, 
                            include_torch: bool = True, include_numpy: bool = True, 
                            include_random: bool = True) -> Generator[int, None, None]:
        """确定性上下文管理器
        
        Args:
            context: 种子上下文
            scope: 种子作用域
            include_torch: 是否控制PyTorch随机性
            include_numpy: 是否控制NumPy随机性
            include_random: 是否控制Python随机性
            
        Yields:
            生成的确定性种子
        """
        seed = context.generate_seed(scope)
        
        # 保存原始状态
        original_states = {}
        if include_torch:
            original_states['torch'] = torch.get_rng_state()
            if torch.cuda.is_available():
                original_states['torch_cuda'] = torch.cuda.get_rng_state()
        if include_numpy:
            original_states['numpy'] = np.random.get_state()
        if include_random:
            original_states['random'] = random.getstate()
        
        try:
            # 设置确定性种子
            if include_torch:
                torch.manual_seed(seed)
                if torch.cuda.is_available():
                    torch.cuda.manual_seed_all(seed)
            if include_numpy:
                np.random.seed(seed)
            if include_random:
                random.seed(seed)
            
            yield seed
            
        finally:
            # 恢复原始状态
            if include_torch:
                torch.set_rng_state(original_states['torch'])
                if torch.cuda.is_available():
                    torch.cuda.set_rng_state(original_states['torch_cuda'])
            if include_numpy:
                np.random.set_state(original_states['numpy'])
            if include_random:
                random.setstate(original_states['random'])
    
    def set_global_seed(self, seed: Optional[int] = None) -> None:
        """设置全局随机种子"""
        seed = seed or self.base_seed
        torch.manual_seed(seed)
        np.random.seed(seed)
        random.seed(seed)
        
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
        
        # 设置其他确定性选项
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    def generate_batch_indices(self, batch_size: int, context: SeedContext) -> torch.Tensor:
        """生成确定性批次索引"""
        with self.deterministic_context(context, SeedScope.BATCH, 
                                      include_numpy=False, include_random=False) as seed:
            return torch.randperm(batch_size)
    
    def generate_trial_indices(self, total_size: int, context: SeedContext) -> torch.Tensor:
        """生成确定性试验索引"""
        with self.deterministic_context(context, SeedScope.TRIAL,
                                      include_numpy=False, include_random=False) as seed:
            return torch.randperm(total_size)
    
    def sample_with_replacement(self, population_size: int, sample_size: int, 
                              context: SeedContext) -> np.ndarray:
        """确定性有放回采样"""
        with self.deterministic_context(context, SeedScope.TRIAL,
                                      include_torch=False, include_random=False) as seed:
            return np.random.choice(population_size, sample_size, replace=True)
    
    def sample_without_replacement(self, population_size: int, sample_size: int,
                                 context: SeedContext) -> np.ndarray:
        """确定性无放回采样"""
        with self.deterministic_context(context, SeedScope.TRIAL,
                                      include_torch=False, include_random=False) as seed:
            return np.random.choice(population_size, sample_size, replace=False)
    
    def shuffle_list(self, items: list, context: SeedContext) -> list:
        """确定性列表打乱"""
        with self.deterministic_context(context, SeedScope.TRIAL,
                                      include_torch=False, include_numpy=False) as seed:
            shuffled = items.copy()
            random.shuffle(shuffled)
            return shuffled


# 全局种子管理器实例
_global_seed_manager = None

def get_seed_manager(base_seed: int = 42) -> DeterministicSeedManager:
    """获取全局种子管理器实例"""
    global _global_seed_manager
    if _global_seed_manager is None:
        _global_seed_manager = DeterministicSeedManager(base_seed)
    return _global_seed_manager


# 便捷函数
def with_deterministic_seed(context: SeedContext, scope: SeedScope = SeedScope.BATCH):
    """装饰器：为函数提供确定性种子环境"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            manager = get_seed_manager()
            with manager.deterministic_context(context, scope):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def create_mining_context(epoch: int, batch: int) -> SeedContext:
    """为挖掘器创建种子上下文"""
    return SeedContext(epoch=epoch, batch=batch)


def create_validation_context(epoch: int, trial: int, class_idx: int) -> SeedContext:
    """为验证创建种子上下文"""
    return SeedContext(epoch=epoch, trial=trial, class_idx=class_idx)


def create_meta_learning_context(epoch: int) -> SeedContext:
    """为元学习创建种子上下文"""
    return SeedContext(epoch=epoch)


 