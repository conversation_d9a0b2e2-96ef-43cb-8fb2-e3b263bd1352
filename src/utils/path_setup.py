"""
通用路径设置工具模块
用于在各个入口文件中统一设置Python导入路径
"""

import os
import sys
from pathlib import Path


def setup_project_paths(current_file_path: str = None):
    """
    设置项目路径，确保可以正确导入src目录下的模块
    
    Args:
        current_file_path: 当前文件的路径，如果不提供则自动获取调用者的路径
    
    Returns:
        tuple: (project_root, src_dir) 项目根目录和src目录路径
    """
    if current_file_path is None:
        # 获取调用者的文件路径
        import inspect
        frame = inspect.currentframe().f_back
        current_file_path = frame.f_code.co_filename
    
    # 计算项目根目录
    current_dir = os.path.dirname(os.path.abspath(current_file_path))
    
    # 向上查找直到找到包含src目录的根目录
    project_root = current_dir
    while project_root != os.path.dirname(project_root):  # 避免无限循环
        if os.path.exists(os.path.join(project_root, "src")):
            break
        project_root = os.path.dirname(project_root)
    
    # 如果没找到src目录，使用相对路径计算
    if not os.path.exists(os.path.join(project_root, "src")):
        # 假设当前文件在项目的某个子目录中
        project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
        if not os.path.exists(os.path.join(project_root, "src")):
            project_root = os.path.abspath(os.path.join(current_dir, ".."))
    
    src_dir = os.path.join(project_root, "src")
    
    # 添加路径到sys.path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)
    
    return project_root, src_dir


def setup_src_path_from_relative(relative_levels: int = 2):
    """
    基于相对层级设置src路径
    
    Args:
        relative_levels: 相对于src目录的层级数
                        例如：src/test/xxx.py -> relative_levels=2
                             src/faiss_retrieval/xxx.py -> relative_levels=2
                             src/xxx.py -> relative_levels=1
    
    Returns:
        tuple: (project_root, src_dir) 项目根目录和src目录路径
    """
    import inspect
    
    # 获取调用者的文件路径
    frame = inspect.currentframe().f_back
    current_file_path = frame.f_code.co_filename
    
    current_dir = os.path.dirname(os.path.abspath(current_file_path))
    
    # 向上relative_levels层找到项目根目录
    project_root = current_dir
    for _ in range(relative_levels):
        project_root = os.path.dirname(project_root)
    
    src_dir = os.path.join(project_root, "src")
    
    # 添加路径到sys.path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    if src_dir not in sys.path:
        sys.path.insert(0, src_dir)
    
    return project_root, src_dir


def ensure_src_in_path():
    """
    确保src目录在Python路径中
    这是一个简化版本，适用于大多数情况
    """
    import inspect
    
    # 获取调用者的文件路径
    frame = inspect.currentframe().f_back
    current_file_path = frame.f_code.co_filename
    
    current_dir = os.path.dirname(os.path.abspath(current_file_path))
    
    # 尝试找到src目录
    possible_src_dirs = [
        current_dir,  # 当前就是src目录
        os.path.join(current_dir, ".."),  # 上一级是src目录
        os.path.join(current_dir, "..", ".."),  # 上两级包含src目录
    ]
    
    for base_dir in possible_src_dirs:
        src_dir = os.path.join(base_dir, "src") if not base_dir.endswith("src") else base_dir
        if os.path.exists(src_dir) and os.path.isdir(src_dir):
            if src_dir not in sys.path:
                sys.path.insert(0, src_dir)
            
            # 也添加项目根目录
            project_root = os.path.dirname(src_dir) if src_dir.endswith("src") else base_dir
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            return project_root, src_dir
    
    # 如果都找不到，使用默认策略
    return setup_project_paths(current_file_path)


# 便捷函数
def quick_setup():
    """
    快速设置，适用于大多数情况
    """
    return ensure_src_in_path()


if __name__ == "__main__":
    # 测试功能
    project_root, src_dir = setup_project_paths()
    print(f"Project root: {project_root}")
    print(f"Src directory: {src_dir}")
    print(f"Current sys.path: {sys.path[:3]}...")  # 只显示前3个路径 