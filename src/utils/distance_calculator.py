#!/usr/bin/env python3
"""
统一距离计算管理器

解决项目中训练、验证、推理阶段距离计算不一致的核心问题：
1. 统一余弦距离计算公式
2. 统一类间距离计算策略  
3. 统一分离比计算方法
4. 提供一致的接口

修复的关键问题：
- 训练阶段: (1-cosine_sim)/2, 范围[0,1]
- 验证阶段: 1-cosine_sim, 范围[0,2]  
- 数值差异2倍，语义不同
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Union, Optional
from . import log_utils


class UnifiedDistanceCalculator:
    """统一距离计算器 - 解决四个阶段计算不一致问题"""
    
    def __init__(self, distance_range: str = "0_1"):
        """
        初始化统一距离计算器
        
        Args:
            distance_range: 距离范围，"0_1"表示[0,1]，"0_2"表示[0,2]
        """
        self.distance_range = distance_range
        log_utils.info(f"🔧 统一距离计算器初始化，距离范围: {distance_range}")
    
    def cosine_distance(self, vec1: torch.Tensor, vec2: torch.Tensor) -> torch.Tensor:
        """
        统一的余弦距离计算
        
        Args:
            vec1: 向量1
            vec2: 向量2
            
        Returns:
            余弦距离
        """
        # 确保向量已归一化
        vec1_norm = F.normalize(vec1, p=2, dim=-1)
        vec2_norm = F.normalize(vec2, p=2, dim=-1)
        
        # 计算余弦相似度
        cosine_sim = torch.sum(vec1_norm * vec2_norm, dim=-1)
        cosine_sim = torch.clamp(cosine_sim, -1.0, 1.0)
        
        # 根据配置返回不同范围的距离
        if self.distance_range == "0_1":
            return (1.0 - cosine_sim) / 2.0  # [0, 1]
        else:
            return 1.0 - cosine_sim  # [0, 2]
    
    def batch_cosine_distance(self, embeddings: torch.Tensor) -> torch.Tensor:
        """
        批量计算余弦距离矩阵
        
        Args:
            embeddings: [N, D] 特征矩阵
            
        Returns:
            [N, N] 距离矩阵
        """
        # 归一化
        embeddings_norm = F.normalize(embeddings, p=2, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.mm(embeddings_norm, embeddings_norm.t())
        similarity_matrix = torch.clamp(similarity_matrix, -1.0, 1.0)
        
        # 转换为距离矩阵
        if self.distance_range == "0_1":
            return (1.0 - similarity_matrix) / 2.0
        else:
            return 1.0 - similarity_matrix
    
    def compute_intra_class_distances(self, class_embeddings: Dict[int, List[torch.Tensor]]) -> Tuple[float, float]:
        """
        计算类内距离 - 统一实现
        
        Args:
            class_embeddings: {class_id: [embeddings]}
            
        Returns:
            (平均类内距离, 类内距离标准差)
        """
        all_intra_distances = []
        
        for class_id, embeddings in class_embeddings.items():
            if len(embeddings) < 2:
                continue
            
            # 转换为tensor
            if isinstance(embeddings[0], torch.Tensor):
                class_tensor = torch.stack(embeddings)
            else:
                class_tensor = torch.tensor(embeddings)
            
            # 计算类内距离矩阵
            distance_matrix = self.batch_cosine_distance(class_tensor)
            
            # 提取上三角距离（排除对角线）
            mask = torch.triu(torch.ones_like(distance_matrix, dtype=torch.bool), diagonal=1)
            intra_distances = distance_matrix[mask]
            
            all_intra_distances.extend(intra_distances.cpu().numpy().tolist())
        
        if all_intra_distances:
            return float(np.mean(all_intra_distances)), float(np.std(all_intra_distances))
        else:
            return 0.0, 0.0
    
    def compute_inter_class_distances(self, class_embeddings: Dict[int, List[torch.Tensor]], 
                                    strategy: str = "class_center") -> Tuple[float, float]:
        """
        计算类间距离 - 统一实现
        
        Args:
            class_embeddings: {class_id: [embeddings]}
            strategy: "class_center" 或 "all_pairs"
            
        Returns:
            (平均类间距离, 类间距离标准差)
        """
        if strategy == "class_center":
            return self._compute_inter_class_center_distances(class_embeddings)
        else:
            return self._compute_inter_all_pairs_distances(class_embeddings)
    
    def _compute_inter_class_center_distances(self, class_embeddings: Dict[int, List[torch.Tensor]]) -> Tuple[float, float]:
        """计算类中心间距离"""
        class_centers = {}
        
        # 计算每个类的中心
        for class_id, embeddings in class_embeddings.items():
            if len(embeddings) == 0:
                continue
            
            if isinstance(embeddings[0], torch.Tensor):
                class_tensor = torch.stack(embeddings)
            else:
                class_tensor = torch.tensor(embeddings)
            
            class_centers[class_id] = torch.mean(class_tensor, dim=0)
        
        # 计算类中心间距离
        inter_distances = []
        class_ids = list(class_centers.keys())
        
        for i in range(len(class_ids)):
            for j in range(i + 1, len(class_ids)):
                center1 = class_centers[class_ids[i]]
                center2 = class_centers[class_ids[j]]
                distance = self.cosine_distance(center1.unsqueeze(0), center2.unsqueeze(0))
                inter_distances.append(distance.item())
        
        if inter_distances:
            return float(np.mean(inter_distances)), float(np.std(inter_distances))
        else:
            return 0.0, 0.0
    
    def _compute_inter_all_pairs_distances(self, class_embeddings: Dict[int, List[torch.Tensor]]) -> Tuple[float, float]:
        """计算所有类间样本对距离"""
        inter_distances = []
        class_ids = list(class_embeddings.keys())
        
        for i in range(len(class_ids)):
            for j in range(i + 1, len(class_ids)):
                class1_embeddings = class_embeddings[class_ids[i]]
                class2_embeddings = class_embeddings[class_ids[j]]
                
                if len(class1_embeddings) == 0 or len(class2_embeddings) == 0:
                    continue
                
                # 计算类间所有样本对距离
                for emb1 in class1_embeddings:
                    for emb2 in class2_embeddings:
                        distance = self.cosine_distance(emb1.unsqueeze(0), emb2.unsqueeze(0))
                        inter_distances.append(distance.item())
        
        if inter_distances:
            return float(np.mean(inter_distances)), float(np.std(inter_distances))
        else:
            return 0.0, 0.0
    
    def compute_separation_ratio(self, intra_distance: float, inter_distance: float) -> float:
        """
        计算分离比 - 统一实现，增强数值稳定性
        
        Args:
            intra_distance: 类内距离
            inter_distance: 类间距离
            
        Returns:
            分离比
        """
        # LLM_CONTEXT: [WHAT] 增强分离比计算的数值稳定性 [WHY] 防止数值爆炸和NaN #separation_ratio_fix
        
        # 数值稳定性参数
        EPSILON = 1e-8
        MAX_SEPARATION_RATIO = 100.0  # 分离比上限
        
        # 输入验证
        if not isinstance(intra_distance, (int, float)) or not isinstance(inter_distance, (int, float)):
            log_utils.warning(f"分离比计算输入类型错误: intra={type(intra_distance)}, inter={type(inter_distance)}")
            return 0.0
        
        # NaN检查
        if np.isnan(intra_distance) or np.isnan(inter_distance):
            log_utils.warning(f"分离比计算输入包含NaN: intra={intra_distance}, inter={inter_distance}")
            return 0.0
        
        # 确保分母稳定
        stable_intra_distance = max(abs(intra_distance), EPSILON)
        
        # 计算分离比并限制上限
        raw_ratio = inter_distance / stable_intra_distance
        separation_ratio = min(raw_ratio, MAX_SEPARATION_RATIO)
        
        # 记录是否发生了截断
        if raw_ratio > MAX_SEPARATION_RATIO:
            log_utils.warning(f"分离比过大被截断: {raw_ratio:.4f} -> {MAX_SEPARATION_RATIO}")
        
        return separation_ratio
    
    def compute_all_metrics(self, class_embeddings: Dict[int, List[torch.Tensor]], 
                          inter_strategy: str = "class_center") -> Dict[str, float]:
        """
        计算所有距离指标 - 一站式接口
        
        Args:
            class_embeddings: {class_id: [embeddings]}
            inter_strategy: 类间距离计算策略
            
        Returns:
            包含所有指标的字典
        """
        # 计算类内距离
        intra_mean, intra_std = self.compute_intra_class_distances(class_embeddings)
        
        # 计算类间距离
        inter_mean, inter_std = self.compute_inter_class_distances(class_embeddings, inter_strategy)
        
        # 计算分离比
        separation_ratio = self.compute_separation_ratio(intra_mean, inter_mean)
        
        return {
            'intra_distance': intra_mean,
            'intra_std': intra_std,
            'inter_distance': inter_mean,
            'inter_std': inter_std,
            'separation_ratio': separation_ratio,
            'distance_range': self.distance_range,
            'inter_strategy': inter_strategy
        }


# 全局统一距离计算器实例
_global_distance_calculator = None


def get_distance_calculator(distance_range: str = "0_1") -> UnifiedDistanceCalculator:
    """获取全局距离计算器实例"""
    global _global_distance_calculator
    if _global_distance_calculator is None:
        _global_distance_calculator = UnifiedDistanceCalculator(distance_range)
    return _global_distance_calculator


def set_global_distance_range(distance_range: str):
    """设置全局距离范围"""
    global _global_distance_calculator
    if _global_distance_calculator is not None:
        _global_distance_calculator.distance_range = distance_range
        log_utils.info(f"🔧 全局距离范围更新为: {distance_range}")


# 便捷函数 - 直接使用全局计算器
def cosine_distance(vec1: torch.Tensor, vec2: torch.Tensor) -> torch.Tensor:
    """便捷函数：计算余弦距离"""
    return get_distance_calculator().cosine_distance(vec1, vec2)


def compute_distance_metrics(class_embeddings: Dict[int, List[torch.Tensor]], 
                           inter_strategy: str = "class_center") -> Dict[str, float]:
    """便捷函数：计算所有距离指标"""
    return get_distance_calculator().compute_all_metrics(class_embeddings, inter_strategy)


def compute_separation_ratio(intra_distance: float, inter_distance: float) -> float:
    """便捷函数：计算分离比 - 增强数值稳定性"""
    # LLM_CONTEXT: [WHAT] 使用统一的数值稳定性计算 [WHY] 确保一致性和稳定性 #separation_ratio_fix
    return get_distance_calculator().compute_separation_ratio(intra_distance, inter_distance) 