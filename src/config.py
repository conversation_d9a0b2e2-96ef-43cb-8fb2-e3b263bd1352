import os
import sys
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple


BEST_MODEL_PATH = "output/model_epoch_14.pth"


# 自动检测项目根目录和正确的文件路径
def _get_project_root():
    """自动检测项目根目录"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 如果当前在src目录下，则上级目录是项目根目录
    if current_dir.endswith('/src') or current_dir.endswith('\\src'):
        return os.path.dirname(current_dir)
    # 如果当前就在项目根目录
    elif os.path.exists(os.path.join(current_dir, 'output')):
        return current_dir
    # 否则假设上级目录是项目根目录
    else:
        return os.path.dirname(current_dir)

def _get_metadata_path(filename):
    """获取元数据文件的正确路径"""
    project_root = _get_project_root()
    metadata_path = os.path.join(project_root, 'output', 'metadata', filename)
    # 转换为相对路径（相对于当前工作目录）
    try:
        return os.path.relpath(metadata_path)
    except ValueError:
        # 如果无法计算相对路径，则返回绝对路径
        return metadata_path


# ==================== 核心配置类 ====================

@dataclass
class TrainConfig:
    """训练配置 - 包含所有训练相关的参数"""
    
    # 基础训练参数
    total_training_epochs: int = 100
    random_seed: int = 42
    embedding_dim: int = 512
    
    # 数据配置
    train_metadata_path: str = field(default_factory=lambda: _get_metadata_path('train_meta.json'))
    val_metadata_path: str = field(default_factory=lambda: _get_metadata_path('val_meta.json'))
    output_dir: str = "output"
    logs_dir: str = "output/logs"
    batch_size: int = 64
    num_workers: int = 8
    img_size: int = 224
    pin_memory: bool = True
    persistent_workers: bool = True
    prefetch_factor: int = 4
    multiprocessing_context: str = 'spawn'
    
    # 数据增强参数
    horizontal_flip_prob: float = 0.5
    vertical_flip_prob: float = 0.05
    color_jitter_prob: float = 0.5
    color_jitter_brightness: float = 0.2
    color_jitter_contrast: float = 0.2
    color_jitter_saturation: float = 0.1
    color_jitter_hue: float = 0.05
    gaussian_blur_prob: float = 0.1
    gaussian_blur_kernel_size: int = 3
    gaussian_blur_sigma: Tuple[float, float] = (0.1, 1.0)
    random_affine_degrees: int = 10
    random_affine_translate: Tuple[float, float] = (0.1, 0.1)
    random_affine_scale: Tuple[float, float] = (0.9, 1.1)
    random_erasing_prob: float = 0.2
    random_erasing_scale: Tuple[float, float] = (0.02, 0.2)
    imagenet_mean: List[float] = field(default_factory=lambda: [0.485, 0.456, 0.406])
    imagenet_std: List[float] = field(default_factory=lambda: [0.229, 0.224, 0.225])
    difficulty_threshold: float = 0.75
    enable_pseudo_difficult_detection: bool = False
    
    # 困难样本增强参数
    difficult_horizontal_flip_prob: float = 0.7
    difficult_vertical_flip_prob: float = 0.1
    difficult_rotation_degrees: int = 15
    difficult_color_jitter_brightness: float = 0.3
    difficult_color_jitter_contrast: float = 0.3
    difficult_color_jitter_saturation: float = 0.2
    difficult_color_jitter_hue: float = 0.1
    difficult_gaussian_blur_sigma: Tuple[float, float] = (0.1, 2.0)
    difficult_random_erasing_prob: float = 0.3
    difficult_random_erasing_scale: Tuple[float, float] = (0.02, 0.33)
    
    # 网络架构参数
    backbone_name: str = 'efficientnet_b2'
    pretrained: bool = True
    attention_reduction: int = 8
    
    # 多尺度融合参数
    branch_dim_ratio: float = 1.0/3
    fusion_dropout_rate: float = 0.15
    use_attention_fusion: bool = True
    embedding_hidden_dims: List[int] = field(default_factory=lambda: [1024, 768])
    embedding_dropout_rates: List[float] = field(default_factory=lambda: [0.25, 0.25])
    l2_norm_p: int = 2
    l2_norm_dim: int = 1
    
    # 融合模块参数
    attention_num_heads: int = 4
    attention_head_dim_auto: bool = True
    local_kernel_sizes: List[int] = field(default_factory=lambda: [3, 5, 7])
    local_groups_factor: int = 4
    stats_dim: int = 9
    hidden_dim_factor: int = 8
    min_hidden_dim: int = 16
    
    # 分支权重和增益 - 🎯 优化：降低local_gain，减少过拟合风险
    default_semantics_weight: float = 0.33
    default_local_weight: float = 0.33
    default_global_weight: float = 0.34
    semantic_gain: float = 1.0
    local_gain: float = 1.1  # 从1.2降低到1.1，减少局部特征过度增强
    global_gain: float = 1.0
    
    # 门控机制
    enable_gating_mechanism: bool = True
    gate_reduction_ratio: int = 4
    
    # 优化器参数 - 🎯 优化：降低学习率，提升训练稳定性
    backbone_lr: float = 6e-5   # 降低骨干网络学习率
    head_lr: float = 1.0e-4     # 降低头部网络学习率
    weight_decay: float = 1.6e-4
    head_lr_ratio: float = 2.0
    max_lr_ratio: float = 2.5
    optimizer_type: str = 'adamw'
    
    # 学习率调度器参数 - 🎯 优化：更稳定的学习率策略，改善后期性能
    scheduler_type: str = 'lambda'
    lr_schedule_total_epochs: int = 100
    warmup_epochs: int = 10  # 进一步延长预热期
    peak_lr_factor: float = 0.75  # 进一步降低峰值学习率因子
    min_lr_factor: float = 0.2   # 进一步提高最小学习率因子，保持后期学习能力
    
    # 采样器参数
    initial_m: int = 8
    max_m: int = 16
    hard_class_ratio: float = 0.45
    sampling_mode: str = "adaptive"
    max_duplicate_ratio: float = 0.2
    
    # 自适应采样参数
    default_difficulty: float = 0.5
    min_iterations: int = 1
    
    # 损失函数参数
    circle_margin: float = 0.35
    circle_gamma: float = 320
    # center_loss_alpha: float = 0.3  # 已移除，SubCenterArcFace内部管理
    confidence_threshold: float = 0.8
    loss_mode: str = "balanced"
    
    # ArcFace 参数
    arcface_margin: float = 0.5
    arcface_scale: float = 64.0
    arcface_sub_centers: int = 3
    arcface_center_weight: float = 0.1
    
    # 默认配置
    default_feat_dim: int = 512
    default_loss_mode: str = "balanced"
    default_miner_mode: str = "hard"
    
    # 损失权重 - 简化为 Circle + Sub-center ArcFace
    loss_weights: Dict[str, float] = field(default_factory=lambda: {
        'circle': 0.7,
        'subcenter_arcface': 0.3
    })
    
    # 平衡模式损失权重 - 🎯 优化：进一步增强ArcFace权重，改善识别率
    balanced_circle: float = 0.55  # 降低Circle权重
    balanced_subcenter_arcface: float = 0.45  # 提升ArcFace权重，增强判别性
    
    # 挖掘器参数 - 🎯 优化：收紧边界范围，与验证引擎保持一致
    ms_epsilon: float = 0.05
    boundary_similarity_low: float = 0.20
    boundary_similarity_high: float = 0.80
    mining_strategy: str = "balanced"
    
    # 挖掘器权重 - 基于性能分析优化
    miner_weights: Dict[str, float] = field(default_factory=lambda: {
        'hard': 0.60,    # 🎯 进一步增加BatchHardMiner权重，解决困难样本挖掘失效
        'ms': 0.20,      # 🎯 降低MS权重，减少复杂相似性计算开销
        'boundary': 0.20  # 🎯 适度降低边界权重，防止边界样本过度挖掘
    })
    
    # 平衡模式挖掘器权重
    balanced_hard: float = 0.60  # 🎯 大幅提升BatchHardMiner权重，确保稳定的困难样本供应
    balanced_ms: float = 0.20    # 🎯 降低MS权重，提升训练稳定性
    balanced_boundary: float = 0.20  # 🎯 平衡边界权重，维持边界样本质量
    
    # 挖掘器配置参数
    weight_sum_tolerance: float = 0.001
    ms_adjustment_start_epoch: int = 10
    ms_ratio_low_threshold: float = 0.3
    ms_weight_boost_factor: float = 1.1
    ms_weight_boost_factor_alt: float = 1.05
    ms_weight_max_limit: float = 0.6
    weight_change_detection_threshold: float = 0.01
    ms_high_adjustment_start_epoch: int = 20
    ms_ratio_high_threshold: float = 0.7
    adjustment_reset_interval: int = 10
    
    # 权重验证参数
    ms_weight_low_threshold: float = 0.15
    extreme_weight_ratio_threshold: float = 5.0
    hard_boundary_sum_threshold: float = 0.8
    
    # 保护状态配置
    default_start_epoch: int = -1
    default_last_end_epoch: int = -10
    default_cooldown: int = 4
    default_min_duration: int = 5
    early_stage_epochs: int = 15
    early_stage_threshold: float = 0.15
    ms_min_weight_protection: float = 0.25
    ms_max_weight_protection: float = 0.55
    boundary_weight_max_limit: float = 0.45
    boundary_weight_boost_factor: float = 1.05
    hard_weight_min_limit: float = 0.15
    hard_weight_boost_factor: float = 1.05
    deactivation_threshold: float = 0.8
    
    # 样本跟踪配置
    overlap_penalty_factor: float = 0.1
    max_adjustments_per_run: int = 3
    
    # 权重控制参数 - 🎯 优化：进一步提升稳定性，减少频繁调整
    stability_factor: float = 0.995  # 进一步提高稳定性因子
    adaptation_rate: float = 0.0003  # 进一步降低适应率，减少权重波动
    early_training_epochs: int = 15
    min_history_length: int = 3 
    weight_update_interval: int = 300
    weight_stability_factor: float = 0.9
    weight_adaptation_rate: float = 0.1
    initial_weight_update_offset: float = 5.0
    disturb_interval_seconds: float = 120.0
    
    # MS优化配置
    balanced_mode_default_config: str = "balanced"
    enable_dynamic_enhancement: bool = True
    
    # 权重范围 - 🎯 优化：放宽权重不平衡阈值，减少频繁重置
    circle_min_init: float = 0.45  # 降低最小阈值
    circle_max_init: float = 0.75  # 提高最大阈值，允许Circle权重更高
    center_min_init: float = 0.15  # 降低最小阈值
    center_max_init: float = 0.35  # 提高最大阈值
    circle_reset_target: float = 0.65  # 提高重置目标，更接近优化后的平衡点
    center_reset_target: float = 0.35  # 提高重置目标，增强ArcFace作用
    
    # 课程学习参数
    curriculum_enabled: bool = True
    curriculum_start_epoch: int = 1
    curriculum_update_interval: int = 2
    difficulty_metric: str = "loss_based"
    curriculum_strategy: str = "adaptive"
    initial_easy_ratio: float = 0.7
    final_easy_ratio: float = 0.3
    curriculum_speed: float = 0.02
    
    # 课程学习扩展参数 - 🎯 优化：更保守的课程学习策略
    difficulty_window: int = 10
    difficulty_smoothing: float = 0.3
    curriculum_initial_easy_ratio: float = 0.85  # 进一步提高初始简单样本比例
    curriculum_final_easy_ratio: float = 0.5    # 提高最终简单样本比例，保持稳定性
    curriculum_temperature: float = 1.0         # 降低温度，减少随机性
    curriculum_feedback_window: int = 5
    feedback_window: int = 5
    performance_threshold: float = 0.02
    performance_decline_threshold: float = 0.01
    curriculum_enable_curriculum_learning: bool = True
    curriculum_enable_multi_stage: bool = True
    curriculum_enable_feedback: bool = True
    curriculum_temperature_decay: float = 0.99  # 🎯 优化：进一步减慢温度衰减，保持稳定性
    curriculum_selection_method: str = "probabilistic"
    curriculum_enable_reweighting: bool = True
    curriculum_easy_sample_weight: float = 1.0
    curriculum_hard_sample_weight: float = 1.3  # 🎯 降低困难样本权重，减少训练不稳定性
    curriculum_stage_epochs: List[int] = field(default_factory=lambda: [25, 50, 80])  # 🎯 调整阶段切换点，避开崩溃区间
    
    # 元学习参数
    enable_meta_learning: bool = True
    enable_loss_weight_optimization: bool = False  # 禁用损失权重优化，避免与AdaptiveWeightController冲突
    meta_learning_start_epoch: int = 5
    meta_learning_interval: int = 3
    meta_lr: float = 0.01
    meta_momentum: float = 0.9
    
    # 元学习搜索空间配置
    lr_search_range: Tuple[float, float] = (1e-6, 1e-2)
    weight_decay_range: Tuple[float, float] = (1e-6, 1e-2)
    batch_size_options: List[int] = field(default_factory=lambda: [32, 64, 128])
    circle_weight_range: Tuple[float, float] = (0.4, 0.8)
    arcface_weight_range: Tuple[float, float] = (0.15, 0.35)
    
    # 元学习优化器配置
    history_length: int = 20
    exploration_rate: float = 0.3
    exploration_decay: float = 0.95
    performance_window: int = 5
    improvement_threshold: float = 0.001
    convergence_patience: int = 5
    meta_optimizer_type: str = "adamw"
    meta_weight_decay: float = 1e-4
    min_lr_ratio: float = 0.5
    weight_sum_constraint: bool = True
    
    # 环境配置
    device_preference: str = "auto"
    enable_cudnn_benchmark: bool = True
    enable_mps_fallback: bool = True
    disable_albumentations_update: bool = True
    multiprocessing_sharing_strategy: str = 'file_system'
    
    # 日志和监控
    log_interval: int = 10
    progress_interval: int = 50
    sampling_stats_interval: int = 1
    weight_status_interval: int = 5
    status_log_interval: int = 10
    weight_distribution_log_interval: int = 5
    
    # 回调配置
    enable_fusion_callbacks: bool = False
    fusion_log_level: str = "info"
    fusion_enable_analysis: bool = False
    
    # 梯度裁剪
    gradient_clip_base_norm: float = 1.0
    gradient_clip_decay_factor: float = 0.5
    gradient_clip_decay_epochs: int = 10
    
    # 数值稳定性
    numerical_epsilon: float = 1e-8
    division_epsilon: float = 1e-10
    tensor_zero_value: float = 0.0
    
    # 回调配置参数
    aggressive_log_interval: int = 5
    aggressive_stability_threshold: float = 0.02
    aggressive_analysis_interval: int = 10
    conservative_log_interval: int = 20
    save_history: bool = True
    conservative_stability_threshold: float = 0.05
    conservative_stability_window: int = 10
    balanced_log_interval: int = 10
    balanced_stability_threshold: float = 0.03
    balanced_analysis_interval: int = 15
    
    # 优化器回调配置
    enable_logging: bool = True
    save_config_history: bool = True
    max_history_length: int = 100

    # 🎯 新增：视频场景验证配置 - 解决域适应问题
    enable_video_validation: bool = True  # 启用视频场景验证
    video_validation_interval: int = 3    # 🎯 优化：每3轮执行一次视频验证，及时发现域适应问题
    video_validation_path: str = "src/test/video/17b4f44844468df76536f6f7183b7bde.mp4"
    video_target_class_id: str = "30002"
    video_frame_skip: int = 5
    video_threshold: float = 0.3

    # 🎯 高级：自适应视频验证配置
    enable_adaptive_video_interval: bool = True   # 启用自适应间隔
    video_validation_early_interval: int = 2      # 前期间隔（前20轮）
    video_validation_mid_interval: int = 3        # 中期间隔（20-60轮）
    video_validation_late_interval: int = 5       # 后期间隔（60轮后）
    video_validation_early_epochs: int = 20       # 前期轮数阈值
    video_validation_mid_epochs: int = 60         # 中期轮数阈值
    
    def __post_init__(self):
        """验证配置参数"""
        if self.total_training_epochs <= 0:
            raise ValueError(f"total_training_epochs必须大于0，当前值: {self.total_training_epochs}")
        
        if self.embedding_dim <= 0:
            raise ValueError(f"embedding_dim必须大于0，当前值: {self.embedding_dim}")
        
        if self.batch_size <= 0:
            raise ValueError(f"batch_size必须大于0，当前值: {self.batch_size}")
        
        if not 0.0 <= self.hard_class_ratio <= 1.0:
            raise ValueError(f"hard_class_ratio必须在[0.0, 1.0]范围内，当前值: {self.hard_class_ratio}")
        
        # 验证损失权重和为1
        total_loss_weight = sum(self.loss_weights.values())
        if abs(total_loss_weight - 1.0) > 0.001:
            raise ValueError(f"损失权重总和必须为1.0，当前为: {total_loss_weight:.3f}")
        
        # 验证挖掘器权重和为1
        total_miner_weight = sum(self.miner_weights.values())
        if abs(total_miner_weight - 1.0) > 0.001:
            raise ValueError(f"挖掘器权重总和必须为1.0，当前为: {total_miner_weight:.3f}")


@dataclass
class ValConfig:
    """验证配置 - 包含所有验证相关的参数"""
    
    # 基础验证参数
    val_batch_size: int = 64
    val_mode: str = 'open_set'
    analyze_epoch_interval: int = 2
    
    # 验证数据加载
    val_batch_size_factor: float = 1.0
    max_val_batch_size: int = 64
    
    # 验证指标
    metrics: List[str] = field(default_factory=lambda: [
        'accuracy', 'precision', 'recall', 'f1_score', 
        'fisher_score', 'separation_ratio', 'boundary_ratio'
    ])
    
    # 验证引擎参数
    validation_interval: int = 1
    default_gallery_per_class: int = 3
    
    # 开集评估参数
    default_validation_mode: str = 'open_set'
    default_log_confusion: bool = False
    
    # 边界挖掘器配置 - 🎯 优化：收紧边界范围，提升识别精度
    boundary_similarity_low: float = 0.2
    boundary_similarity_high: float = 0.8
    
    # 特征质量评估阈值
    quality_excellent_threshold: float = 0.85
    quality_good_threshold: float = 0.7
    quality_baseline_threshold: float = 0.5
    quality_poor_threshold: float = 0.3
    
    # Fisher得分阈值
    fisher_excellent_threshold: float = 8.0
    fisher_good_threshold: float = 5.0
    fisher_baseline_threshold: float = 2.0
    fisher_weak_threshold: float = 1.0
    
    # 分离比阈值
    separation_excellent_threshold: float = 3.0
    separation_good_threshold: float = 2.0
    separation_baseline_threshold: float = 1.5
    separation_poor_threshold: float = 1.0
    
    # 开集评估阈值
    openset_excellent_threshold: float = 0.9
    openset_good_threshold: float = 0.8
    openset_baseline_threshold: float = 0.7
    
    # 边界样本阈值
    boundary_excellent_threshold: float = 0.1
    boundary_good_threshold: float = 0.2
    boundary_normal_threshold: float = 0.3
    
    # 综合评估权重
    quality_score_weight: float = 0.4
    fisher_score_weight: float = 0.3
    separation_score_weight: float = 0.3
    
    # 归一化因子
    fisher_normalization_factor: float = 10.0
    separation_normalization_factor: float = 5.0
    
    # 综合健康度阈值
    overall_excellent_threshold: float = 0.8
    overall_good_threshold: float = 0.6
    overall_baseline_threshold: float = 0.4
    
    # 进度显示配置
    progress_bar_ncols: int = 120
    progress_bar_leave: bool = False
    progress_bar_dynamic_ncols: bool = True
    progress_bar_ascii: bool = True
    
    # 采样器配置（用于验证）
    val_sampler: str = "sequential"
    
    # 检索器配置
    retriever_type: str = "faiss"
    
    # 日志配置
    enable_detailed_logging: bool = True
    enable_progress_monitoring: bool = True
    
    def __post_init__(self):
        """验证配置参数"""
        if self.val_batch_size <= 0:
            raise ValueError(f"val_batch_size必须大于0，当前值: {self.val_batch_size}")
        
        if self.default_gallery_per_class <= 0:
            raise ValueError(f"default_gallery_per_class必须大于0，当前值: {self.default_gallery_per_class}")
        
        # 验证评估权重和为1
        total_weight = self.quality_score_weight + self.fisher_score_weight + self.separation_score_weight
        if abs(total_weight - 1.0) > 0.001:
            raise ValueError(f"评估权重总和必须为1.0，当前为: {total_weight:.3f}")
        
        # 验证阈值递减关系
        quality_thresholds = [
            self.quality_excellent_threshold, self.quality_good_threshold,
            self.quality_baseline_threshold, self.quality_poor_threshold
        ]
        if not all(quality_thresholds[i] >= quality_thresholds[i+1] for i in range(len(quality_thresholds)-1)):
            raise ValueError("质量评估阈值必须满足递减关系")
        

@dataclass
class StoppingConfig:
    """早停配置 - 包含所有早停相关的参数"""
    
    # 基础早停参数 - 🎯 优化：进一步提升早停稳定性，避免错失最佳性能
    early_stopping: bool = True
    patience: int = 25  # 进一步增加耐心值，基于测试结果显示最佳性能可能出现较晚
    delta: float = 0.003  # 进一步降低改进阈值，更容易检测到微小改进
    monitor: str = "val_accuracy"


    
    # 边界样本监控 - 🎯 优化：降低边界样本要求，提升稳定性
    boundary_threshold: int = 6  # 降低边界样本阈值
    boundary_min_threshold: int = 6
    boundary_stability_threshold: int = 2  # 降低稳定性要求
    min_boundary_threshold: int = 4  # 降低最小边界样本要求
    
    # 权重稳定性
    weight_stability_epochs: int = 8
    weight_change_threshold: float = 0.05
    
    # 改进率阈值
    improvement_rate_threshold: float = 0.015
    min_improvement_rate: float = 0.001
    separation_improvement_threshold: float = 0.005
    
    # 距离相关阈值
    inter_distance_decline_threshold: float = 0.035
    intra_distance_threshold: float = 0.025
    inter_intra_safety_ratio: float = 0.25
    
    # 损失相关参数
    loss_patience: int = 10
    best_loss_gap_threshold: float = 1.10
    
    # 验证集监控
    val_boundary_ratio_threshold: float = 0.015
    val_acc_fluctuation_threshold: float = 0.05
    
    # 智能早停参数
    adaptive_patience: bool = True
    performance_plateau_threshold: float = 0.95
    overfit_detection_window: int = 8
    generalization_gap_threshold: float = 0.05
    
    # 伪提升检测
    pseudo_improve_circle_boost: float = 0.10
    pseudo_improve_center_boost: float = 0.05
    
    # 停滞检测
    max_stagnation: int = 3
    trend_window: int = 5
    max_negative_trends: int = 3
    
    # 特征空间健康度
    feature_quality_threshold: float = 0.5
    
    # 过拟合检测常量
    significant_decline_threshold: float = 0.02
    boundary_observation_window: int = 4
    boundary_low_ratio_threshold: float = 0.75
    consecutive_observation_threshold: int = 2
    
    # 停滞检测常量
    stagnation_history_window: int = 6
    accuracy_relative_gap_threshold: float = -0.02
    zigzag_fluctuation_threshold: int = 2
    separation_change_rate_threshold: float = 0.005
    val_boundary_low_threshold: float = 0.01
    
    # 智能过拟合检测
    train_acc_estimation_factor: float = 1.15
    gap_trend_threshold: float = 0.01
    val_acc_variance_threshold: float = 0.0001
    sep_variance_threshold: float = 0.001
    boundary_decline_threshold: float = 0.3
    min_boundary_denominator: float = 0.001
    distance_ratio_decline_threshold: float = 0.1
    overfit_confidence_threshold: float = 0.4
    min_overfit_signals: int = 2
    total_overfit_signals: int = 4
    
    # 性能平台期检测
    min_plateau_detection_window: int = 10
    plateau_detection_window: int = 12
    high_performance_ratio_threshold: float = 0.75
    sep_high_ratio_threshold: float = 0.7
    acc_variance_plateau_threshold: float = 0.0005
    sep_variance_plateau_threshold: float = 0.002
    
    # 动态耐心值调整
    high_overfit_threshold: float = 0.7
    medium_overfit_threshold: float = 0.4
    high_overfit_patience_factor: float = 0.7
    medium_overfit_patience_factor: float = 0.85
    low_overfit_patience_factor: float = 1.1
    
    # 平台期调整
    long_plateau_threshold: int = 15
    medium_plateau_threshold: int = 8
    long_plateau_patience_factor: float = 1.3
    medium_plateau_patience_factor: float = 1.15
    normal_plateau_patience_factor: float = 1.0
    
    # 稳定性调整
    high_stability_threshold: float = 0.8
    medium_stability_threshold: float = 0.5
    high_stability_patience_factor: float = 0.8
    medium_stability_patience_factor: float = 0.9
    low_stability_patience_factor: float = 1.2
    
    # 训练阶段调整
    early_stage_ratio: float = 0.3
    early_stage_patience_factor: float = 1.4
    middle_stage_patience_factor: float = 1.0
    late_stage_patience_factor: float = 0.9
    
    # 动态耐心值范围
    min_dynamic_patience: int = 8
    max_dynamic_patience: int = 30
    patience_change_log_threshold: int = 2
    
    # 改进率计算
    improvement_rate_window: int = 5
    min_separation_denominator: float = 0.001
    min_distance_denominator: float = 0.001
    
    # 边界保护参数
    alert_cooldown: int = 8
    window_size: int = 3
    decline_trend_threshold: float = -0.2
    consecutive_low_threshold: int = 12
    rapid_decline_threshold: float = -0.5
    current_low_threshold: int = 20
    recovery_threshold: int = 25
    
    # 最小训练轮数
    early_stopping_min_epochs: int = 50
    
    # 泛化约束早停参数
    patience_val_epoch: int = 8
    patience_sep_epoch: int = 6
    patience_inter_epoch: int = 5
    
    # 日志控制参数
    last_warning_epoch_init: int = -1
    
    def __post_init__(self):
        """验证配置参数"""
        if self.patience <= 0:
            raise ValueError(f"patience必须大于0，当前值: {self.patience}")
        
        if self.delta <= 0:
            raise ValueError(f"delta必须大于0，当前值: {self.delta}")
        
        if self.boundary_threshold <= 0:
            raise ValueError(f"boundary_threshold必须大于0，当前值: {self.boundary_threshold}")
        
        if not 0.0 <= self.performance_plateau_threshold <= 1.0:
            raise ValueError(f"performance_plateau_threshold必须在[0.0, 1.0]范围内，当前值: {self.performance_plateau_threshold}")
        
        if self.min_dynamic_patience > self.max_dynamic_patience:
            raise ValueError(f"min_dynamic_patience必须小于等于max_dynamic_patience")


# ==================== 配置工厂函数 ====================

def create_train_config(**kwargs) -> TrainConfig:
    """创建训练配置"""
    return TrainConfig(**kwargs)


def create_val_config(**kwargs) -> ValConfig:
    """创建验证配置"""
    return ValConfig(**kwargs)


def create_stopping_config(**kwargs) -> StoppingConfig:
    """创建早停配置"""
    return StoppingConfig(**kwargs)


def create_lightweight_train_config() -> TrainConfig:
    """创建轻量级训练配置"""
    return TrainConfig(
        batch_size=32,
        num_workers=4,
        total_training_epochs=50,
        backbone_lr=1e-4,
        weight_decay=1e-4,
        fusion_dropout_rate=0.1
    )


def create_aggressive_train_config() -> TrainConfig:
    """创建激进训练配置"""
    return TrainConfig(
        batch_size=96,
        total_training_epochs=150,
        backbone_lr=1.5e-4,
        head_lr=2.5e-4,
        hard_class_ratio=0.6,
        loss_weights={'circle': 0.7, 'arcface': 0.3}
    )


# ==================== 全局配置实例 ====================

# 默认配置实例
DEFAULT_TRAIN_CONFIG = TrainConfig()
DEFAULT_VAL_CONFIG = ValConfig()
DEFAULT_STOPPING_CONFIG = StoppingConfig()

# 训练回调配置 - 直接使用现有配置类
DEFAULT_TRAINING_CALLBACKS_CONFIG = DEFAULT_TRAIN_CONFIG

# 预设配置实例
LIGHTWEIGHT_TRAIN_CONFIG = create_lightweight_train_config()
AGGRESSIVE_TRAIN_CONFIG = create_aggressive_train_config()


# ==================== 向后兼容性支持 ====================

# 为了向后兼容，保留一些重要的全局配置实例
DEFAULT_TRAINING_CONFIG = DEFAULT_TRAIN_CONFIG
TrainingConfig = TrainConfig

# 重要的路径配置
BEST_MODEL_PATH = "output/model_epoch_14.pth"
