from setuptools import setup, find_packages

setup(
    name="high-precision-individual-identification",
    version="1.0.0",
    packages=find_packages(),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=[
        "torch>=1.9.0",
        "torchvision>=0.10.0",
        "numpy>=1.21.0",
        "Pillow>=8.3.0",
        "tqdm>=4.62.0",
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "scikit-learn>=1.0.0",
        "faiss-cpu>=1.7.0",
    ],
    author="会飞的黑羊",
    description="High Precision Individual Identification System",
) 