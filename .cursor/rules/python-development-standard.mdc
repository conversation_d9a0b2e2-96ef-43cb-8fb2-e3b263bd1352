---
description: 
globs: 
alwaysApply: true
---
{
  "python_coding_standard_v1": {
    "method_constraints": {
      "ideal_line_count": [5, 15],
      "max_line_count": 25,
      "refactor_triggers": [
        "line_count > 15 AND (has_loop OR has_nesting)",
        "parameter_count > 3",
        "cyclomatic_complexity > 5"
      ]
    },
    "file_constraints": {
      "ideal_method_count": [5, 10],
      "max_method_count": 15,
      "split_rules": [
        "file_line_count > 300",
        "contains_unrelated_functionalities >= 2"
      ]
    },
    "single_responsibility": {
      "core_principle": "One core problem per file",
      "naming_convention": "File name reflects function (e.g., image_processor.py)",
      "prohibitions": [
        "Mixing business logic with utility functions",
        "Combining data models with processing logic"
      ]
    },
    "cohesion_coupling": {
      "high_cohesion": [
        "Group related functionality together",
        "Ensure tight intra-module collaboration"
      ],
      "low_coupling": [
        "Interact via interfaces (not direct dependencies)",
        "Use dependency injection",
        "Module communication via messages/events"
      ]
    },
    "code_simplification": {
      "examples": {
        "anti_pattern": "result = []\nfor i in range(10):\n    if i % 2 == 0:\n        result.append(i*2)",
        "best_practice": "result = (i*2 for i in range(10) if i % 2 == 0)"
      },
      "practices": [
        "Prefer built-in functions",
        "Use comprehensions over loops",
        "Apply decorators for code reuse"
      ]
    },
    "nesting_control": {
      "limits": {
        "max_depth": 2,
        "max_consecutive_if": 3
      },
      "optimization_examples": {
        "anti_pattern": "if cond1:\n    if cond2:\n        if cond3:\n            ...",
        "best_practice": "if not cond1: return\nif not cond2: return\n# Main logic (flat)"
      }
    },
    "supplementary_rules": {
      "type_annotations": {
        "requirement": "Mandatory for public interfaces",
        "example": "def process_data(data: list[dict]) -> pd.DataFrame: ..."
      },
      "exception_handling": {
        "rules": [
          "No bare 'except:'",
          "Catch specific exceptions",
          "Use context managers for resources"
        ],
        "example": "with open('file.txt') as f:\n    content = f.read()"
      },
      "unit_testing": {
        "min_coverage": 85,
        "method_requirement": "Test case per method",
        "aaa_pattern": true,
        "example": {
          "arrange": "input = [{\"value\": 5}, {\"value\": 10}]",
          "act": "result = process_data(input)",
          "assert": "assert result[\"sum\"] == 15"
        }
      },
      "performance_priority": [
        "Algorithm optimization (time complexity)",
        "Memory management (generators)",
        "Concurrency (asyncio/threadpools)",
        "JIT compilation (Numba/Cython)"
      ],
      "documentation": {
        "style": "google",
        "requirement": "Required for public APIs",
        "example": {
          "description": "Calculate dataset statistics",
          "args": "data: List of numerical values",
          "returns": "Dict with stats (min, max, avg)",
          "raises": "ValueError: On empty input"
        }
      }
    },
    "automation": {
      "pre_commit_tools": [
        {"tool": "black", "rev": "23.3.0"},
        {"tool": "flake8", "rev": "6.0.0"},
        {"tool": "darglint", "rev": "v1.8.1"}
      ]
    },
    "enforcement_priority": {
      "MUST": ["method_length", "single_responsibility", "type_annotations"],
      "SHOULD": ["nesting_control", "exception_handling"],
      "MAY": ["documentation", "performance"]
    },
    "core_philosophy": "Write code like prose: concise and clear, where every element has purpose. Enforce line/method limits to promote abstraction and decoupling."
  }
}